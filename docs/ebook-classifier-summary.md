# 电子书分类器功能总结

## 🎯 核心功能

电子书分类器是 Hydra 的一个子命令，实现了以下核心功能：

### 1. 智能扫描与分类
- **递归扫描**: 自动扫描指定目录中的电子书文件
- **格式支持**: 支持 PDF、EPUB、MOBI、AZW、TXT 等多种电子书格式
- **智能分类**: 使用 LLM 根据书本信息进行智能分类

### 2. 书本信息检索
- **豆瓣优先**: 优先使用豆瓣读书搜索中文书籍信息
- **Google Books**: 豆瓣搜索失败时使用 Google Books API
- **文件名解析**: 从文件名中提取书名和作者信息

### 3. 索引管理
- **避免重复**: 维护已处理文件的索引，避免重复处理
- **自动清理**: 定期清理过期索引条目（30天未访问）
- **统计信息**: 提供详细的处理统计和索引信息

### 4. 严格的错误处理
- **LLM连接检查**: API密钥未配置或连接失败时程序直接退出
- **分类失败跳过**: 无法确定分类的文件会被跳过，不会移动
- **详细日志**: 完整的错误日志和处理记录

## 📚 新分类体系

采用了层次化的分类体系，共25个分类：

### **一、人文类** (5个)
1. 人文·历史文化
2. 人文·历史文学
3. 人文·哲学与思维
4. 人文·宗教与灵修
5. 人文·旅行与地理

### **二、社会科学类** (8个)
6. 社会科学·政治制度
7. 社会科学·心理学与自我成长
8. 社会科学·社会纪实
9. 社会科学·人物传记
10. 社会科学·法律与社会规则
11. 社会科学·教育与学习方法
12. 社会科学·经济与金融
13. 社会科学·商业经管

### **三、文学艺术类** (8个)
14. 文学艺术·文学小说
15. 文学艺术·日本文学
16. 文学艺术·外国文学
17. 文学艺术·中国文学
18. 文学艺术·科幻小说
19. 文学艺术·网络文学
20. 文学艺术·诗歌戏剧
21. 文学艺术·艺术设计

### **四、自然科学类** (2个)
22. 自然科学·科学新知
23. 自然科学·医学与健康

### **五、应用学科类** (2个)
24. 应用学科·技术与编程
25. 应用学科·语言学习

## 🔧 技术特性

### API配置
- **默认LLM API**: `https://ark.cn-beijing.volces.com/api/v3/chat/completions`
- **默认模型**: `deepseek-v3-1-250821`
- **豆瓣搜索**: 自动处理反爬虫，设置合适的请求头

### 文件处理
- **安全文件名**: 自动清理不安全字符，生成唯一文件名
- **跨文件系统**: 支持跨文件系统的文件移动
- **试运行模式**: 支持预览模式，不实际移动文件

### 索引结构
```json
{
  "version": "1.0",
  "created_time": "2024-01-15T10:30:00Z",
  "updated_time": "2024-01-15T14:25:30Z",
  "entries": {
    "书名": {
      "original_path": "/source/书名.pdf",
      "target_path": "/target/分类/书名.pdf",
      "book_info": {...},
      "classification": {...},
      "processed_time": "2024-01-15T14:25:30Z"
    }
  },
  "stats": {
    "total_entries": 150,
    "category_counts": {...}
  }
}
```

## 🚀 使用示例

### 基本使用
```bash
hydra ebook-classify \
  -s /path/to/source \
  -t /path/to/target \
  --llm-api-key your-api-key
```

### 试运行模式
```bash
hydra ebook-classify \
  -s /path/to/source \
  -t /path/to/target \
  --dry-run
```

### 查看统计
```bash
hydra ebook-classify -t /path/to/target --show-stats
```

## 📊 预期输出

```
=== 电子书分类完成 ===
总文件数: 150
成功处理: 120
处理失败: 5
跳过处理: 25
从索引跳过: 50

本次分类统计:
  文学艺术·文学小说: 25 本
  应用学科·技术与编程: 18 本
  人文·历史文化: 12 本
  社会科学·商业经管: 15 本

=== 电子书索引统计 ===
总条目数: 170
今日处理: 120
本周处理: 170
```

## ⚠️ 重要注意事项

1. **必须配置LLM API密钥**，否则程序会直接退出
2. **分类失败的文件会被跳过**，不会移动到任何目录
3. **豆瓣搜索可能受限**，建议控制处理频率
4. **建议先使用试运行模式**验证配置和效果
5. **索引文件很重要**，包含所有处理历史，请妥善保管

## 🔄 工作流程

1. **扫描文件** → 递归扫描源目录
2. **检查索引** → 跳过已处理的文件（除非强制重新处理）
3. **检索信息** → 豆瓣 → Google Books → 文件名解析
4. **LLM分类** → 调用LLM API进行智能分类
5. **移动文件** → 根据分类结果移动到目标目录
6. **更新索引** → 记录处理结果，更新统计信息
7. **生成报告** → 输出处理结果和统计信息

## 📁 目录结构示例

```
/KindleBooks/
├── .ebook_index.json                    # 索引文件
├── 人文·历史文化/
│   ├── 史记.pdf
│   └── 全球通史.epub
├── 社会科学·心理学与自我成长/
│   ├── 心理学与生活.pdf
│   └── 思考快与慢.epub
├── 文学艺术·文学小说/
│   ├── 百年孤独.epub
│   └── 1984.pdf
├── 应用学科·技术与编程/
│   ├── 深入理解计算机系统.pdf
│   └── Python编程.epub
└── ebook_classify_errors_20240115_142530.log  # 错误日志
```

这个电子书分类器提供了完整的自动化书籍整理解决方案，结合了智能分类、索引管理和严格的错误处理，能够高效地组织大量电子书文件。
