# 电子书分类器使用指南

## 概述

电子书分类器是 Hydra 的一个子命令，用于自动扫描、分类和整理电子书文件。它使用 LLM（大语言模型）来智能分类书籍，并维护索引以避免重复处理。

## 功能特性

- **自动扫描**: 递归扫描指定目录中的电子书文件
- **智能分类**: 使用 LLM 根据书本信息进行智能分类
- **豆瓣优先**: 优先使用豆瓣搜索书本信息，然后才使用 Google Books
- **索引管理**: 维护已处理文件的索引，避免重复处理
- **严格模式**: LLM 无法连接时程序直接退出，分类失败时跳过文件
- **试运行模式**: 支持预览模式，不实际移动文件
- **统计报告**: 提供详细的处理统计和索引信息

## 支持的文件格式

- PDF (.pdf)
- EPUB (.epub)
- MOBI (.mobi)
- AZW (.azw, .azw3)
- TXT (.txt)
- DJVU (.djvu)
- FB2 (.fb2)
- LIT (.lit)
- PDB (.pdb)

## 默认分类

系统预定义了以下分类体系：

### **一、人文类**
- **人文·历史文化**: 历史文化类书籍，包括世界史、中国史、文化研究等
- **人文·历史文学**: 历史文学作品，历史小说、历史散文等
- **人文·哲学与思维**: 哲学、思维方法、逻辑学等
- **人文·宗教与灵修**: 宗教、灵修、禅学、佛学等
- **人文·旅行与地理**: 旅行指南、地理、文化探索类书籍

### **二、社会科学类**
- **社会科学·政治制度**: 政治学、制度研究、政策分析等
- **社会科学·心理学与自我成长**: 心理学、自我提升、个人成长等
- **社会科学·社会纪实**: 社会调查、纪实文学、社会问题研究等
- **社会科学·人物传记**: 人物传记、自传、回忆录等
- **社会科学·法律与社会规则**: 法律、社会规则、制度研究等
- **社会科学·教育与学习方法**: 教育学、学习方法、教学理论等
- **社会科学·经济与金融**: 经济学、金融、投资理财等
- **社会科学·商业经管**: 商业管理、企业经营、管理学等

### **三、文学艺术类**
- **文学艺术·文学小说**: 各类文学小说，包括现代小说、经典文学等
- **文学艺术·日本文学**: 日本文学作品，包括小说、散文等
- **文学艺术·外国文学**: 外国文学作品（除日本外），包括欧美文学等
- **文学艺术·中国文学**: 中国文学作品，包括古典文学、现代文学等
- **文学艺术·科幻小说**: 科幻小说、奇幻小说、未来主义文学等
- **文学艺术·网络文学**: 网络小说、轻小说等网络文学作品
- **文学艺术·诗歌戏剧**: 诗歌、戏剧、剧本等文学形式
- **文学艺术·艺术设计**: 艺术、设计、美学、创作理论等

### **四、自然科学类**
- **自然科学·科学新知**: 自然科学、数学、物理、化学、生物等科学知识
- **自然科学·医学与健康**: 医学、健康、养生、运动类书籍

### **五、应用学科类**
- **应用学科·技术与编程**: 编程、计算机科学、工程技术类书籍
- **应用学科·语言学习**: 语言学习、外语教材、语言学等

**注意**: 如果 LLM 无法确定合适的分类，文件将被跳过而不是归类到默认分类。

## 基本用法

### 1. 基本分类命令

```bash
hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key your-api-key
```

### 2. 试运行模式

预览将要执行的操作，不实际移动文件：

```bash
hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run
```

### 3. 强制重新处理

忽略索引，重新处理所有文件：

```bash
hydra ebook-classify -s /books/unsorted -t /books/sorted --force-reprocess
```

### 4. 显示索引统计

查看索引统计信息：

```bash
hydra ebook-classify -t /books/sorted --show-stats
```

## 参数说明

### 必需参数

- `-s, --source`: 源目录路径
- `-t, --target`: 目标分类目录路径

### 可选参数

- `--llm-api-key`: LLM API 密钥（必需，否则程序退出）
- `--llm-api-url`: LLM API 地址（默认: https://ark.cn-beijing.volces.com/api/v3/chat/completions）
- `--llm-model`: LLM 模型名称（默认: deepseek-v3-1-250821）
- `--dry-run`: 试运行模式，不实际移动文件
- `--force-reprocess`: 强制重新处理已索引的文件
- `--show-stats`: 显示索引统计信息后退出
- `-l, --logLevel`: 日志级别（info/debug）

## 环境变量

### Google Books API

可以设置 `GOOGLE_BOOKS_API_KEY` 环境变量来启用 Google Books API 进行书本信息检索：

```bash
export GOOGLE_BOOKS_API_KEY="your-google-books-api-key"
```

## 书本信息检索优先级

1. **豆瓣读书**: 优先使用豆瓣搜索中文书籍信息
2. **Google Books**: 豆瓣搜索失败时使用 Google Books API
3. **文件名解析**: 两者都失败时使用文件名提取的基本信息

## 索引功能

### 索引文件

索引文件保存在目标目录的 `.ebook_categories.json` 中，采用简化的KV结构：

```json
{
  "深入理解计算机系统": "应用学科·技术与编程",
  "三体": "文学艺术·科幻小说",
  "史记": "人文·历史文化",
  "经济学原理": "社会科学·经济与金融",
  "乔布斯传": "社会科学·人物传记"
}
```

### 索引特性

- **简化结构**: 仅保存文件名到分类的映射
- **跨源复用**: 相同文件名在不同source目录中可直接复用分类
- **永久保存**: 无过期机制，所有分类结果永久保留
- **高效查找**: O(1)时间复杂度的分类查找

## 错误处理

### 严格模式

- **LLM 连接失败**: 程序直接退出，不会继续处理
- **分类失败**: 跳过该文件，记录日志，不会移动到默认分类
- **文件移动失败**: 记录错误日志，继续处理其他文件

### 错误日志

处理过程中的错误会记录在目标目录的错误日志文件中，文件名格式为：
`ebook_classify_errors_YYYYMMDD_HHMMSS.log`

## 最佳实践

### 1. 首次使用

```bash
# 先试运行查看效果
hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run --llm-api-key your-key

# 确认无误后正式运行
hydra ebook-classify -s /books/unsorted -t /books/sorted --llm-api-key your-key
```

### 2. 定期整理

```bash
# 日常增量处理（利用索引跳过已处理文件）
hydra ebook-classify -s /books/new -t /books/sorted --llm-api-key your-key
```

### 3. 重新整理

```bash
# 重新分类所有文件
hydra ebook-classify -s /books/sorted -t /books/new_sorted --force-reprocess --llm-api-key your-key
```

## 示例输出

```
=== 电子书分类完成 ===
总文件数: 150
成功处理: 120
处理失败: 5
跳过处理: 25
从索引跳过: 50

本次分类统计:
  文学艺术·文学小说: 25 本
  应用学科·技术与编程: 18 本
  人文·历史文化: 12 本
  社会科学·商业经管: 15 本
  社会科学·心理学与自我成长: 10 本

=== 电子书索引统计 ===
索引版本: 1.0
创建时间: 2024-01-15 10:30:00
更新时间: 2024-01-15 14:25:30
总条目数: 170
今日处理: 120
本周处理: 170

分类统计:
  文学艺术·文学小说: 45 本
  应用学科·技术与编程: 32 本
  人文·历史文化: 25 本
  社会科学·商业经管: 28 本
  社会科学·心理学与自我成长: 18 本
  文学艺术·科幻小说: 12 本
  自然科学·科学新知: 10 本
```

## 注意事项

1. **必须配置 LLM API 密钥**，否则程序会直接退出
2. **分类失败的文件会被跳过**，不会移动到任何目录
3. **豆瓣搜索可能受到反爬虫限制**，建议适当控制处理频率
4. **建议先使用试运行模式**验证配置和效果
