# 电子书分类器使用指南

## 概述

电子书分类器是 Hydra 的一个子命令，用于自动扫描、分类和整理电子书文件。它使用 LLM（大语言模型）来智能分类书籍，并维护索引以避免重复处理。

## 功能特性

- **自动扫描**: 递归扫描指定目录中的电子书文件
- **智能分类**: 使用 LLM 根据书本信息进行智能分类
- **索引管理**: 维护已处理文件的索引，避免重复处理
- **错误处理**: 完善的错误处理和日志记录
- **试运行模式**: 支持预览模式，不实际移动文件
- **统计报告**: 提供详细的处理统计和索引信息

## 支持的文件格式

- PDF (.pdf)
- EPUB (.epub)
- MOBI (.mobi)
- AZW (.azw, .azw3)
- TXT (.txt)
- DJVU (.djvu)
- FB2 (.fb2)
- LIT (.lit)
- PDB (.pdb)

## 默认分类

系统预定义了以下分类：

- **小说**: 各类小说，包括科幻、奇幻、言情、悬疑、历史等
- **技术**: 编程、计算机科学、工程技术类书籍
- **商业**: 商业管理、经济学、投资理财类书籍
- **历史**: 历史类书籍，包括世界史、中国史等
- **哲学**: 哲学、思想、宗教类书籍
- **科学**: 自然科学、数学、物理、化学、生物等
- **艺术**: 艺术、设计、音乐、绘画等创作类书籍
- **传记**: 人物传记、自传、回忆录等
- **教育**: 教育学、心理学、儿童读物等
- **健康**: 医学、健康、养生、运动类书籍
- **旅行**: 旅行指南、地理、文化探索类书籍
- **其他**: 无法归类到以上类别的书籍

## 基本用法

### 1. 基本分类命令

```bash
hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key your-api-key
```

### 2. 启用默认移动

当分类失败时，将文件移动到"其他"分类：

```bash
hydra ebook-classify -s /books/unsorted -t /books/sorted --default-move
```

### 3. 试运行模式

预览将要执行的操作，不实际移动文件：

```bash
hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run
```

### 4. 强制重新处理

忽略索引，重新处理所有文件：

```bash
hydra ebook-classify -s /books/unsorted -t /books/sorted --force-reprocess
```

### 5. 显示索引统计

查看索引统计信息：

```bash
hydra ebook-classify -t /books/sorted --show-stats
```

## 参数说明

### 必需参数

- `-s, --source`: 源目录路径
- `-t, --target`: 目标分类目录路径

### 可选参数

- `--llm-api-key`: LLM API 密钥
- `--llm-api-url`: LLM API 地址（默认: OpenAI API）
- `--llm-model`: LLM 模型名称（默认: gpt-3.5-turbo）
- `-d, --default-move`: 分类失败时是否移动到默认目录
- `--dry-run`: 试运行模式，不实际移动文件
- `--force-reprocess`: 强制重新处理已索引的文件
- `--show-stats`: 显示索引统计信息后退出
- `-l, --logLevel`: 日志级别（info/debug）

## 环境变量

### Google Books API

可以设置 `GOOGLE_BOOKS_API_KEY` 环境变量来启用 Google Books API 进行书本信息检索：

```bash
export GOOGLE_BOOKS_API_KEY="your-google-books-api-key"
```

## 索引功能

### 索引文件

索引文件保存在目标目录的 `.ebook_index.json` 中，包含以下信息：

- 已处理文件的路径映射
- 书本信息和分类结果
- 处理时间和访问时间
- 统计信息

### 索引清理

系统会自动清理 30 天未访问的索引条目，以保持索引文件的精简。

## 错误处理

### 错误日志

处理过程中的错误会记录在目标目录的错误日志文件中，文件名格式为：
`ebook_classify_errors_YYYYMMDD_HHMMSS.log`

### 常见错误

1. **LLM API 调用失败**: 检查 API 密钥和网络连接
2. **文件移动失败**: 检查目录权限和磁盘空间
3. **书本信息检索失败**: 通常不影响分类，会使用文件名信息

## 最佳实践

### 1. 首次使用

```bash
# 先试运行查看效果
hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run --llm-api-key your-key

# 确认无误后正式运行
hydra ebook-classify -s /books/unsorted -t /books/sorted --llm-api-key your-key
```

### 2. 定期整理

```bash
# 日常增量处理（利用索引跳过已处理文件）
hydra ebook-classify -s /books/new -t /books/sorted --default-move
```

### 3. 重新整理

```bash
# 重新分类所有文件
hydra ebook-classify -s /books/sorted -t /books/new_sorted --force-reprocess
```

## 性能优化

- 使用索引功能避免重复处理
- 设置合适的 API 调用间隔
- 定期清理索引文件
- 使用试运行模式验证配置

## 故障排除

### 1. 构建问题

如果遇到 Go 版本兼容性问题，可以尝试：

```bash
go mod tidy
go build -o bin/hydra ./apps/hydra/cmd/
```

### 2. API 限制

如果遇到 API 调用限制，系统会自动添加延迟并重试。

### 3. 权限问题

确保对源目录有读权限，对目标目录有写权限。

## 示例输出

```
=== 电子书分类完成 ===
总文件数: 150
成功处理: 145
处理失败: 2
跳过处理: 3
从索引跳过: 50

本次分类统计:
  小说: 45 本
  技术: 32 本
  历史: 18 本
  其他: 50 本

=== 电子书索引统计 ===
索引版本: 1.0
创建时间: 2024-01-15 10:30:00
更新时间: 2024-01-15 14:25:30
总条目数: 195
今日处理: 145
本周处理: 195

分类统计:
  小说: 78 本
  技术: 45 本
  历史: 25 本
  商业: 15 本
  其他: 32 本
```
