# Tiver Configuration Guide

Tiver has been optimized with a new configuration management system that supports environment variables for better security and flexibility.

## 🚀 Quick Start

### 1. Check Current Configuration
```bash
make tiver-config-check
```

### 2. See Configuration Examples
```bash
make tiver-config-example
```

### 3. Setup for Development
```bash
make tiver-dev-setup
```

## ⚙️ Configuration Options

### Database Configuration

#### Option 1: Complete DSN (Recommended)
```bash
export TIVER_DB_DSN="user:password@tcp(host:port)/database?tls=tidb&parseTime=true&loc=Asia%2FShanghai"
```

#### Option 2: Individual Settings
```bash
export TIVER_DB_HOST="your-database-host"
export TIVER_DB_PORT=4000
export TIVER_DB_USERNAME="your_username"
export TIVER_DB_PASSWORD="your_password"
export TIVER_DB_NAME="tiver"
export TIVER_DB_TLS_NAME="tidb"
```

### HTTP Configuration

```bash
# Custom User-Agent string
export TIVER_HTTP_USER_AGENT="Custom Bot 1.0"

# HTTP timeout in seconds
export TIVER_HTTP_TIMEOUT=30

# Proxy configuration
export TIVER_HTTP_PROXY="http://proxy.example.com:8080"

# Authentication cookie
export TIVER_HTTP_COOKIE="session=abc123; auth=xyz789"
```

### Application Configuration

```bash
# Log level: debug, info, warn, error
export TIVER_LOG_LEVEL="info"

# Data directory for downloads and storage
export TIVER_DATA_DIR="./data"
```

## 🔧 Development Setup

### Using Environment File

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your configuration:
   ```bash
   nano .env
   ```

3. Source the environment file:
   ```bash
   source .env
   ```

4. Build and run:
   ```bash
   make tiver-dev-setup
   ./bin/tiver --help
   ```

### Using Export Commands

```bash
# Set up your environment
export TIVER_DB_DSN="your_database_dsn_here"
export TIVER_LOG_LEVEL="debug"
export TIVER_DATA_DIR="/tmp/tiver-dev"

# Build and run
make build-tiver
./bin/tiver download --help
./bin/tiver refresh --help
```

## 🛡️ Security Best Practices

### 1. Use Environment Variables
- Never hardcode sensitive information in code
- Use environment variables for all credentials
- Different configs for dev/staging/production

### 2. Secure Storage
- Store credentials in secure environment variable managers
- Use `.env` files for local development (add to `.gitignore`)
- Use container secrets or CI/CD variable systems in production

### 3. Database Security
- Use strong, unique passwords
- Enable TLS connections when available
- Limit database user permissions to minimum required

## 📝 Configuration Validation

The application will validate configuration on startup:

```bash
# This will show any configuration issues
./bin/tiver download --dry-run

# Check configuration without running commands
make tiver-config-check
```

## 🔄 Migration from Hardcoded Configuration

If you're upgrading from an older version with hardcoded configuration:

1. **Database**: Replace hardcoded connection strings with `TIVER_DB_DSN`
2. **Headers**: Use `TIVER_HTTP_*` variables instead of hardcoded values
3. **Paths**: Use `TIVER_DATA_DIR` for data storage location

### Legacy Support

For backward compatibility, if no environment variables are set, Tiver will use default values. However, this is not recommended for production use.

## 🚀 Production Deployment

### Docker Example
```dockerfile
FROM alpine:latest
COPY bin/tiver /usr/local/bin/tiver

ENV TIVER_DB_DSN="prod_connection_string"
ENV TIVER_LOG_LEVEL="warn"
ENV TIVER_DATA_DIR="/app/data"

ENTRYPOINT ["/usr/local/bin/tiver"]
```

### SystemD Service
```ini
[Unit]
Description=Tiver Service
After=network.target

[Service]
Type=simple
Environment=TIVER_DB_DSN=your_production_dsn
Environment=TIVER_LOG_LEVEL=info
Environment=TIVER_DATA_DIR=/var/lib/tiver
ExecStart=/usr/local/bin/tiver refresh
Restart=always

[Install]
WantedBy=multi-user.target
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `TIVER_DB_DSN` format
   - Verify network connectivity
   - Confirm credentials

2. **Permission Denied**
   - Check `TIVER_DATA_DIR` permissions
   - Ensure user has write access

3. **Configuration Not Applied**
   - Verify environment variables are exported
   - Check for typos in variable names
   - Use `make tiver-config-check` to verify

### Debug Mode

Enable debug logging for troubleshooting:
```bash
export TIVER_LOG_LEVEL="debug"
./bin/tiver your-command
```

## 📊 Configuration Precedence

Configuration is loaded in this order (later values override earlier ones):

1. Default values (in code)
2. Environment variables
3. Command-line flags (if applicable)

## 🔗 Related Commands

- `make tiver-config-check` - Check current configuration
- `make tiver-config-example` - Show example configuration
- `make tiver-dev-setup` - Setup development environment
- `make build-tiver` - Build Tiver binary
- `make test-tiver` - Run Tiver tests