# Tiver 架构优化计划

## 当前架构分析

### 目录结构
```
apps/tiver/
├── cmd/main.go              # 入口文件
├── internal/
│   ├── command/             # 命令处理
│   │   ├── download.go      # 下载命令
│   │   └── refresh.go       # 刷新命令
│   ├── common.go           # 通用配置(package名错误)
│   ├── const/              # 常量定义
│   ├── db/                 # 数据库连接
│   ├── model/              # 数据模型
│   ├── progress/           # 进度管理
│   ├── util/               # 工具函数
│   ├── image_download.go   # 图片下载核心逻辑
│   ├── thread_refresh.go   # 线程刷新核心逻辑
│   └── pdf/                # PDF生成
└── pkg/ (空)
```

## 主要架构问题

### 1. 包结构混乱
- **问题**: `internal/common.go` 包名声明为 `pkg`，但位置在 `internal/` 下
- **影响**: 违反 Go 包命名约定，造成混淆
- **建议**: 重新组织包结构，将通用代码移至合适位置

### 2. 硬编码配置严重
- **问题**: 
  - 数据库连接字符串硬编码在 `db.go:27`
  - Cookie 信息硬编码在 `common.go:20`
  - 域名和 User-Agent 硬编码
- **影响**: 难以配置管理，无法适应不同环境
- **安全风险**: 敏感信息暴露在代码中

### 3. 代码重复和耦合度高
- **问题**:
  - `download.go` 和 `refresh.go` 中重复的日志初始化和数据库初始化代码
  - Header 设置在多处重复定义
  - 业务逻辑与基础设施代码耦合
- **影响**: 维护困难，修改成本高

### 4. 错误处理不统一
- **问题**: 
  - 有些地方使用 `log.Logger.Fatal`，有些使用 `log.Logger.Error`
  - 错误处理策略不一致
- **影响**: 异常情况下行为不可预测

### 5. 并发安全问题
- **问题**:
  - `common.go:54` 全局变量 `saveErrorUrl` 没有并发保护
  - 多处使用全局变量
- **风险**: 可能导致数据竞争

### 6. 测试覆盖率低
- **问题**: 除了少数工具函数，核心业务逻辑缺乏测试
- **影响**: 代码质量无法保证，重构风险高

### 7. 资源管理不完善
- **问题**: 
  - 数据库连接没有连接池配置
  - HTTP 客户端配置分散
  - 文件操作后缺少资源释放检查

## 优化建议

### 阶段一：基础重构（高优先级）

#### 1.1 配置管理重构
- 创建统一的配置管理模块
- 支持环境变量、配置文件多种配置方式
- 移除所有硬编码配置

#### 1.2 包结构重组
```
apps/tiver/
├── cmd/main.go
├── internal/
│   ├── app/                 # 应用层
│   │   ├── command/         # 命令处理
│   │   └── service/         # 业务服务
│   ├── domain/              # 领域层
│   │   ├── model/           # 领域模型
│   │   └── repository/      # 仓储接口
│   ├── infrastructure/      # 基础设施层
│   │   ├── database/        # 数据库实现
│   │   ├── http/            # HTTP 客户端
│   │   └── config/          # 配置管理
│   └── pkg/                 # 内部共享包
│       ├── progress/        # 进度管理
│       └── util/            # 工具函数
└── pkg/                     # 对外暴露的包(如需要)
```

#### 1.3 依赖注入
- 使用依赖注入容器管理组件依赖
- 消除全局变量的使用
- 提高组件的可测试性

### 阶段二：功能优化（中优先级）

#### 2.1 并发模型优化
- 使用 Worker Pool 模式优化图片下载
- 实现更精细的并发控制
- 添加上下文取消机制

#### 2.2 错误处理标准化
- 定义统一的错误类型
- 实现错误重试机制
- 添加优雅的错误恢复

#### 2.3 监控和观测性
- 添加结构化日志
- 实现 Metrics 收集
- 添加分布式追踪支持

### 阶段三：架构升级（低优先级）

#### 3.1 微服务化准备
- 将刷新和下载功能拆分为独立服务
- 添加 API 接口支持
- 实现服务发现机制

#### 3.2 数据层优化
- 添加数据库迁移管理
- 实现读写分离
- 添加缓存层

#### 3.3 可扩展性增强
- 支持插件化扩展
- 添加配置热更新
- 实现水平扩展能力

## 实施计划

### Week 1-2: 配置管理重构
- [ ] 创建配置管理模块
- [ ] 移除硬编码配置
- [ ] 添加环境变量支持

### Week 3-4: 包结构重组
- [ ] 重新组织目录结构
- [ ] 修复包名问题
- [ ] 实现依赖注入

### Week 5-6: 并发安全修复
- [ ] 修复全局变量并发问题
- [ ] 优化 Worker Pool 实现
- [ ] 添加上下文管理

### Week 7-8: 错误处理优化
- [ ] 统一错误处理机制
- [ ] 添加重试逻辑
- [ ] 完善日志记录

### Week 9-10: 测试覆盖
- [ ] 添加单元测试
- [ ] 添加集成测试
- [ ] 实现测试覆盖率检查

## 技术债务清单

### 立即处理（Critical）
1. `internal/common.go` 包名错误修复
2. 数据库连接字符串外部化
3. 全局变量并发安全问题修复

### 近期处理（High）
1. 重复代码消除
2. 错误处理标准化
3. 资源管理完善

### 中期处理（Medium）
1. 测试覆盖率提升
2. 监控体系建设
3. 性能优化

### 长期处理（Low）
1. 架构微服务化
2. 可扩展性增强
3. 高可用设计

## 预期收益

### 开发效率提升
- 统一的配置管理减少环境配置问题
- 依赖注入提高代码可测试性
- 标准化错误处理减少调试时间

### 系统稳定性提升  
- 并发安全问题修复避免数据竞争
- 完善的错误处理提高容错能力
- 资源管理优化避免内存泄漏

### 维护成本降低
- 清晰的包结构便于理解和维护
- 消除重复代码减少修改点
- 完善的测试保证重构安全

## 风险评估

### 技术风险
- **中等**: 包结构重组可能影响现有功能
- **低**: 配置外部化实现相对简单
- **缓解措施**: 分阶段实施，保持向后兼容

### 业务风险
- **低**: 不涉及核心业务逻辑变更
- **缓解措施**: 充分测试，灰度发布

### 时间风险
- **中等**: 重构工作量较大
- **缓解措施**: 合理安排优先级，分批次实施