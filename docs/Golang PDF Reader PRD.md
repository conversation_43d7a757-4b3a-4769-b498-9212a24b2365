# 📘 产品需求文档（PRD）V1.0

## 一、项目名称

本地化PDF阅读系统（支持Golang后端 + React前端）

---

## 二、项目背景

针对本地PDF文件管理、轻量化阅读体验的需求，开发一套支持目录监听、分页加载、用户阅读记录、权限控制等功能的本地PDF阅读系统，适用于家庭用户、开发者个人文档库、小型团队内部知识库等场景。

---

## 三、产品目标

构建一个具备以下能力的本地PDF系统：

* 基于本地文件系统构建目录结构
* 高性能分布式分页加载PDF，避免浏览器卡死
* 多用户支持、权限可控
* 简单易部署，适合家庭/局域网内运行

---

## 四、功能需求

### 4.1 核心功能

| 功能模块  | 描述                               |
| ----- | -------------------------------- |
| PDF监听 | 后端启动时扫描配置目录，实时识别PDF文件变化          |
| PDF上传 | 页面上传PDF，保存至指定本地目录                |
| PDF阅读 | 每次只加载一页PDF，前端翻页时按需请求下一页          |
| 目录浏览  | 前端支持以“文件夹 + PDF”结构展示             |
| 元数据提取 | 提取并展示PDF的标题、作者、页数、大小等信息          |
| 自定义存储 | 后端支持配置本地MetaStorage数据库路径和PDF保存路径 |

---

### 4.2 用户与权限

| 功能模块  | 描述             |
| ----- | -------------- |
| 多用户登录 | 支持用户注册、登录（JWT） |
| 用户隔离  | 不同用户PDF空间隔离    |
| 权限控制  | 上传、删除权限按角色控制   |

---

### 4.3 阅读记录管理

| 功能模块 | 描述              |
| ---- | --------------- |
| 阅读记录 | 保存用户最近阅读的PDF及页码 |
| 继续阅读 | 登录后可直接跳转到上次阅读位置 |

---

### 4.4 搜索与过滤

| 功能模块 | 描述                             |
| ---- | ------------------------------ |
| 文件过滤 | 按文件名、目录、上传时间等筛选PDF             |
| 全文搜索 | 可选功能：支持PDF全文搜索（基于Tika + Bleve） |

---

### 4.5 文件操作

| 功能模块 | 描述                  |
| ---- | ------------------- |
| 重命名  | 支持PDF重命名            |
| 删除   | 用户有权限时，可删除PDF       |
| 移动   | 将PDF移动到其他目录         |
| 属性展示 | 显示文件大小、上传时间、页数等基本信息 |

---

### 4.6 系统配置与日志

| 功能模块 | 描述                         |
| ---- | -------------------------- |
| 配置文件 | 后端支持读取 `config.yaml` 配置参数  |
| 日志记录 | 操作日志（上传、阅读、删除等），错误日志分级输出   |
| 可部署性 | 仅依赖本地目录与数据库，部署简单、支持Docker化 |

---

## 五、数据库设计（SQLite3）

### 5.1 表结构概览

| 表名                | 描述        |
| ----------------- | --------- |
| `users`           | 用户信息      |
| `pdf_files`       | PDF文件及元数据 |
| `reading_history` | 用户阅读记录    |
| `log_events`      | 系统日志记录    |

（详见[表结构定义节](#数据库结构详解)）

---

## 六、配置文件示例（`config.yaml`）

```yaml
watch_dir: "/data/pdf-library"
upload_dir: "/data/pdf-uploads"
meta_db_path: "/data/pdf-meta.db"
enable_login: true
log_level: "info"
```

---

## 七、数据库结构详解

### 7.1 `users`

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 7.2 `pdf_files`

```sql
CREATE TABLE pdf_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    folder_path TEXT,
    author TEXT,
    title TEXT,
    page_count INTEGER,
    size_bytes INTEGER,
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 7.3 `reading_history`

```sql
CREATE TABLE reading_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    pdf_id INTEGER,
    last_read_page INTEGER,
    last_read_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (pdf_id) REFERENCES pdf_files(id)
);
```

### 7.4 `log_events`

```sql
CREATE TABLE log_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    event_type TEXT,
    file_path TEXT,
    page_number INTEGER,
    event_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    message TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

---

## 八、非功能性需求

| 项目    | 描述                      |
| ----- | ----------------------- |
| 响应性能  | 加载一页PDF时间 < 500ms（本地环境） |
| 并发支持  | 支持10+用户并发阅读             |
| 安全性   | 文件校验、路径隔离、防注入攻击         |
| 可维护性  | 模块化设计，前后端代码分离           |
| 部署便利性 | 可打包为Docker镜像            |
