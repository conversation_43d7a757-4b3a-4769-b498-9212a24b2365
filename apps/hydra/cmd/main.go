package main

import (
	"context"
	"fmt"

	inner "github.com/penwyp/hydra/apps/hydra/internal"
	"github.com/penwyp/hydra/apps/hydra/internal/command"
	"github.com/spf13/cobra"
)

var rootCommand = &cobra.Command{
	Use:   "hydra",
	Short: "Hydra: A multi-purpose CLI for system automation",
	Long: `Hydra is a powerful command-line tool designed to streamline file management, data processing, and system operations.
It provides a collection of utilities including file transfers, data tidying, searching, index building, and more.

Available commands:
  - transfer: Manage file transfers
  - tidy: Organize and clean up data
  - search: Search through files and indexes
  - hlink: Handle links or hardlinks
  - buildindex: Create and manage indexes
  - ebook-classify: Scan and classify ebooks using LLM

Run 'hydra [command] --help' for more information on a specific command.`,
	PostRun: postRunFunc,
	Run:     runFunc,
}

func init() {
	rootCommand.Flags().BoolP("version", "v", false, "Display build time and version")
}

// main 执行真正业务逻辑
func main() {
	addCommandWithPostRunFunc(rootCommand,
		command.NewQBCmd(),
		command.NewTransferCommand(),
		command.NewTidyCommand(),
		command.NewSearchCommand(),
		command.NewHlinkCommand(),
		command.NewAfterTransferCommand(),
		command.NewRemoveCommand(),
		command.NewBuildIndexCommand(),
		command.NewCGCommand(),
		command.NewPtppCommand(),
		command.NewSubscribeCommand(),
		command.NewSystemctlCommand(),
		command.NewHookCommand(),
		command.NewChownCommand(),
		command.NewDDNSCommand(),
		command.NewPruneHistoryCommand(),
		command.NewEbookClassifierCommand(),
	)
	cobra.CheckErr(rootCommand.ExecuteContext(context.Background()))
}

var postRunFunc = func(cmd *cobra.Command, args []string) {
}

var runFunc = func(cmd *cobra.Command, args []string) {
	// 获取 -v 标志的值
	version, _ := cmd.Flags().GetBool("version")
	if version {
		fmt.Printf("Version: %s\nBuild Time: %s\n", inner.GetGitVersion(), inner.GetBuildTime())
		return
	}
	// 如果没有 -v，显示默认帮助信息
	cmd.Help()
}

func addCommandWithPostRunFunc(root *cobra.Command, cmdArr ...*cobra.Command) {
	for _, cmd := range cmdArr {
		cmd.PostRun = postRunFunc
		root.AddCommand(cmd)
	}
}
