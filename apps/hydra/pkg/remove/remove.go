package remove

import (
	"context"
	"github.com/penwyp/hydra/apps/hydra/internal/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/pkg/index"
	"github.com/penwyp/hydra/shared/log"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strings"
)

func RemoveFiles(ro RemoveOption) {
	rdb := cache.OpenCacheDB(constants.RedisAddr)

	// 记录输入参数
	log.Logger.Info("Starting file operation",
		zap.Bool("remove_enabled", ro.CanRemove),
		zap.Strings("paths", ro.Paths))

	// 搜索阶段
	action := "dry-run"
	if ro.CanRemove {
		action = "deletion"
	}
	log.Logger.Info("Searching files and directories for " + action)

	// 从索引数据库搜索文件
	searchResult := index.SearchFromIndexDB(context.Background(), rdb, ro.Paths)
	files := searchResult.GetFiles()
	totalFiles := len(files)

	// 开始处理文件
	log.Logger.Info("Processing files",
		zap.String("action", action),
		zap.Int("total_files", totalFiles))

	// 处理文件
	toBeClearDirs := make([]string, 0, totalFiles)
	for i, file := range files {
		filePath := file.FilePath
		toBeClearDirs = append(toBeClearDirs, path.Dir(filePath))

		if ro.CanRemove {
			if err := os.RemoveAll(filePath); err != nil {
				log.Logger.Error("Failed to delete file",
					zap.String("path", filePath),
					zap.Error(err))
				continue
			}
			log.Logger.Info("Deleted file",
				zap.String("path", filePath))

			// 进度日志（每10个文件）
			if (i+1)%10 == 0 {
				log.Logger.Info("Deletion progress",
					zap.Int("processed", i+1),
					zap.Int("total", totalFiles))
			}
		} else {
			log.Logger.Info("Found file",
				zap.String("path", filePath))
		}
	}

	// 处理完成总结
	if totalFiles > 0 {
		log.Logger.Info("File operation completed",
			zap.String("action", action),
			zap.Int("total_files", totalFiles))
	} else {
		log.Logger.Warn("No files processed",
			zap.String("action", action))
	}

	// 处理目录
	toBeClearDirs = appendUpperLevelDirs(toBeClearDirs)
	toBeClearDirs = distinctAndSortDirs(toBeClearDirs)

	for i, dir := range toBeClearDirs {
		log.Logger.Info("Processing directory",
			zap.String("action", action),
			zap.Int("index", i+1),
			zap.Int("total_dirs", len(toBeClearDirs)),
			zap.String("path", dir))

		RemoveDirs(RemoveOption{
			CanRemove:        ro.CanRemove,
			DirSizeThreshold: ro.DirSizeThreshold,
			Paths:            []string{dir},
		})
	}

	log.Logger.Info("Directory operation completed",
		zap.String("action", action),
		zap.Int("total_dirs", len(toBeClearDirs)))
}

func appendUpperLevelDirs(dirs []string) []string {
	arr := []string{}
	for _, dir := range dirs {
		arr = append(arr, dir, path.Dir(dir))
	}
	return lo.Uniq(arr)
}

func distinctAndSortDirs(toBeClearDirs []string) []string {
	toBeClearDirs = lo.Uniq(toBeClearDirs)

	return sortPaths(toBeClearDirs)
}

func IsEmptyDirs(dir string) (bool, error) {
	dirFiles, err := os.ReadDir(dir)
	if err != nil {
		return false, err
	}
	if len(dirFiles) != 0 {
		return false, nil
	}
	return true, nil
}

type RemoveOption struct {
	CanRemove        bool
	DirSizeThreshold int64
	Paths            []string
}

// RemoveDirs removes empty directories based on RemoveOption and returns the count of empty dirs.
func RemoveDirs(ro RemoveOption) int {
	emptyDirs := make([]string, 0, 10)      // 预分配初始容量
	walked := make(map[string]struct{}, 50) // 使用 struct{} 节省内存，预分配容量

	// 遍历所有路径
	for _, removePath := range ro.Paths {
		err := filepath.Walk(removePath, func(filePath string, info os.FileInfo, err error) error {
			if _, exists := walked[filePath]; exists {
				return nil
			}
			walked[filePath] = struct{}{}

			if err != nil {
				return err
			}
			// 跳过非目录或特殊目录
			if !info.IsDir() || info.Name() == "." || info.Name() == ".." {
				return nil
			}

			isEmpty, err := IsEmptyDirs(filePath)
			if err != nil {
				return err
			}
			if isEmpty {
				log.Logger.Info("Found empty directory",
					zap.String("path", filePath),
					zap.Int64("size", info.Size()))
				emptyDirs = append(emptyDirs, filePath)
			}
			return nil
		})
		if err != nil {
			log.Logger.Debug("Failed to walk files", zap.Error(err))
		}
	}

	// 执行删除操作
	if ro.CanRemove && len(emptyDirs) > 0 {
		emptyDirs = sortPaths(emptyDirs)
		for _, dir := range emptyDirs {
			if err := os.RemoveAll(dir); err != nil {
				log.Logger.Fatal("Failed to remove directory",
					zap.String("path", dir),
					zap.Error(err))
			}
			log.Logger.Info("Removed directory successfully", zap.String("path", dir))
		}
	}

	return len(emptyDirs)
}

// 按照要求排序目录的函数
func sortPaths(paths []string) []string {
	// 自定义排序函数
	sort.Slice(paths, func(i, j int) bool {
		// 比较路径长度
		if len(paths[i]) != len(paths[j]) {
			return len(paths[i]) > len(paths[j])
		}
		// 如果路径长度相同，比较层级关系
		return strings.Count(paths[i], "/") > strings.Count(paths[j], "/")
	})

	return paths
}
