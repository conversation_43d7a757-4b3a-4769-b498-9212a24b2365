package remove

import (
	"reflect"
	"testing"
)

func Test_distinctAndSortDirs(t *testing.T) {
	type args struct {
		toBeClearDirs []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			args: args{
				toBeClearDirs: []string{
					"/a/b/ccc",
					"/a/b/ccc/dd",
					"/a/b/z",
					"/a/b/dd/p",
					"/a/b/dd",
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := distinctAndSortDirs(tt.args.toBeClearDirs); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("distinctAndSortDirs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sortPaths(t *testing.T) {
	type args struct {
		paths []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			args: args{
				paths: []string{
					"/home/<USER>/docs",
					"/home/<USER>",
					"/home/<USER>/docs/reports",
					"/var/log",
					"/var/log/nginx",
					"/var",
				},
			},
			want: []string{
				"/home/<USER>/docs/reports",
				"/home/<USER>/docs",
				"/var/log/nginx",
				"/home/<USER>",
				"/var/log",
				"/var",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := sortPaths(tt.args.paths); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("sortPaths() = %v, want %v", got, tt.want)
			}
		})
	}
}
