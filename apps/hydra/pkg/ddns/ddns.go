package ddns

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	_ "github.com/moby/moby/api/types"
	"github.com/moby/moby/client"
	"github.com/penwyp/hydra/shared/log"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	dnspod "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/dnspod/v20210323"
	"go.uber.org/zap"
)

var (
	// Container and domain mappings
	mappings = []ContainerDomainMapping{
		{
			ContainerName: "emby",
			Domain:        "em6.hydra.cab",
		},
		{
			ContainerName: "embyx",
			Domain:        "emx6.hydra.cab",
		},
	}
	// Tencent Cloud DNSPod configuration
	dnsConfig = DNSPodConfig{
		SecretId:  "AKIDWVM1g2C9sOmy331vrgJn5A2jFfsuoU6o",
		SecretKey: "pbFj6II6nJdmkXEj3BB5pOn02vc9ZGqI",
		Region:    "ap-guangzhou",
	}
)

// ContainerDomainMapping defines the mapping between a container and a domain
type ContainerDomainMapping struct {
	ContainerName string
	Domain        string
	SubDomain     string
}

// DNSPodConfig holds the configuration for Tencent Cloud DNSPod
type DNSPodConfig struct {
	SecretId  string
	SecretKey string
	Region    string
}

// getContainerIPv6 retrieves the IPv6 address of the specified Docker container
func getContainerIPv6(cli *client.Client, containerName string) (string, error) {
	log.Logger.Debug("Starting to retrieve IPv6 address for container", zap.String("container", containerName))

	// Inspect the container
	container, err := cli.ContainerInspect(context.Background(), containerName)
	if err != nil {
		log.Logger.Error("Failed to inspect container", zap.String("container", containerName), zap.Error(err))
		return "", fmt.Errorf("failed to inspect container %s: %v", containerName, err)
	}

	// Log container network settings
	log.Logger.Debug("Container network settings", zap.Any("networkSettings", container.NetworkSettings.Networks))

	// Check network settings for IPv6 address
	for networkName, network := range container.NetworkSettings.Networks {
		log.Logger.Debug("Checking network",
			zap.String("networkName", networkName),
			zap.String("IPv6Gateway", network.IPv6Gateway),
			zap.String("GlobalIPv6Address", network.GlobalIPv6Address))
		if network.IPv6Gateway != "" && network.GlobalIPv6Address != "" {
			log.Logger.Info("Successfully retrieved IPv6 address for container",
				zap.String("container", containerName),
				zap.String("ipv6Addr", network.GlobalIPv6Address))
			return network.GlobalIPv6Address, nil
		}
	}

	log.Logger.Warn("No IPv6 address found for container", zap.String("container", containerName))
	return "", fmt.Errorf("no IPv6 address found for container %s", containerName)
}

// createDNSPodClient creates a DNSPod client with the given configuration
func createDNSPodClient(config DNSPodConfig) (*dnspod.Client, error) {
	log.Logger.Debug("Creating DNSPod client", zap.String("region", config.Region))

	credential := common.NewCredential(config.SecretId, config.SecretKey)
	log.Logger.Debug("Created Tencent Cloud credential", zap.String("secretId", config.SecretId))

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "dnspod.tencentcloudapi.com"
	log.Logger.Debug("Set DNSPod client endpoint", zap.String("endpoint", cpf.HttpProfile.Endpoint))

	client, err := dnspod.NewClient(credential, config.Region, cpf)
	if err != nil {
		log.Logger.Error("Failed to create DNSPod client", zap.Error(err))
		return nil, fmt.Errorf("failed to create DNSPod client: %v", err)
	}

	log.Logger.Debug("Successfully created DNSPod client", zap.String("region", config.Region))
	return client, nil
}

// findExistingRecords queries DNSPod for existing AAAA records for both @ and www
func findExistingRecords(client *dnspod.Client, domain string) (map[string][]struct {
	RecordId *uint64
	Value    string
}, error) {
	log.Logger.Debug("Querying DNSPod for existing AAAA records",
		zap.String("domain", domain))

	describeRequest := dnspod.NewDescribeRecordListRequest()
	describeRequest.Domain = common.StringPtr(domain)
	describeRequest.Subdomain = common.StringPtr("") // Query all records under the domain
	describeRequest.RecordType = common.StringPtr("AAAA")
	log.Logger.Debug("Query DNSPod record request", zap.Any("request", describeRequest))

	describeResponse, err := client.DescribeRecordList(describeRequest)
	if err != nil {
		// Handle "ResourceNotFound.NoDataOfRecord" as a normal case (record does not exist)
		if strings.Contains(err.Error(), "ResourceNotFound.NoDataOfRecord") {
			log.Logger.Info("No existing AAAA records found",
				zap.String("domain", domain),
				zap.Error(err))
			return nil, nil
		}
		log.Logger.Error("Failed to query DNSPod records",
			zap.Any("request", describeRequest),
			zap.Error(err))
		return nil, fmt.Errorf("failed to query DNSPod records: %v", err)
	}

	log.Logger.Debug("Successfully queried DNSPod records", zap.Any("response", describeResponse.Response))

	// Group records by Name (@ and www)
	records := make(map[string][]struct {
		RecordId *uint64
		Value    string
	})
	for _, record := range describeResponse.Response.RecordList {
		log.Logger.Debug("Checking record",
			zap.String("recordType", *record.Type),
			zap.String("recordName", *record.Name),
			zap.Uint64("recordId", *record.RecordId),
			zap.String("recordValue", *record.Value))
		if *record.Type == "AAAA" && (*record.Name == "@" || *record.Name == "www") {
			records[*record.Name] = append(records[*record.Name], struct {
				RecordId *uint64
				Value    string
			}{record.RecordId, *record.Value})
		}
	}

	// Log found records
	for name, recs := range records {
		log.Logger.Info("Found existing AAAA records",
			zap.String("domain", domain),
			zap.String("name", name),
			zap.Int("recordCount", len(recs)))
	}

	return records, nil
}

// deleteDNSRecord deletes an existing AAAA record in DNSPod
func deleteDNSRecord(client *dnspod.Client, domain, name string, recordId *uint64) error {
	log.Logger.Debug("Preparing to delete AAAA record",
		zap.String("domain", domain),
		zap.String("name", name),
		zap.Uint64("recordId", *recordId))

	deleteRequest := dnspod.NewDeleteRecordRequest()
	deleteRequest.Domain = common.StringPtr(domain)
	deleteRequest.RecordId = recordId
	log.Logger.Debug("Delete AAAA record request", zap.Any("request", deleteRequest))

	deleteResponse, err := client.DeleteRecord(deleteRequest)
	if err != nil {
		log.Logger.Error("Failed to delete AAAA record",
			zap.Any("request", deleteRequest),
			zap.Error(err))
		return fmt.Errorf("failed to delete AAAA record: %v", err)
	}

	log.Logger.Info("Successfully deleted AAAA record",
		zap.String("domain", domain),
		zap.String("name", name),
		zap.Uint64("recordId", *recordId),
		zap.Any("response", deleteResponse.Response))
	return nil
}

// createDNSRecord creates a new AAAA record in DNSPod for both @ and www
func createDNSRecord(client *dnspod.Client, domain, ipv6Addr string) error {
	names := []string{"@", "www"}
	for _, name := range names {
		log.Logger.Debug("Preparing to create AAAA record",
			zap.String("domain", domain),
			zap.String("name", name),
			zap.String("ipv6Addr", ipv6Addr))

		createRequest := dnspod.NewCreateRecordRequest()
		createRequest.Domain = common.StringPtr(domain)
		createRequest.SubDomain = common.StringPtr(name)
		createRequest.RecordType = common.StringPtr("AAAA")
		createRequest.RecordLine = common.StringPtr("默认") // Use "默认" to avoid load balancing
		createRequest.Value = common.StringPtr(ipv6Addr)
		createRequest.TTL = common.Uint64Ptr(600)
		log.Logger.Debug("Create AAAA record request", zap.Any("request", createRequest))

		createResponse, err := client.CreateRecord(createRequest)
		if err != nil {
			// Handle "InvalidParameter.DomainRecordExist" as a normal case (record already exists)
			if strings.Contains(err.Error(), "InvalidParameter.DomainRecordExist") {
				log.Logger.Warn("Failed to create AAAA record because it already exists",
					zap.String("domain", domain),
					zap.String("name", name),
					zap.String("ipv6Addr", ipv6Addr),
					zap.Error(err))
				continue // Skip to the next name
			}
			// Handle "LimitExceeded.SubdomainRollLimit" error
			if strings.Contains(err.Error(), "LimitExceeded.SubdomainRollLimit") {
				log.Logger.Error("Failed to create AAAA record due to subdomain load balancing limit",
					zap.String("domain", domain),
					zap.String("name", name),
					zap.String("ipv6Addr", ipv6Addr),
					zap.Error(err))
				return fmt.Errorf("failed to create AAAA record due to subdomain load balancing limit: %v", err)
			}
			log.Logger.Error("Failed to create AAAA record",
				zap.Any("request", createRequest),
				zap.Error(err))
			return fmt.Errorf("failed to create AAAA record: %v", err)
		}

		log.Logger.Info("Successfully created AAAA record",
			zap.String("domain", domain),
			zap.String("name", name),
			zap.String("ipv6Addr", ipv6Addr),
			zap.Any("response", createResponse.Response))
	}
	return nil
}

// updateDNSPodRecord updates the AAAA records in DNSPod for both @ and www
func updateDNSPodRecord(config DNSPodConfig, domain, ipv6Addr string) error {
	log.Logger.Debug("Starting to update DNSPod record",
		zap.String("domain", domain),
		zap.String("ipv6Addr", ipv6Addr))

	// Validate IPv6 address
	if net.ParseIP(ipv6Addr) == nil || net.ParseIP(ipv6Addr).To4() != nil {
		log.Logger.Error("Invalid IPv6 address", zap.String("ipv6Addr", ipv6Addr))
		return fmt.Errorf("invalid IPv6 address: %s", ipv6Addr)
	}

	// Create DNSPod client
	client, err := createDNSPodClient(config)
	if err != nil {
		return err
	}

	// Query existing records
	existingRecords, err := findExistingRecords(client, domain)
	if err != nil {
		return err
	}

	// Check if records for @ and www exist and are up-to-date
	names := []string{"@", "www"}
	allRecordsMatch := true
	for _, name := range names {
		records, exists := existingRecords[name]
		if !exists || len(records) == 0 {
			allRecordsMatch = false
			break
		}
		// Check if all records for this name match the desired IPv6 address
		for _, record := range records {
			if record.Value != ipv6Addr {
				allRecordsMatch = false
				break
			}
		}
		if !allRecordsMatch {
			break
		}
	}

	// Skip update if all records exist and match
	if allRecordsMatch && len(existingRecords["@"]) > 0 && len(existingRecords["www"]) > 0 {
		log.Logger.Info("All AAAA records already exist and are the same, skipping update",
			zap.String("domain", domain),
			zap.String("ipv6Addr", ipv6Addr))
		return nil
	}

	// Delete all existing records for @ and www
	for name, records := range existingRecords {
		if name == "@" || name == "www" {
			for _, record := range records {
				log.Logger.Info("Deleting existing AAAA record",
					zap.String("domain", domain),
					zap.String("name", name),
					zap.String("existingIPv6Addr", record.Value),
					zap.Uint64("recordId", *record.RecordId))
				err = deleteDNSRecord(client, domain, name, record.RecordId)
				if err != nil {
					return err
				}
			}
		}
	}

	// Create new records for @ and www
	return createDNSRecord(client, domain, ipv6Addr)
}

// Update periodically updates DNS records
func Update() {
	log.Logger.Info("Starting DNS update task")

	log.Logger.Debug("DNSPod configuration",
		zap.String("secretId", dnsConfig.SecretId),
		zap.String("region", dnsConfig.Region))

	log.Logger.Debug("Container and domain mappings", zap.Any("mappings", mappings))

	// Create Docker client
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		log.Logger.Fatal("Failed to create Docker client", zap.Error(err))
	}
	defer cli.Close()
	log.Logger.Info("Successfully created Docker client")

	// Schedule periodic task: check every 5 minutes
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	log.Logger.Info("Scheduled periodic task", zap.Duration("interval", 5*time.Minute))

	for {
		log.Logger.Debug("Starting a new round of DNS updates")
		for _, mapping := range mappings {
			// Get container IPv6 address
			ipv6Addr, err := getContainerIPv6(cli, mapping.ContainerName)
			if err != nil {
				log.Logger.Warn("Failed to retrieve container IPv6 address",
					zap.String("container", mapping.ContainerName),
					zap.Error(err))
				continue
			}

			// Update DNSPod record
			err = updateDNSPodRecord(dnsConfig, mapping.Domain, ipv6Addr)
			if err != nil {
				log.Logger.Warn("Failed to update DNS record",
					zap.String("domain", mapping.Domain),
					zap.Error(err))
			}
		}

		// Wait for the next check
		log.Logger.Debug("Waiting for the next check")
		<-ticker.C
	}
}
