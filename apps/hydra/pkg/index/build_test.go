package index

import (
	"reflect"
	"testing"
)

func Test_extractUpperFolders(t *testing.T) {
	type args struct {
		filePath  string
		topFolder string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "a",
			args: args{
				filePath:  "/a/b/c/d/e/f/g.txt",
				topFolder: "/a",
			},
			want: []string{
				"/a",
				"/a/b",
				"/a/b/c",
				"/a/b/c/d",
				"/a/b/c/d/e",
				"/a/b/c/d/e/f",
			},
		},
		{
			name: "b",
			args: args{
				filePath:  "/a/b/c/d/e/f/g.txt",
				topFolder: "/a/b/c/d/e/f",
			},
			want: []string{
				"/a/b/c/d/e/f",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := extractUpperFolders(tt.args.filePath, tt.args.topFolder); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>rf("extractUpperFolders() = %v, want %v", got, tt.want)
			}
		})
	}
}
