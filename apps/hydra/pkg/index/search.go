package index

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"github.com/penwyp/hydra/apps/hydra/internal/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/json"
	"github.com/penwyp/hydra/shared/log"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"os"
)

// SearchResult holds search results for files and directories
type SearchResult struct {
	files map[string]*SearchedFile
	dirs  map[string]*SearchedFile
}

// NewSearchResult initializes a new SearchResult
func NewSearchResult() *SearchResult {
	return &SearchResult{
		files: make(map[string]*SearchedFile),
		dirs:  make(map[string]*SearchedFile),
	}
}

// GetFiles returns sorted file results
func (r *SearchResult) GetFiles() []*SearchedFile {
	return r.getSortedEntries(r.files)
}

// GetDirs returns sorted directory results
func (r *SearchResult) GetDirs() []*SearchedFile {
	return r.getSortedEntries(r.dirs)
}

// getSortedEntries extracts and sorts entries from a map
func (r *SearchResult) getSortedEntries(m map[string]*SearchedFile) []*SearchedFile {
	entries := make([]*SearchedFile, 0, len(m))
	for _, entry := range m {
		entries = append(entries, entry)
	}
	sort.Slice(entries, func(i, j int) bool {
		return entries[i].FilePath < entries[j].FilePath
	})
	return entries
}

// appendEntry adds or updates an entry in the specified map
func (r *SearchResult) appendEntry(m map[string]*SearchedFile, path string, hitByName bool) {
	if _, exists := m[path]; !exists {
		m[path] = &SearchedFile{FilePath: path}
	}
	if hitByName {
		m[path].HitByName = true
	} else {
		m[path].HitByTree = true
	}
}

// AppendDirByName adds a directory matched by name
func (r *SearchResult) AppendDirByName(dirPath string) {
	r.appendEntry(r.dirs, dirPath, true)
}

// AppendDirByTree adds a directory matched by tree
func (r *SearchResult) AppendDirByTree(dirPath string) {
	r.appendEntry(r.dirs, dirPath, false)
}

// AppendFileByName adds a file matched by name
func (r *SearchResult) AppendFileByName(filePath string) {
	r.appendEntry(r.files, filePath, true)
}

// AppendFileByTree adds a file matched by tree
func (r *SearchResult) AppendFileByTree(filePath string) {
	r.appendEntry(r.files, filePath, false)
}

// SearchedFile represents a searched file or directory
type SearchedFile struct {
	FilePath  string
	HitByName bool
	HitByTree bool
}

func (f *SearchedFile) String() string {
	return f.FilePath
}

// BuildSearchResult constructs a SearchResult from search inputs
func BuildSearchResult(searchedFiles, searchedDirs, searchedTrees []string) *SearchResult {
	result := NewSearchResult()

	for _, path := range searchedFiles {
		result.appendPath(path, true, true)
	}

	for _, dir := range searchedDirs {
		result.AppendDirByName(dir)
	}

	for _, path := range searchedTrees {
		result.appendPath(path, false, false)
	}

	return result
}

// appendPath adds a path to the result, determining if it's a file or directory
func (r *SearchResult) appendPath(path string, byName, isFile bool) {
	if info, err := os.Stat(path); err != nil {
		log.Logger.Debug("Failed to stat path", zap.String("path", path), zap.Error(err))
		if isFile {
			r.AppendFileByName(path)
		} else {
			r.AppendFileByTree(path)
		}
	} else if info.IsDir() {
		if byName {
			r.AppendDirByName(path)
		} else {
			r.AppendDirByTree(path)
		}
	} else {
		if byName {
			r.AppendFileByName(path)
		} else {
			r.AppendFileByTree(path)
		}
	}
}

// SearchFromIndexDB performs a search across the index database
func SearchFromIndexDB(ctx context.Context, cacheDB cache.Cache, queryTokens []string) *SearchResult {
	files := searchMatchedFiles(ctx, cacheDB, queryTokens)
	dirs := searchMatchedDirs(ctx, cacheDB, queryTokens)
	treeFiles := searchMatchedTrees(ctx, cacheDB, append(files, dirs...))
	return BuildSearchResult(files, dirs, treeFiles)
}

// GetIndexDBVersion retrieves the current index database version
func GetIndexDBVersion(ctx context.Context, cacheDB cache.Cache) string {
	version, err := cacheDB.Get(ctx, constants.VersionKey)
	if err != nil {
		log.Logger.Error("Failed to get index version", zap.Error(err))
		return ""
	}
	log.Logger.Info("Current index version", zap.String("version", version))
	return version
}

// searchMatchedFiles finds files matching query tokens
func searchMatchedFiles(ctx context.Context, cacheDB cache.Cache, queryTokens []string) []string {
	return searchMatchedEntries(ctx, cacheDB, constants.FilesKey, queryTokens)
}

// searchMatchedDirs finds directories matching query tokens
func searchMatchedDirs(ctx context.Context, cacheDB cache.Cache, queryTokens []string) []string {
	return searchMatchedEntries(ctx, cacheDB, constants.DirsKey, queryTokens)
}

// searchMatchedEntries retrieves and filters entries from the cache
func searchMatchedEntries(ctx context.Context, cacheDB cache.Cache, key string, queryTokens []string) []string {
	result := cacheDB.HKeys(ctx, key)
	if result.Err() != nil {
		log.Logger.Error("Failed to fetch keys", zap.String("key", key), zap.Error(result.Err()))
		return nil
	}

	var matches []string
	for _, entry := range result.Val() {
		if isSynologyTmpDir(entry) {
			continue
		}
		for _, token := range queryTokens {
			if strings.Contains(strings.ToLower(entry), strings.ToLower(token)) {
				matches = append(matches, entry)
				break
			}
		}
	}
	sort.Strings(matches)
	return matches
}

// searchMatchedTrees finds files and directories in the tree structure
func searchMatchedTrees(ctx context.Context, cacheDB cache.Cache, queryTokens []string) []string {
	if len(queryTokens) == 0 {
		return []string{}
	}
	inodes := collectInodes(ctx, cacheDB, queryTokens)
	if len(inodes) == 0 {
		return []string{}
	}

	files := fetchFilesByInodes(ctx, cacheDB, inodes)
	sort.Strings(files)
	return files
}

// collectInodes gathers inodes from various sources
func collectInodes(ctx context.Context, cacheDB cache.Cache, queryTokens []string) []string {
	inodes := make([]string, 0)
	for _, key := range []string{constants.DirsKey, constants.FilesKey, constants.TreesKey} {
		log.Logger.Debug("Fetching inodes", zap.String("key", key), zap.Strings("fields", queryTokens))
		result := cacheDB.HMGet(ctx, key, queryTokens...)
		if result.Err() != nil {
			log.Logger.Error("Failed to fetch inodes", zap.String("key", key), zap.Error(result.Err()))
			continue
		}

		for _, val := range result.Val() {
			if val == nil {
				continue
			}
			if key == constants.TreesKey {
				var inodeInts []uint64
				if err := json.Unmarshal([]byte(val.(string)), &inodeInts); err == nil {
					for _, inode := range inodeInts {
						inodes = append(inodes, fmt.Sprintf("%d", inode))
					}
				}
			} else {
				inodes = append(inodes, val.(string))
			}
		}
	}
	return lo.Uniq(inodes)
}

// fetchFilesByInodes retrieves file paths using inode references
func fetchFilesByInodes(ctx context.Context, cacheDB cache.Cache, inodes []string) []string {
	log.Logger.Debug("Fetching files by inodes", zap.String("key", constants.InodesKey), zap.Strings("fields", inodes))
	result := cacheDB.HMGet(ctx, constants.InodesKey, inodes...)
	if result.Err() != nil {
		log.Logger.Error("Failed to fetch files by inodes", zap.Error(result.Err()))
		return nil
	}

	var files []string
	for _, val := range result.Val() {
		if val != nil {
			var fileArr []string
			if err := json.Unmarshal([]byte(val.(string)), &fileArr); err == nil {
				files = append(files, fileArr...)
			}
		}
	}
	return files
}

// isSynologyTmpDir checks if a path contains Synology temporary directories
func isSynologyTmpDir(path string) bool {
	return strings.Contains(path, "@eadir") || strings.Contains(path, "@eaDir")
}
