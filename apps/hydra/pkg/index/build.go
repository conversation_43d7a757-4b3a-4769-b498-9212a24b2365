package index

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"syscall"
	"time"

	"github.com/penwyp/hydra/apps/hydra/internal/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/json"
	"github.com/penwyp/hydra/shared/log"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// BuildIndex constructs the file system index and stores it in Redis.
func BuildIndex(ctx context.Context, folders []string, redisAddress string) {
	// Use DB 1 as temporary database for building new index
	tempDB := cache.OpenCacheDBWithDB(redisAddress, 1)
	mainDB := cache.OpenCacheDB(redisAddress)

	log.Logger.Info("Starting index build", zap.Strings("folders", folders))

	// Clear temporary database first
	if err := tempDB.FlushDB(ctx); err != nil {
		log.Logger.Error("Failed to clear temporary database", zap.Error(err))
		return
	}

	versionStr := time.Now().Format(constants.TimeFormat)
	iterateResult := iterateAllFolderFiles(folders)

	// Build new index in temporary database
	saveIndexesToDB(ctx, iterateResult, tempDB)
	saveVersionToDB(ctx, tempDB, versionStr)

	// Validate the new index
	if err := validateIndex(ctx, tempDB, iterateResult); err != nil {
		log.Logger.Error("Index validation failed", zap.Error(err))
		// Clear failed build from temp DB
		tempDB.FlushDB(ctx)
		return
	}

	// Validation passed, swap the databases
	if err := swapIndexDatabases(ctx, mainDB, tempDB, redisAddress); err != nil {
		log.Logger.Error("Failed to swap databases", zap.Error(err))
		return
	}

	log.Logger.Info("Index build completed", zap.String("version", versionStr))
}

// saveIndexesToDB saves all index mappings to Redis.
func saveIndexesToDB(ctx context.Context, result *IterateResult, cacheDB cache.Cache) {
	saveFileToInodeToDB(ctx, result, cacheDB)
	saveDirInodesToDB(ctx, result, cacheDB)
	saveInodeToFilesToDB(ctx, result, cacheDB)
}

// saveVersionToDB saves the version string to Redis.
func saveVersionToDB(ctx context.Context, cacheDB cache.Cache, versionStr string) {
	if err := cacheDB.Set(ctx, constants.VersionKey, versionStr, 0); err != nil {
		log.Logger.Error("Failed to save version", zap.Error(err))
	}
}

// saveInodeToFilesToDB saves the inode-to-files mapping to Redis.
func saveInodeToFilesToDB(ctx context.Context, result *IterateResult, cacheDB cache.Cache) {
	for _, chunk := range result.GetInodeFilesMapByRedisChunks() {
		if err := cacheDB.HSet(ctx, constants.InodesKey, chunk); err.Err() != nil {
			log.Logger.Error("Failed to save inode-to-files chunk", zap.Error(err.Err()))
		}
	}
	log.Logger.Info("Saved inode-to-files index", zap.Int("total", len(result.GetInodeFilesMap())))
}

// saveDirInodesToDB saves directory-related indexes to Redis.
func saveDirInodesToDB(ctx context.Context, result *IterateResult, cacheDB cache.Cache) {
	for _, chunk := range result.GetDirInodesMapByRedisChunks() {
		if err := cacheDB.HSet(ctx, constants.TreesKey, chunk); err.Err() != nil {
			log.Logger.Error("Failed to save dir-to-inodes chunk", zap.Error(err.Err()))
		}
	}
	log.Logger.Info("Saved dir-to-inodes index", zap.Int("total", len(result.GetDirInodesMap())))

	for _, chunk := range result.GetDirInodeMapByRedisChunks() {
		if err := cacheDB.HSet(ctx, constants.DirsKey, chunk); err.Err() != nil {
			log.Logger.Error("Failed to save dir-to-inode chunk", zap.Error(err.Err()))
		}
	}
	log.Logger.Info("Saved dir-to-inode index", zap.Int("total", len(result.GetDirInodeMap())))
}

// saveFileToInodeToDB saves the file-to-inode mapping to Redis.
func saveFileToInodeToDB(ctx context.Context, result *IterateResult, cacheDB cache.Cache) {
	for _, chunk := range result.GetFileInodeMapByRedisChunks() {
		if err := cacheDB.HSet(ctx, constants.FilesKey, chunk); err.Err() != nil {
			log.Logger.Error("Failed to save file-to-inode chunk", zap.Error(err.Err()))
		}
	}
	log.Logger.Info("Saved file-to-inode index", zap.Int("total", len(result.GetFileInodeMap())))
}

// Counter tracks file and folder traversal counts.
type Counter struct {
	totalWalkCounter  int
	folderWalkCounter int
}

func (c *Counter) IncreaseTotalWalkCounter() {
	c.totalWalkCounter++
}

func (c *Counter) IncreaseFolderWalkCounter() {
	c.folderWalkCounter++
}

func (c *Counter) ResetFolderWalkCounter() {
	c.folderWalkCounter = 0
}

func (c *Counter) LogProgress(folder string) {
	if c.totalWalkCounter%10000 == 0 {
		log.Logger.Info("Scanning files",
			zap.String("folder", folder),
			zap.Int("folder_count", c.folderWalkCounter))
	}
}

func (c *Counter) LogFolderSummary(folder string) {
	log.Logger.Info("Finished scanning folder",
		zap.String("folder", folder),
		zap.Int("folder_count", c.folderWalkCounter),
		zap.Int("total_count", c.totalWalkCounter))
}

// extractUpperFolders extracts upper-level directories from a file path.
func extractUpperFolders(filePath, topFolder string) []string {
	var upperFolders []string
	for i := len(filePath); i > len(topFolder); i-- {
		if filePath[i-1] == filepath.Separator {
			folder := filePath[:i-1]
			if len(folder) >= len(topFolder) {
				upperFolders = append(upperFolders, folder)
			}
		}
	}
	sort.Strings(upperFolders)
	return upperFolders
}

// IterateResult holds file system index data.
type IterateResult struct {
	fileInodeMap  map[string]uint64
	inodeFilesMap map[uint64][]string
	dirInodeMap   map[string]uint64
	dirInodesMap  map[string][]uint64
}

func NewIterateResult() *IterateResult {
	return &IterateResult{
		fileInodeMap:  make(map[string]uint64),
		inodeFilesMap: make(map[uint64][]string),
		dirInodeMap:   make(map[string]uint64),
		dirInodesMap:  make(map[string][]uint64),
	}
}

func (i *IterateResult) GetFileInodeMap() map[string]uint64    { return i.fileInodeMap }
func (i *IterateResult) GetInodeFilesMap() map[uint64][]string { return i.inodeFilesMap }
func (i *IterateResult) GetDirInodeMap() map[string]uint64     { return i.dirInodeMap }
func (i *IterateResult) GetDirInodesMap() map[string][]uint64  { return i.dirInodesMap }

// GetFileInodeMapByRedisChunks splits file-to-inode map into chunks for Redis.
func (i *IterateResult) GetFileInodeMapByRedisChunks() []map[string]interface{} {
	return splitMapToChunks(i.GetFileInodeMap(), 10000)
}

// GetInodeFilesMapByRedisChunks splits inode-to-files map into chunks for Redis.
func (i *IterateResult) GetInodeFilesMapByRedisChunks() []map[string]interface{} {
	return splitMapToChunksWithJSON(i.GetInodeFilesMap(), 10000, func(k uint64) string {
		return fmt.Sprintf("%d", k)
	})
}

// GetDirInodeMapByRedisChunks splits dir-to-inode map into chunks for Redis.
func (i *IterateResult) GetDirInodeMapByRedisChunks() []map[string]interface{} {
	return splitMapToChunks(i.GetDirInodeMap(), 10000)
}

// GetDirInodesMapByRedisChunks splits dir-to-inodes map into chunks for Redis.
func (i *IterateResult) GetDirInodesMapByRedisChunks() []map[string]interface{} {
	return splitMapToChunksWithJSON(i.GetDirInodesMap(), 10000, func(k string) string { return k })
}

// splitMapToChunks splits a map into chunks of a specified size.
func splitMapToChunks[K comparable, V any](m map[K]V, chunkSize int) []map[string]interface{} {
	chunks := make([]map[string]interface{}, 0, len(m)/chunkSize+1)
	chunk := make(map[string]interface{}, chunkSize)
	for k, v := range m {
		chunk[fmt.Sprintf("%v", k)] = v
		if len(chunk) == chunkSize {
			chunks = append(chunks, chunk)
			chunk = make(map[string]interface{}, chunkSize)
		}
	}
	if len(chunk) > 0 {
		chunks = append(chunks, chunk)
	}
	return chunks
}

// splitMapToChunksWithJSON splits a map into chunks, encoding values as JSON.
func splitMapToChunksWithJSON[K comparable, V any](m map[K]V, chunkSize int, keyToString func(K) string) []map[string]interface{} {
	chunks := make([]map[string]interface{}, 0, len(m)/chunkSize+1)
	chunk := make(map[string]interface{}, chunkSize)
	for k, v := range m {
		vString, _ := json.MarshalString(v)
		chunk[keyToString(k)] = vString
		if len(chunk) == chunkSize {
			chunks = append(chunks, chunk)
			chunk = make(map[string]interface{}, chunkSize)
		}
	}
	if len(chunk) > 0 {
		chunks = append(chunks, chunk)
	}
	return chunks
}

func (i *IterateResult) AppendDirInode(dir, filePath string, inode uint64) {
	for _, folder := range extractUpperFolders(filePath, dir) {
		if _, ok := i.dirInodesMap[folder]; !ok {
			i.dirInodesMap[folder] = make([]uint64, 0, 100)
		}
		i.dirInodesMap[folder] = append(i.dirInodesMap[folder], inode)
	}
}

func (i *IterateResult) AppendDir(dir string, ino uint64) {
	i.dirInodeMap[dir] = ino
}

func (i *IterateResult) AppendFileInode(filePath string, inode uint64) {
	i.fileInodeMap[filePath] = inode
	if _, ok := i.inodeFilesMap[inode]; !ok {
		i.inodeFilesMap[inode] = make([]string, 0)
	}
	i.inodeFilesMap[inode] = append(i.inodeFilesMap[inode], filePath)
}

// iterateAllFolderFiles scans folders and builds index data.
func iterateAllFolderFiles(folders []string) *IterateResult {
	counter := &Counter{}
	result := NewIterateResult()

	for _, folder := range folders {
		folder = strings.TrimSpace(folder)
		counter.ResetFolderWalkCounter()
		log.Logger.Info("Starting folder scan", zap.String("folder", folder))

		err := filepath.Walk(folder, func(filePath string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			counter.IncreaseTotalWalkCounter()
			counter.IncreaseFolderWalkCounter()
			counter.LogProgress(folder)

			stat, ok := info.Sys().(*syscall.Stat_t)
			if !ok {
				return errors.New("Failed to get syscall.Stat_t data for " + filePath)
			}
			if info.IsDir() {
				result.AppendDir(filePath, stat.Ino)
			} else {
				result.AppendFileInode(filePath, stat.Ino)
			}
			return nil
		})
		if err != nil {
			log.Logger.Error("Folder scan failed", zap.String("folder", folder), zap.Error(err))
		}
		counter.LogFolderSummary(folder)
	}

	// Batch append directory inodes
	for filePath, inode := range result.fileInodeMap {
		for _, folder := range folders {
			if strings.HasPrefix(filePath, folder) {
				result.AppendDirInode(folder, filePath, inode)
			}
		}
	}
	return result
}

// removeIndexFromDB clears the existing index from Redis.
func removeIndexFromDB(ctx context.Context, cacheDB cache.Cache) error {
	log.Logger.Info("Removing old index")
	if err := cacheDB.FlushDB(ctx); err != nil {
		log.Logger.Error("Failed to remove old index", zap.Error(err))
		return err
	}
	return nil
}

// validateIndex verifies the integrity of the newly built index
func validateIndex(ctx context.Context, cacheDB cache.Cache, result *IterateResult) error {
	// Check if version key exists
	version, err := cacheDB.Get(ctx, constants.VersionKey)
	if err != nil || version == "" {
		return errors.New("version key not found in index")
	}

	// Check if all required keys have data
	filesKeys := cacheDB.HKeys(ctx, constants.FilesKey)
	if filesKeys.Err() != nil || len(filesKeys.Val()) == 0 {
		return errors.New("files index is empty")
	}

	inodesKeys := cacheDB.HKeys(ctx, constants.InodesKey)
	if inodesKeys.Err() != nil || len(inodesKeys.Val()) == 0 {
		return errors.New("inodes index is empty")
	}

	// Verify counts match expectations
	expectedFileCount := len(result.GetFileInodeMap())
	actualFileCount := len(filesKeys.Val())
	minExpected := int(float64(expectedFileCount) * 0.95) // Allow 5% tolerance for any edge cases
	if actualFileCount < minExpected {
		return errors.Errorf("file count mismatch: expected ~%d, got %d", expectedFileCount, actualFileCount)
	}

	log.Logger.Info("Index validation passed",
		zap.String("version", version),
		zap.Int("files", actualFileCount),
		zap.Int("inodes", len(inodesKeys.Val())))

	return nil
}

// swapIndexDatabases atomically swaps the new index from temp DB to main DB
func swapIndexDatabases(ctx context.Context, mainDB, tempDB cache.Cache, redisAddress string) error {
	// First, try to use Redis SWAPDB command if available (Redis 4.0+)
	// If not available, we'll do a manual copy

	// For now, implement the manual approach:
	// 1. Clear main database
	if err := mainDB.FlushDB(ctx); err != nil {
		return errors.Wrap(err, "failed to clear main database")
	}

	// 2. Copy all keys from temp DB to main DB
	// This is done by reading from temp and writing to main
	if err := copyIndexBetweenDBs(ctx, tempDB, mainDB); err != nil {
		return errors.Wrap(err, "failed to copy index to main database")
	}

	// 3. Clear temp database after successful copy
	if err := tempDB.FlushDB(ctx); err != nil {
		log.Logger.Warn("Failed to clear temp database after swap", zap.Error(err))
		// Not a critical error, continue
	}

	return nil
}

// copyIndexBetweenDBs copies all index data from source to destination database
func copyIndexBetweenDBs(ctx context.Context, source, dest cache.Cache) error {
	// Copy version
	version, err := source.Get(ctx, constants.VersionKey)
	if err != nil {
		return errors.Wrap(err, "failed to get version from source")
	}
	if err := dest.Set(ctx, constants.VersionKey, version, 0); err != nil {
		return errors.Wrap(err, "failed to set version in destination")
	}

	// Copy hash keys
	hashKeys := []string{constants.FilesKey, constants.InodesKey, constants.DirsKey, constants.TreesKey}
	for _, key := range hashKeys {
		if err := copyHashKey(ctx, source, dest, key); err != nil {
			return errors.Wrapf(err, "failed to copy hash key %s", key)
		}
	}

	return nil
}

// copyHashKey copies a hash key from source to destination
func copyHashKey(ctx context.Context, source, dest cache.Cache, key string) error {
	// Get all fields from source
	allKeys := source.HKeys(ctx, key)
	if allKeys.Err() != nil {
		if allKeys.Err() == redis.Nil {
			// Key doesn't exist, skip
			return nil
		}
		return allKeys.Err()
	}

	if len(allKeys.Val()) == 0 {
		return nil
	}

	// Process in chunks to avoid memory issues
	chunkSize := 1000
	for i := 0; i < len(allKeys.Val()); i += chunkSize {
		end := i + chunkSize
		if end > len(allKeys.Val()) {
			end = len(allKeys.Val())
		}

		fields := allKeys.Val()[i:end]
		values := source.HMGet(ctx, key, fields...)
		if values.Err() != nil {
			return values.Err()
		}

		// Build map for HSet
		data := make(map[string]interface{})
		for j, field := range fields {
			if values.Val()[j] != nil {
				data[field] = values.Val()[j]
			}
		}

		if len(data) > 0 {
			if err := dest.HSet(ctx, key, data); err.Err() != nil {
				return err.Err()
			}
		}
	}

	return nil
}
