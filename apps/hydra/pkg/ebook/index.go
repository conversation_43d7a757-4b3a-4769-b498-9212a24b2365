package ebook

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// SimpleIndex 简化的电子书索引 - 仅保存文件名到分类的映射
type SimpleIndex map[string]string // key: 文件名(不含扩展名), value: 分类名称

// IndexManager 索引管理器
type IndexManager struct {
	indexPath string
	index     SimpleIndex
}

// NewIndexManager 创建新的索引管理器
func NewIndexManager(targetDir string) *IndexManager {
	indexPath := filepath.Join(targetDir, ".ebook_categories.json")
	return &IndexManager{
		indexPath: indexPath,
		index:     make(SimpleIndex),
	}
}

// LoadIndex 加载索引文件
func (im *IndexManager) LoadIndex() error {
	// 如果索引文件不存在，创建新的索引
	if _, err := os.Stat(im.indexPath); os.IsNotExist(err) {
		im.index = make(SimpleIndex)
		return im.SaveIndex()
	}

	// 读取现有索引文件
	data, err := os.ReadFile(im.indexPath)
	if err != nil {
		return fmt.Errorf("读取索引文件失败: %w", err)
	}

	im.index = make(SimpleIndex)
	if err := json.Unmarshal(data, &im.index); err != nil {
		log.Logger.Warn("索引文件格式错误，将创建新索引", zap.Error(err))
		im.index = make(SimpleIndex)
		return im.SaveIndex()
	}

	log.Logger.Info("索引文件加载成功",
		zap.String("path", im.indexPath),
		zap.Int("entries", len(im.index)))

	return nil
}

// SaveIndex 保存索引文件
func (im *IndexManager) SaveIndex() error {
	if im.index == nil {
		return fmt.Errorf("索引为空，无法保存")
	}

	data, err := json.MarshalIndent(im.index, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化索引失败: %w", err)
	}

	if err := os.WriteFile(im.indexPath, data, 0644); err != nil {
		return fmt.Errorf("保存索引文件失败: %w", err)
	}

	log.Logger.Debug("索引文件保存成功", zap.String("path", im.indexPath))
	return nil
}

// FindByFileName 根据文件名查找分类
func (im *IndexManager) FindByFileName(fileName string) (string, bool) {
	if im.index == nil {
		return "", false
	}

	category, exists := im.index[fileName]
	return category, exists
}

// FindByFilePath 根据文件路径查找分类
func (im *IndexManager) FindByFilePath(filePath string) (string, bool) {
	if im.index == nil {
		return "", false
	}

	fileName := filepath.Base(filePath)
	nameWithoutExt := fileName[:len(fileName)-len(filepath.Ext(fileName))]

	return im.FindByFileName(nameWithoutExt)
}

// AddEntry 添加索引条目
func (im *IndexManager) AddEntry(file EbookFile, bookInfo *BookInfo, classification *ClassificationResult, targetPath string) {
	if im.index == nil {
		return
	}

	im.index[file.Name] = classification.Category

	log.Logger.Debug("添加索引条目",
		zap.String("fileName", file.Name),
		zap.String("category", classification.Category))
}

// UpdateEntry 更新索引条目
func (im *IndexManager) UpdateEntry(fileName string, category string) {
	if im.index == nil {
		return
	}

	im.index[fileName] = category

	log.Logger.Debug("更新索引条目",
		zap.String("fileName", fileName),
		zap.String("category", category))
}

// RemoveEntry 删除索引条目
func (im *IndexManager) RemoveEntry(fileName string) {
	if im.index == nil {
		return
	}

	if _, exists := im.index[fileName]; exists {
		delete(im.index, fileName)
		log.Logger.Debug("删除索引条目", zap.String("fileName", fileName))
	}
}

// ExportIndex 导出索引到指定文件
func (im *IndexManager) ExportIndex(exportPath string) error {
	if im.index == nil {
		return fmt.Errorf("索引为空，无法导出")
	}

	data, err := json.MarshalIndent(im.index, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化索引失败: %w", err)
	}

	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("导出索引文件失败: %w", err)
	}

	log.Logger.Info("索引导出成功", zap.String("path", exportPath))
	return nil
}

// PrintStats 打印索引统计信息
func (im *IndexManager) PrintStats() {
	if im.index == nil {
		fmt.Println("索引未加载")
		return
	}

	// 统计分类数量
	categoryStats := make(map[string]int)
	for _, category := range im.index {
		categoryStats[category]++
	}

	fmt.Printf("\n=== 电子书分类索引统计 ===\n")
	fmt.Printf("总条目数: %d\n", len(im.index))

	if len(categoryStats) > 0 {
		fmt.Printf("\n分类统计:\n")
		for category, count := range categoryStats {
			fmt.Printf("  %s: %d 本\n", category, count)
		}
	}
}
