package ebook

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// IndexEntry 索引条目
type IndexEntry struct {
	OriginalPath    string                `json:"original_path"`    // 原始文件路径
	TargetPath      string                `json:"target_path"`      // 目标文件路径
	FileName        string                `json:"file_name"`        // 文件名（不含扩展名）
	FileSize        int64                 `json:"file_size"`        // 文件大小
	FileHash        string                `json:"file_hash"`        // 文件哈希（可选，用于更精确的匹配）
	BookInfo        *BookInfo             `json:"book_info"`        // 书本信息
	Classification  *ClassificationResult `json:"classification"`   // 分类结果
	ProcessedTime   time.Time             `json:"processed_time"`   // 处理时间
	LastAccessTime  time.Time             `json:"last_access_time"` // 最后访问时间
}

// EbookIndex 电子书索引
type EbookIndex struct {
	Version     string                 `json:"version"`      // 索引版本
	CreatedTime time.Time              `json:"created_time"` // 创建时间
	UpdatedTime time.Time              `json:"updated_time"` // 更新时间
	Entries     map[string]*IndexEntry `json:"entries"`      // 索引条目，key为文件名
	Stats       IndexStats             `json:"stats"`        // 统计信息
}

// IndexStats 索引统计信息
type IndexStats struct {
	TotalEntries     int            `json:"total_entries"`     // 总条目数
	CategoryCounts   map[string]int `json:"category_counts"`   // 各分类数量
	LastCleanupTime  time.Time      `json:"last_cleanup_time"` // 最后清理时间
	ProcessedToday   int            `json:"processed_today"`   // 今日处理数量
	ProcessedThisWeek int           `json:"processed_this_week"` // 本周处理数量
}

// IndexManager 索引管理器
type IndexManager struct {
	indexPath string
	index     *EbookIndex
}

// NewIndexManager 创建新的索引管理器
func NewIndexManager(targetDir string) *IndexManager {
	indexPath := filepath.Join(targetDir, ".ebook_index.json")
	return &IndexManager{
		indexPath: indexPath,
		index:     nil,
	}
}

// LoadIndex 加载索引文件
func (im *IndexManager) LoadIndex() error {
	// 如果索引文件不存在，创建新的索引
	if _, err := os.Stat(im.indexPath); os.IsNotExist(err) {
		im.index = &EbookIndex{
			Version:     "1.0",
			CreatedTime: time.Now(),
			UpdatedTime: time.Now(),
			Entries:     make(map[string]*IndexEntry),
			Stats: IndexStats{
				CategoryCounts: make(map[string]int),
			},
		}
		return im.SaveIndex()
	}

	// 读取现有索引文件
	data, err := os.ReadFile(im.indexPath)
	if err != nil {
		return fmt.Errorf("读取索引文件失败: %w", err)
	}

	im.index = &EbookIndex{}
	if err := json.Unmarshal(data, im.index); err != nil {
		log.Logger.Warn("索引文件格式错误，将创建新索引", zap.Error(err))
		im.index = &EbookIndex{
			Version:     "1.0",
			CreatedTime: time.Now(),
			UpdatedTime: time.Now(),
			Entries:     make(map[string]*IndexEntry),
			Stats: IndexStats{
				CategoryCounts: make(map[string]int),
			},
		}
		return im.SaveIndex()
	}

	// 确保必要的字段不为nil
	if im.index.Entries == nil {
		im.index.Entries = make(map[string]*IndexEntry)
	}
	if im.index.Stats.CategoryCounts == nil {
		im.index.Stats.CategoryCounts = make(map[string]int)
	}

	log.Logger.Info("索引文件加载成功", 
		zap.String("path", im.indexPath),
		zap.Int("entries", len(im.index.Entries)))

	return nil
}

// SaveIndex 保存索引文件
func (im *IndexManager) SaveIndex() error {
	if im.index == nil {
		return fmt.Errorf("索引为空，无法保存")
	}

	im.index.UpdatedTime = time.Now()
	im.updateStats()

	data, err := json.MarshalIndent(im.index, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化索引失败: %w", err)
	}

	if err := os.WriteFile(im.indexPath, data, 0644); err != nil {
		return fmt.Errorf("保存索引文件失败: %w", err)
	}

	log.Logger.Debug("索引文件保存成功", zap.String("path", im.indexPath))
	return nil
}

// updateStats 更新统计信息
func (im *IndexManager) updateStats() {
	if im.index == nil {
		return
	}

	// 重新计算统计信息
	im.index.Stats.TotalEntries = len(im.index.Entries)
	im.index.Stats.CategoryCounts = make(map[string]int)

	today := time.Now().Truncate(24 * time.Hour)
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	
	processedToday := 0
	processedThisWeek := 0

	for _, entry := range im.index.Entries {
		// 统计分类
		if entry.Classification != nil {
			im.index.Stats.CategoryCounts[entry.Classification.Category]++
		}

		// 统计今日和本周处理数量
		processedDate := entry.ProcessedTime.Truncate(24 * time.Hour)
		if processedDate.Equal(today) {
			processedToday++
		}
		if processedDate.After(weekStart) || processedDate.Equal(weekStart) {
			processedThisWeek++
		}
	}

	im.index.Stats.ProcessedToday = processedToday
	im.index.Stats.ProcessedThisWeek = processedThisWeek
}

// FindByFileName 根据文件名查找索引条目
func (im *IndexManager) FindByFileName(fileName string) *IndexEntry {
	if im.index == nil {
		return nil
	}

	if entry, exists := im.index.Entries[fileName]; exists {
		// 更新最后访问时间
		entry.LastAccessTime = time.Now()
		return entry
	}

	return nil
}

// FindByFilePath 根据文件路径查找索引条目
func (im *IndexManager) FindByFilePath(filePath string) *IndexEntry {
	if im.index == nil {
		return nil
	}

	fileName := filepath.Base(filePath)
	nameWithoutExt := fileName[:len(fileName)-len(filepath.Ext(fileName))]
	
	return im.FindByFileName(nameWithoutExt)
}

// AddEntry 添加索引条目
func (im *IndexManager) AddEntry(file EbookFile, bookInfo *BookInfo, classification *ClassificationResult, targetPath string) {
	if im.index == nil {
		return
	}

	entry := &IndexEntry{
		OriginalPath:   file.Path,
		TargetPath:     targetPath,
		FileName:       file.Name,
		FileSize:       file.Size,
		BookInfo:       bookInfo,
		Classification: classification,
		ProcessedTime:  time.Now(),
		LastAccessTime: time.Now(),
	}

	im.index.Entries[file.Name] = entry

	log.Logger.Debug("添加索引条目",
		zap.String("fileName", file.Name),
		zap.String("targetPath", targetPath),
		zap.String("category", classification.Category))
}

// UpdateEntry 更新索引条目
func (im *IndexManager) UpdateEntry(fileName string, targetPath string) {
	if im.index == nil {
		return
	}

	if entry, exists := im.index.Entries[fileName]; exists {
		entry.TargetPath = targetPath
		entry.LastAccessTime = time.Now()
		
		log.Logger.Debug("更新索引条目",
			zap.String("fileName", fileName),
			zap.String("newTargetPath", targetPath))
	}
}

// RemoveEntry 删除索引条目
func (im *IndexManager) RemoveEntry(fileName string) {
	if im.index == nil {
		return
	}

	if _, exists := im.index.Entries[fileName]; exists {
		delete(im.index.Entries, fileName)
		log.Logger.Debug("删除索引条目", zap.String("fileName", fileName))
	}
}

// CleanupStaleEntries 清理过期条目
func (im *IndexManager) CleanupStaleEntries(maxAge time.Duration) int {
	if im.index == nil {
		return 0
	}

	cutoffTime := time.Now().Add(-maxAge)
	removedCount := 0

	for fileName, entry := range im.index.Entries {
		// 检查目标文件是否仍然存在
		if _, err := os.Stat(entry.TargetPath); os.IsNotExist(err) {
			delete(im.index.Entries, fileName)
			removedCount++
			log.Logger.Debug("清理不存在的文件条目",
				zap.String("fileName", fileName),
				zap.String("targetPath", entry.TargetPath))
			continue
		}

		// 检查是否长时间未访问
		if entry.LastAccessTime.Before(cutoffTime) {
			delete(im.index.Entries, fileName)
			removedCount++
			log.Logger.Debug("清理长时间未访问的条目",
				zap.String("fileName", fileName),
				zap.Duration("age", time.Since(entry.LastAccessTime)))
		}
	}

	if removedCount > 0 {
		im.index.Stats.LastCleanupTime = time.Now()
		log.Logger.Info("索引清理完成", zap.Int("removedCount", removedCount))
	}

	return removedCount
}

// GetStats 获取索引统计信息
func (im *IndexManager) GetStats() IndexStats {
	if im.index == nil {
		return IndexStats{}
	}

	im.updateStats()
	return im.index.Stats
}

// ExportIndex 导出索引到指定文件
func (im *IndexManager) ExportIndex(exportPath string) error {
	if im.index == nil {
		return fmt.Errorf("索引为空，无法导出")
	}

	data, err := json.MarshalIndent(im.index, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化索引失败: %w", err)
	}

	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("导出索引文件失败: %w", err)
	}

	log.Logger.Info("索引导出成功", zap.String("path", exportPath))
	return nil
}

// PrintStats 打印索引统计信息
func (im *IndexManager) PrintStats() {
	if im.index == nil {
		fmt.Println("索引未加载")
		return
	}

	stats := im.GetStats()
	
	fmt.Printf("\n=== 电子书索引统计 ===\n")
	fmt.Printf("索引版本: %s\n", im.index.Version)
	fmt.Printf("创建时间: %s\n", im.index.CreatedTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("更新时间: %s\n", im.index.UpdatedTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("总条目数: %d\n", stats.TotalEntries)
	fmt.Printf("今日处理: %d\n", stats.ProcessedToday)
	fmt.Printf("本周处理: %d\n", stats.ProcessedThisWeek)
	
	if len(stats.CategoryCounts) > 0 {
		fmt.Printf("\n分类统计:\n")
		for category, count := range stats.CategoryCounts {
			fmt.Printf("  %s: %d 本\n", category, count)
		}
	}
	
	if !stats.LastCleanupTime.IsZero() {
		fmt.Printf("最后清理: %s\n", stats.LastCleanupTime.Format("2006-01-02 15:04:05"))
	}
}
