package ebook

import (
	"os"
	"path/filepath"
	"strings"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// EbookFile 表示一个电子书文件
type EbookFile struct {
	Path      string // 完整路径
	Name      string // 文件名（不含扩展名）
	Extension string // 文件扩展名
	Size      int64  // 文件大小（字节）
}

// ScanOptions 扫描选项
type ScanOptions struct {
	SourceDir  string   // 源目录
	Extensions []string // 支持的文件扩展名
}

// DefaultExtensions 默认支持的电子书扩展名
var DefaultExtensions = []string{
	".pdf", ".epub", ".mobi", ".azw", ".azw3", ".txt", ".djvu", ".fb2", ".lit", ".pdb",
}

// Scanner 电子书扫描器
type Scanner struct {
	options ScanOptions
}

// NewScanner 创建新的扫描器
func NewScanner(sourceDir string) *Scanner {
	return &Scanner{
		options: ScanOptions{
			SourceDir:  sourceDir,
			Extensions: DefaultExtensions,
		},
	}
}

// SetExtensions 设置支持的文件扩展名
func (s *Scanner) SetExtensions(extensions []string) {
	s.options.Extensions = extensions
}

// isEbookFile 检查文件是否为电子书文件
func (s *Scanner) isEbookFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	for _, supportedExt := range s.options.Extensions {
		if ext == strings.ToLower(supportedExt) {
			return true
		}
	}
	return false
}

// ScanDirectory 扫描目录中的电子书文件
func (s *Scanner) ScanDirectory() ([]EbookFile, error) {
	var ebookFiles []EbookFile

	log.Logger.Info("开始扫描电子书目录", zap.String("directory", s.options.SourceDir))

	err := filepath.Walk(s.options.SourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.Logger.Warn("访问文件失败", zap.String("path", path), zap.Error(err))
			return nil // 继续处理其他文件
		}

		// 检查是否为电子书文件（包括被识别为目录的.epub等文件）
		if !s.isEbookFile(info.Name()) {
			// 如果不是电子书文件且是目录，跳过
			if info.IsDir() {
				return nil
			}
			return nil
		}

		// 对于电子书文件，即使被识别为目录也要处理（如macOS上的.epub文件）

		// 创建电子书文件对象
		ebookFile := EbookFile{
			Path:      path,
			Name:      strings.TrimSuffix(info.Name(), filepath.Ext(info.Name())),
			Extension: strings.ToLower(filepath.Ext(info.Name())),
			Size:      info.Size(),
		}

		ebookFiles = append(ebookFiles, ebookFile)
		log.Logger.Debug("发现电子书文件",
			zap.String("path", path),
			zap.String("name", ebookFile.Name),
			zap.String("extension", ebookFile.Extension),
			zap.Int64("size", ebookFile.Size))

		return nil
	})

	if err != nil {
		log.Logger.Error("扫描目录失败", zap.String("directory", s.options.SourceDir), zap.Error(err))
		return nil, err
	}

	log.Logger.Info("扫描完成",
		zap.String("directory", s.options.SourceDir),
		zap.Int("totalFiles", len(ebookFiles)))

	return ebookFiles, nil
}

// GetFilesByExtension 按扩展名分组文件
func GetFilesByExtension(files []EbookFile) map[string][]EbookFile {
	result := make(map[string][]EbookFile)

	for _, file := range files {
		ext := file.Extension
		if _, exists := result[ext]; !exists {
			result[ext] = make([]EbookFile, 0)
		}
		result[ext] = append(result[ext], file)
	}

	return result
}

// FilterFilesBySize 按文件大小过滤文件
func FilterFilesBySize(files []EbookFile, minSize, maxSize int64) []EbookFile {
	var filtered []EbookFile

	for _, file := range files {
		if (minSize <= 0 || file.Size >= minSize) && (maxSize <= 0 || file.Size <= maxSize) {
			filtered = append(filtered, file)
		}
	}

	return filtered
}
