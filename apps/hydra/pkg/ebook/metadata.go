package ebook

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// BookInfo 书本信息结构
type BookInfo struct {
	Title       string   `json:"title"`
	Author      string   `json:"author"`
	Publisher   string   `json:"publisher"`
	PublishDate string   `json:"publish_date"`
	ISBN        string   `json:"isbn"`
	Description string   `json:"description"`
	Genre       []string `json:"genre"`
	Language    string   `json:"language"`
	PageCount   int      `json:"page_count"`
	Rating      float64  `json:"rating"`
}

// MetadataRetriever 元数据检索器
type MetadataRetriever struct {
	httpClient *http.Client
	apiKeys    map[string]string // 各种API的密钥
}

// NewMetadataRetriever 创建新的元数据检索器
func NewMetadataRetriever() *MetadataRetriever {
	return &MetadataRetriever{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		apiKeys: make(map[string]string),
	}
}

// SetAPIKey 设置API密钥
func (m *MetadataRetriever) SetAPIKey(service, key string) {
	m.apiKeys[service] = key
}

// cleanFileName 清理文件名，提取可能的书名和作者信息
func (m *MetadataRetriever) cleanFileName(filename string) (title, author string) {
	// 移除常见的文件名模式
	cleaned := filename
	
	// 移除括号内容（通常是版本、格式等信息）
	re := regexp.MustCompile(`\([^)]*\)|\[[^\]]*\]|\{[^}]*\}`)
	cleaned = re.ReplaceAllString(cleaned, "")
	
	// 移除常见的分隔符和多余空格
	cleaned = strings.ReplaceAll(cleaned, "_", " ")
	cleaned = strings.ReplaceAll(cleaned, "-", " ")
	cleaned = regexp.MustCompile(`\s+`).ReplaceAllString(cleaned, " ")
	cleaned = strings.TrimSpace(cleaned)
	
	// 尝试分离作者和书名（常见模式：作者 - 书名 或 书名 - 作者）
	if strings.Contains(cleaned, " - ") {
		parts := strings.Split(cleaned, " - ")
		if len(parts) == 2 {
			// 简单启发式：较短的通常是作者名
			if len(parts[0]) < len(parts[1]) {
				author = strings.TrimSpace(parts[0])
				title = strings.TrimSpace(parts[1])
			} else {
				title = strings.TrimSpace(parts[0])
				author = strings.TrimSpace(parts[1])
			}
		}
	}
	
	// 如果没有找到分隔符，整个作为书名
	if title == "" {
		title = cleaned
	}
	
	return title, author
}

// searchGoogleBooks 使用Google Books API搜索书本信息
func (m *MetadataRetriever) searchGoogleBooks(query string) (*BookInfo, error) {
	baseURL := "https://www.googleapis.com/books/v1/volumes"
	params := url.Values{}
	params.Add("q", query)
	params.Add("maxResults", "1")
	
	if apiKey, exists := m.apiKeys["google"]; exists {
		params.Add("key", apiKey)
	}
	
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	
	resp, err := m.httpClient.Get(fullURL)
	if err != nil {
		return nil, fmt.Errorf("Google Books API请求失败: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Google Books API返回错误状态: %d", resp.StatusCode)
	}
	
	var result struct {
		Items []struct {
			VolumeInfo struct {
				Title         string   `json:"title"`
				Authors       []string `json:"authors"`
				Publisher     string   `json:"publisher"`
				PublishedDate string   `json:"publishedDate"`
				Description   string   `json:"description"`
				Categories    []string `json:"categories"`
				Language      string   `json:"language"`
				PageCount     int      `json:"pageCount"`
				AverageRating float64  `json:"averageRating"`
				IndustryIdentifiers []struct {
					Type       string `json:"type"`
					Identifier string `json:"identifier"`
				} `json:"industryIdentifiers"`
			} `json:"volumeInfo"`
		} `json:"items"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析Google Books API响应失败: %w", err)
	}
	
	if len(result.Items) == 0 {
		return nil, fmt.Errorf("未找到匹配的书本信息")
	}
	
	item := result.Items[0].VolumeInfo
	bookInfo := &BookInfo{
		Title:       item.Title,
		Publisher:   item.Publisher,
		PublishDate: item.PublishedDate,
		Description: item.Description,
		Language:    item.Language,
		PageCount:   item.PageCount,
		Rating:      item.AverageRating,
	}
	
	// 处理作者
	if len(item.Authors) > 0 {
		bookInfo.Author = strings.Join(item.Authors, ", ")
	}
	
	// 处理分类/类型
	if len(item.Categories) > 0 {
		bookInfo.Genre = item.Categories
	}
	
	// 处理ISBN
	for _, identifier := range item.IndustryIdentifiers {
		if identifier.Type == "ISBN_13" || identifier.Type == "ISBN_10" {
			bookInfo.ISBN = identifier.Identifier
			break
		}
	}
	
	return bookInfo, nil
}

// RetrieveMetadata 检索书本元数据
func (m *MetadataRetriever) RetrieveMetadata(filename string) (*BookInfo, error) {
	title, author := m.cleanFileName(filename)
	
	log.Logger.Debug("提取文件名信息",
		zap.String("filename", filename),
		zap.String("title", title),
		zap.String("author", author))
	
	// 构建搜索查询
	var query string
	if author != "" {
		query = fmt.Sprintf("%s %s", title, author)
	} else {
		query = title
	}
	
	// 尝试使用Google Books API
	bookInfo, err := m.searchGoogleBooks(query)
	if err != nil {
		log.Logger.Warn("Google Books API搜索失败",
			zap.String("query", query),
			zap.Error(err))
		
		// 返回基于文件名的基本信息
		return &BookInfo{
			Title:  title,
			Author: author,
		}, nil
	}
	
	log.Logger.Info("成功检索书本信息",
		zap.String("filename", filename),
		zap.String("title", bookInfo.Title),
		zap.String("author", bookInfo.Author))
	
	return bookInfo, nil
}

// BatchRetrieveMetadata 批量检索元数据
func (m *MetadataRetriever) BatchRetrieveMetadata(files []EbookFile) map[string]*BookInfo {
	result := make(map[string]*BookInfo)
	
	for i, file := range files {
		log.Logger.Info("检索书本信息",
			zap.Int("current", i+1),
			zap.Int("total", len(files)),
			zap.String("filename", file.Name))
		
		bookInfo, err := m.RetrieveMetadata(file.Name)
		if err != nil {
			log.Logger.Error("检索书本信息失败",
				zap.String("filename", file.Name),
				zap.Error(err))
			continue
		}
		
		result[file.Path] = bookInfo
		
		// 添加延迟避免API限制
		if i < len(files)-1 {
			time.Sleep(100 * time.Millisecond)
		}
	}
	
	return result
}
