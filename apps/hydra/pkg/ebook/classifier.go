package ebook

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// BookCategory 书本分类
type BookCategory struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// ClassificationResult 分类结果
type ClassificationResult struct {
	Category   string  `json:"category"`
	Confidence float64 `json:"confidence"`
	Reason     string  `json:"reason"`
}

// LLMClassifier LLM分类器
type LLMClassifier struct {
	apiKey     string
	apiURL     string
	model      string
	categories []BookCategory
	httpClient *http.Client
}

// DefaultCategories 默认书本分类
var DefaultCategories = []BookCategory{
	// 一、人文类
	{Name: "人文·历史文化", Description: "历史文化类书籍，包括世界史、中国史、文化研究等"},
	{Name: "人文·历史文学", Description: "历史文学作品，历史小说、历史散文等"},
	{Name: "人文·哲学与思维", Description: "哲学、思维方法、逻辑学等"},
	{Name: "人文·宗教与灵修", Description: "宗教、灵修、禅学、佛学等"},
	{Name: "人文·旅行与地理", Description: "旅行指南、地理、文化探索类书籍"},

	// 二、社会科学类
	{Name: "社会科学·政治制度", Description: "政治学、制度研究、政策分析等"},
	{Name: "社会科学·心理学与自我成长", Description: "心理学、自我提升、个人成长等"},
	{Name: "社会科学·社会纪实", Description: "社会调查、纪实文学、社会问题研究等"},
	{Name: "社会科学·人物传记", Description: "人物传记、自传、回忆录等"},
	{Name: "社会科学·法律与社会规则", Description: "法律、社会规则、制度研究等"},
	{Name: "社会科学·教育与学习方法", Description: "教育学、学习方法、教学理论等"},
	{Name: "社会科学·经济与金融", Description: "经济学、金融、投资理财等"},
	{Name: "社会科学·商业经管", Description: "商业管理、企业经营、管理学等"},

	// 三、文学艺术类
	{Name: "文学艺术·文学小说", Description: "各类文学小说，包括现代小说、经典文学等"},
	{Name: "文学艺术·日本文学", Description: "日本文学作品，包括小说、散文等"},
	{Name: "文学艺术·外国文学", Description: "外国文学作品（除日本外），包括欧美文学等"},
	{Name: "文学艺术·中国文学", Description: "中国文学作品，包括古典文学、现代文学等"},
	{Name: "文学艺术·科幻小说", Description: "科幻小说、奇幻小说、未来主义文学等"},
	{Name: "文学艺术·网络文学", Description: "网络小说、轻小说等网络文学作品"},
	{Name: "文学艺术·诗歌戏剧", Description: "诗歌、戏剧、剧本等文学形式"},
	{Name: "文学艺术·艺术设计", Description: "艺术、设计、美学、创作理论等"},

	// 四、自然科学类
	{Name: "自然科学·科学新知", Description: "自然科学、数学、物理、化学、生物等科学知识"},
	{Name: "自然科学·医学与健康", Description: "医学、健康、养生、运动类书籍"},

	// 五、应用学科类
	{Name: "应用学科·技术与编程", Description: "编程、计算机科学、工程技术类书籍"},
	{Name: "应用学科·语言学习", Description: "语言学习、外语教材、语言学等"},
}

// NewLLMClassifier 创建新的LLM分类器
func NewLLMClassifier(apiKey, apiURL, model string) *LLMClassifier {
	return &LLMClassifier{
		apiKey:     apiKey,
		apiURL:     apiURL,
		model:      model,
		categories: DefaultCategories,
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// SetCategories 设置自定义分类
func (c *LLMClassifier) SetCategories(categories []BookCategory) {
	c.categories = categories
}

// buildPrompt 构建分类提示词
func (c *LLMClassifier) buildPrompt(bookInfo *BookInfo) string {
	// 构建分类列表
	var categoryList strings.Builder
	for i, cat := range c.categories {
		categoryList.WriteString(fmt.Sprintf("%d. %s: %s\n", i+1, cat.Name, cat.Description))
	}

	prompt := fmt.Sprintf(`请根据以下书本信息，将其分类到最合适的类别中。

书本信息：
- 标题：%s
- 作者：%s
- 出版社：%s
- 描述：%s
- 类型：%s
- 语言：%s

可选分类：
%s

请以JSON格式返回分类结果，包含以下字段：
- category: 分类名称（必须是上述分类中的一个）
- confidence: 置信度（0-1之间的数值）
- reason: 分类理由（简短说明）

示例格式：
{
  "category": "技术",
  "confidence": 0.9,
  "reason": "这是一本关于编程的技术书籍"
}`,
		bookInfo.Title,
		bookInfo.Author,
		bookInfo.Publisher,
		truncateString(bookInfo.Description, 200),
		strings.Join(bookInfo.Genre, ", "),
		bookInfo.Language,
		categoryList.String())

	return prompt
}

// truncateString 截断字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// callLLMAPI 调用LLM API
func (c *LLMClassifier) callLLMAPI(prompt string) (*ClassificationResult, error) {
	requestBody := map[string]interface{}{
		"model": c.model,
		"messages": []map[string]string{
			{
				"role":    "system",
				"content": "你是一个专业的图书分类专家，能够准确地将书籍分类到合适的类别中。请严格按照要求的JSON格式返回结果。",
			},
			{
				"role":    "user",
				"content": prompt,
			},
		},
		"temperature": 0.3,
		"max_tokens":  500,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	req, err := http.NewRequest("POST", c.apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("LLM API请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("LLM API返回错误状态: %d", resp.StatusCode)
	}

	var response struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析LLM API响应失败: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("LLM API返回空响应")
	}

	content := response.Choices[0].Message.Content

	// 尝试解析JSON响应
	var result ClassificationResult
	if err := json.Unmarshal([]byte(content), &result); err != nil {
		// 如果JSON解析失败，尝试从文本中提取分类信息
		log.Logger.Warn("LLM响应JSON解析失败，尝试文本解析",
			zap.String("content", content),
			zap.Error(err))

		// 简单的文本解析逻辑
		var parseErr error
		result, parseErr = c.parseTextResponse(content)
		if parseErr != nil {
			return nil, fmt.Errorf("解析LLM响应失败: %w", parseErr)
		}
	}

	// 验证分类是否有效
	if !c.isValidCategory(result.Category) {
		return nil, fmt.Errorf("LLM返回了无效的分类: %s", result.Category)
	}

	return &result, nil
}

// parseTextResponse 解析文本响应
func (c *LLMClassifier) parseTextResponse(content string) (ClassificationResult, error) {
	// 简单的文本解析逻辑，寻找分类关键词
	content = strings.ToLower(content)

	for _, cat := range c.categories {
		if strings.Contains(content, strings.ToLower(cat.Name)) {
			return ClassificationResult{
				Category:   cat.Name,
				Confidence: 0.7,
				Reason:     "从LLM文本响应中提取的分类",
			}, nil
		}
	}

	return ClassificationResult{}, fmt.Errorf("无法从LLM响应中确定有效分类")
}

// isValidCategory 检查分类是否有效
func (c *LLMClassifier) isValidCategory(category string) bool {
	for _, cat := range c.categories {
		if cat.Name == category {
			return true
		}
	}
	return false
}

// ClassifyBook 分类单本书
func (c *LLMClassifier) ClassifyBook(bookInfo *BookInfo) (*ClassificationResult, error) {
	if c.apiKey == "" {
		return nil, fmt.Errorf("未配置LLM API密钥，无法进行智能分类")
	}

	prompt := c.buildPrompt(bookInfo)

	log.Logger.Debug("调用LLM进行分类",
		zap.String("title", bookInfo.Title),
		zap.String("author", bookInfo.Author))

	result, err := c.callLLMAPI(prompt)
	if err != nil {
		log.Logger.Error("LLM分类失败",
			zap.String("title", bookInfo.Title),
			zap.Error(err))

		return nil, fmt.Errorf("LLM分类失败: %w", err)
	}

	log.Logger.Info("书本分类完成",
		zap.String("title", bookInfo.Title),
		zap.String("category", result.Category),
		zap.Float64("confidence", result.Confidence))

	return result, nil
}

// BatchClassifyBooks 批量分类书本
func (c *LLMClassifier) BatchClassifyBooks(bookInfos map[string]*BookInfo) map[string]*ClassificationResult {
	results := make(map[string]*ClassificationResult)

	i := 0
	total := len(bookInfos)

	for filePath, bookInfo := range bookInfos {
		i++
		log.Logger.Info("分类书本",
			zap.Int("current", i),
			zap.Int("total", total),
			zap.String("title", bookInfo.Title))

		result, err := c.ClassifyBook(bookInfo)
		if err != nil {
			log.Logger.Warn("跳过文件：分类失败",
				zap.String("filePath", filePath),
				zap.String("title", bookInfo.Title),
				zap.Error(err))
			continue
		}

		results[filePath] = result

		// 添加延迟避免API限制
		if i < total {
			time.Sleep(500 * time.Millisecond)
		}
	}

	return results
}
