package transfer

import (
	"context"
	"fmt"
	"strings"

	"github.com/hekmon/transmissionrpc/v3"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

const (
	machineConfigPrefix = "/volume2/service/bin/transmission"
)

func QueryTr2TrInformation(ctx context.Context, trClient *transmissionrpc.Client,
	trsClient *transmissionrpc.Client) ([]transmissionrpc.Torrent, map[string]float64) {

	trTorrents, err := trClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Panic("Failed to get TR torrent list", zap.Error(err))
	}

	trsTorrents, err := trsClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Panic("Failed to get Small TR torrent list", zap.Error(err))
	}

	trTorrentMapping := make(map[string]float64)
	for _, trTorrent := range trsTorrents {
		currentPercentage, exists := trTorrentMapping[*trTorrent.Name]
		if !exists || currentPercentage > *trTorrent.PercentDone {
			trTorrentMapping[*trTorrent.Name] = *trTorrent.PercentDone
		}
	}

	return trTorrents, trTorrentMapping
}

func TransferTrTorrent(ctx context.Context, torrents []transmissionrpc.Torrent, param model.TransferParam,
	bigClient *transmissionrpc.Client, smallClient *transmissionrpc.Client, mapping map[string]float64) {

	for _, trTorrent := range torrents {
		log.Logger.Info("-------------------")
		log.Logger.Info("Processing TR torrent",
			zap.String("state", trTorrent.Status.String()),
			zap.String("name", *trTorrent.Name),
			zap.Time("completeOn", *trTorrent.DoneDate))

		// Check torrent status
		log.Logger.Info("[1] Checking torrent status")
		if *trTorrent.Status != transmissionrpc.TorrentStatusSeed {
			log.Logger.Info("Skipping torrent: status mismatch",
				zap.String("state", trTorrent.Status.String()))
			continue
		}

		// Check torrent progress
		log.Logger.Info("[2] Checking torrent progress")
		if *trTorrent.PercentDone != 1 {
			log.Logger.Info("Skipping torrent: not completed",
				zap.String("name", *trTorrent.Name),
				zap.Int("percentage", int(*trTorrent.PercentDone*100)))
			continue
		}

		// Calculate torrent size
		trTorrentByteSize := *trTorrent.TotalSize / 8
		sizeInMB := float64(trTorrentByteSize) / 1024 / 1024
		sizeInGB := float64(trTorrentByteSize) / 1024 / 1024 / 1024

		// Check torrent size limits
		log.Logger.Info("[4] Checking torrent size",
			zap.Float64("sizeInMB", sizeInMB),
			zap.String("name", *trTorrent.Name))

		if sizeInGB >= float64(param.TransferSizeLimit) {
			log.Logger.Info("Skipping torrent: size exceeds maximum limit",
				zap.String("name", *trTorrent.Name),
				zap.String("formula", fmt.Sprintf("%f >= %d", sizeInGB, param.TransferSizeLimit)))
			continue
		}

		if !param.IsSmallTorrent(uint64(trTorrentByteSize)) {
			log.Logger.Info("Skipping torrent: size exceeds small torrent limit",
				zap.String("name", *trTorrent.Name),
				zap.String("formula", fmt.Sprintf("%v >= %d", trTorrentByteSize, param.TorrentSizeLimit)))
			continue
		}

		// Validate torrent file
		torrentPath := machineConfigPrefix + *trTorrent.TorrentFile
		_, err := util.OpenTorrent(torrentPath)
		if err != nil {
			log.Logger.Error("Failed to open TR torrent file",
				zap.String("name", *trTorrent.Name),
				zap.String("path", torrentPath),
				zap.Error(err))
			continue
		}
		torrentDownloadDir := *trTorrent.DownloadDir

		// Transfer torrent
		isDuplicateTorrent := false
		log.Logger.Info("[5] Starting torrent upload",
			zap.String("name", *trTorrent.Name),
			zap.String("torrentDownloadDir", torrentDownloadDir),
			zap.String("torrentPath", torrentPath))

		transTorrent, err := smallClient.TorrentAddFileDownloadDir(ctx, torrentPath, torrentDownloadDir)
		if err != nil {
			if strings.Contains(err.Error(), "duplicate torrent") {
				isDuplicateTorrent = true
			} else {
				log.Logger.Error("Failed to upload torrent to TR",
					zap.String("name", *trTorrent.Name),
					zap.Error(err))
				continue
			}
		}

		// Start torrent if not duplicate
		if !isDuplicateTorrent {
			if startErr := smallClient.TorrentStartNowIDs(ctx, []int64{*transTorrent.ID}); startErr != nil {
				log.Logger.Error("Failed to start TR torrent",
					zap.String("name", *trTorrent.Name),
					zap.Error(startErr))
				continue
			}
			log.Logger.Info("Started downloading TR torrent",
				zap.String("name", *trTorrent.Name))

			// Verify transfer in safe mode
			trPercentage := mapping[*trTorrent.Name]
			if param.SafeMode && trPercentage != 1 {
				log.Logger.Info("Verification incomplete, keeping original torrent",
					zap.String("name", *trTorrent.Name),
					zap.Any("percentage", int(trPercentage*100)))
				continue
			}
		}

		// Delete original torrent
		deleteErr := bigClient.TorrentRemove(ctx, transmissionrpc.TorrentRemovePayload{
			IDs: []int64{*trTorrent.ID},
		})
		if deleteErr != nil {
			log.Logger.Error("Failed to delete TR torrent",
				zap.String("name", *trTorrent.Name),
				zap.Error(deleteErr))
			continue
		}

		// Log successful deletion
		logMessage := "Successfully deleted TR torrent"
		logFields := []zap.Field{zap.String("name", *trTorrent.Name)}
		if !param.SafeMode {
			logFields = append(logFields, zap.Any("percentage", int(mapping[*trTorrent.Name]*100)))
		}
		log.Logger.Info(logMessage, logFields...)
	}
}
