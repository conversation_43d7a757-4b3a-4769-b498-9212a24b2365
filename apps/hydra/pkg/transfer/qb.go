package transfer

import (
	"context"
	"fmt"
	"path"
	"strings"

	"github.com/hekmon/transmissionrpc/v3"
	"github.com/pen1120/qbapi"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

func QueryQB2TrInformation(ctx context.Context, qbClient *qbapi.QBAPI, trClient *transmissionrpc.Client,
	trsClient *transmissionrpc.Client) (*qbapi.GetTorrentListRsp, map[string]float64, string) {

	qbPreference, err := qbClient.GetApplicationPreferences(ctx, &qbapi.GetApplicationPreferencesReq{})
	if err != nil {
		log.Logger.Error("Failed to get QB preferences", zap.Error(err))
		return nil, nil, ""
	}

	qbTorrents, err := qbClient.GetTorrentList(ctx, &qbapi.GetTorrentListReq{})
	if err != nil {
		log.Logger.Error("Failed to get QB torrent list", zap.Error(err))
		return nil, nil, ""
	}
	log.Logger.Info("Retrieved QB torrent list", zap.Int("count", len(qbTorrents.Items)))

	trTorrents, err := trClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Error("Failed to get TR torrent list", zap.Error(err))
		return nil, nil, ""
	}
	log.Logger.Info("Retrieved TR torrent list", zap.Int("count", len(trTorrents)))

	trsTorrents, err := trsClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Error("Failed to get Small TR torrent list", zap.Error(err))
		return nil, nil, ""
	}
	log.Logger.Info("Retrieved Small TR torrent list", zap.Int("count", len(trsTorrents)))

	trTorrentMapping := make(map[string]float64)
	for _, trTorrent := range append(trTorrents, trsTorrents...) {
		currentPercentage, exists := trTorrentMapping[*trTorrent.Name]
		if !exists || currentPercentage > *trTorrent.PercentDone {
			trTorrentMapping[*trTorrent.Name] = *trTorrent.PercentDone
		}
	}

	return qbTorrents, trTorrentMapping, qbPreference.ExportDirFin
}

func TransferQBTorrent(ctx context.Context, qbTorrents *qbapi.GetTorrentListRsp, param model.TransferParam, edf string,
	trBigClient *transmissionrpc.Client, trSmallClient *transmissionrpc.Client, trTorrentMapping map[string]float64,
	qbClient *qbapi.QBAPI) {

	for _, qbTorrent := range qbTorrents.Items {
		// Select appropriate TR client based on torrent size
		trClient := trBigClient
		if param.IsSmallTorrent(uint64(qbTorrent.Size)) {
			trClient = trSmallClient
		}

		log.Logger.Info("-------------------")
		log.Logger.Info("Processing QB torrent",
			zap.String("state", qbTorrent.State),
			zap.String("name", qbTorrent.Name),
			zap.Int("completeOn", qbTorrent.CompletionOn))

		// Check torrent state
		log.Logger.Info("[1] Checking torrent state")
		if qbTorrent.State == constants.QB_MOVING {
			log.Logger.Info("Skipping torrent: moving in progress",
				zap.String("name", qbTorrent.Name),
				zap.String("state", qbTorrent.State))
			continue
		}

		// Check torrent progress
		log.Logger.Info("[2] Checking torrent progress")
		if qbTorrent.Progress != 1 {
			log.Logger.Info("Skipping torrent: not completed",
				zap.String("name", qbTorrent.Name),
				zap.Int("percentage", int(qbTorrent.Progress*100)))
			continue
		}

		// Check torrent size
		log.Logger.Info("[4] Checking torrent size")
		sizeInGB := float64(qbTorrent.TotalSize) / 1024 / 1024 / 1024
		if sizeInGB >= float64(param.TransferSizeLimit) {
			log.Logger.Info("Skipping torrent: size exceeds limit",
				zap.String("name", qbTorrent.Name),
				zap.String("formula", fmt.Sprintf("%f >= %d", sizeInGB, param.TransferSizeLimit)))
			continue
		}

		// Check save path
		if param.ShouldIgnoreSavePath(qbTorrent.SavePath) {
			log.Logger.Info("Skipping torrent: save path matched",
				zap.String("name", qbTorrent.Name),
				zap.String("path", fmt.Sprintf("%v match %s", param.IgnoreSavePaths, qbTorrent.SavePath)))

			_, tagErr := qbClient.AddTorrentTags(ctx, &qbapi.AddTorrentTagsReq{
				Hash: []string{qbTorrent.Hash},
				Tag:  []string{"Path Ignored"},
			})
			if tagErr != nil {
				log.Logger.Warn("Failed to add path ignored tag", zap.Error(tagErr))
			}
			continue
		}

		// Check tracker rules
		log.Logger.Info("[5] Checking torrent tracker")
		for _, tracker := range param.IgnoreTrackers {
			if strings.Contains(qbTorrent.Tracker, tracker) {
				log.Logger.Info("Skipping torrent: tracker blocked",
					zap.String("name", qbTorrent.Name),
					zap.String("tracker", qbTorrent.Tracker))
				continue
			}
		}

		// Prepare torrent file paths
		hashTorrentPath := path.Join(edf, qbTorrent.Hash+".torrent")
		namedTorrentPath := path.Join(edf, qbTorrent.Name+".torrent")
		log.Logger.Debug("QB torrent details",
			zap.String("name", qbTorrent.Name),
			zap.String("path", hashTorrentPath),
			zap.String("path2", namedTorrentPath))

		// Validate torrent file
		validPath := hashTorrentPath
		_, err := util.OpenTorrent(hashTorrentPath)
		if err != nil {
			_, err = util.OpenTorrent(namedTorrentPath)
			if err != nil {
				log.Logger.Error("Failed to open QB torrent file",
					zap.String("name", qbTorrent.Name),
					zap.String("path", hashTorrentPath),
					zap.String("path2", namedTorrentPath),
					zap.Error(err))
				continue
			}
			validPath = namedTorrentPath
		}

		// Transfer to TR
		tryToAddAndStartTrTorrent(ctx, trClient, validPath, qbTorrent)

		// Verify transfer in safe mode
		trPercentage := trTorrentMapping[qbTorrent.Name]
		if param.SafeMode && trPercentage != 1 {
			log.Logger.Info("Verification incomplete, keeping original torrent",
				zap.String("name", qbTorrent.Name),
				zap.Any("percentage", int(trPercentage*100)))
			continue
		}

		// Check retention period
		log.Logger.Info("[3] Checking torrent retention period")
		if param.IsRetentionTorrent(qbTorrent.CompletionOn) {
			log.Logger.Info("Skipping torrent: retention period not expired",
				zap.String("name", qbTorrent.Name),
				zap.Int("completeTimestamp", qbTorrent.CompletionOn))
			continue
		}

		// Handle dry run
		if param.DryRun {
			log.Logger.Info("Dry run: skipping deletion")
			continue
		}

		// Delete QB torrent
		_, deleteErr := qbClient.DeleteTorrents(ctx, &qbapi.DeleteTorrentsReq{
			Hash:         []string{qbTorrent.Hash},
			IsDeleteFile: false,
		})
		if deleteErr != nil {
			log.Logger.Error("Failed to delete QB torrent",
				zap.String("name", qbTorrent.Name),
				zap.Error(deleteErr))
			continue
		}

		logMessage := "Successfully deleted QB torrent"
		logFields := []zap.Field{zap.String("name", qbTorrent.Name)}
		if !param.SafeMode {
			logFields = append(logFields, zap.Any("percentage", int(trPercentage*100)))
		}
		log.Logger.Info(logMessage, logFields...)
	}
}

func tryToAddAndStartTrTorrent(ctx context.Context, trClient *transmissionrpc.Client, validPath string, qbTorrent *qbapi.TorrentListItem) {
	transTorrent, err := trClient.TorrentAddFileDownloadDir(ctx, validPath, qbTorrent.SavePath)
	if err != nil {
		log.Logger.Warn("Failed to upload torrent to TR",
			zap.String("name", qbTorrent.Name),
			zap.Error(err))
		return
	}

	if startErr := trClient.TorrentStartIDs(ctx, []int64{*transTorrent.ID}); startErr != nil {
		log.Logger.Warn("Failed to start TR torrent",
			zap.String("name", qbTorrent.Name),
			zap.Error(startErr))
		return
	}

	log.Logger.Info("Started downloading TR torrent",
		zap.String("name", qbTorrent.Name))
}
