package chown

import (
	"github.com/fsnotify/fsnotify"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"
)

// ChownStats 封装了 chown 操作的统计信息
type ChownStats struct {
	successCount int
	failureCount int
	skipCount    int
	failedFiles  map[string]error
	mu           sync.Mutex
}

// ChownManager 管理 chown 操作的核心结构体
type ChownManager struct {
	stats      *ChownStats
	uid        int
	gid        int
	ignoreFunc func(string) bool
}

// NewChownManager 创建并初始化 ChownManager 实例
func NewChownManager(userName, userGroup string, ignorePaths []string) (*ChownManager, error) {
	uid, gid, err := util.LookupUserAndGroup(userName, userGroup)
	if err != nil {
		return nil, err
	}

	isIgnored := func(path string) bool {
		for _, ignore := range ignorePaths {
			cleanIgnore := filepath.Clean(ignore)
			cleanPath := filepath.Clean(path)
			if cleanPath == cleanIgnore || strings.HasPrefix(cleanPath, cleanIgnore+"/") {
				return true
			}
		}
		return false
	}

	return &ChownManager{
		stats:      NewChownStats(),
		uid:        uid,
		gid:        gid,
		ignoreFunc: isIgnored,
	}, nil
}

// NewChownStats 创建并初始化 ChownStats 实例
func NewChownStats() *ChownStats {
	return &ChownStats{
		successCount: 0,
		failureCount: 0,
		skipCount:    0,
		failedFiles:  make(map[string]error),
	}
}

// Stats 相关方法
func (s *ChownStats) IncrementSuccess() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.successCount++
}

func (s *ChownStats) IncrementFailure(filePath string, err error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.failureCount++
	s.failedFiles[filePath] = err
}

func (s *ChownStats) IncrementSkip() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.skipCount++
}

func (s *ChownStats) GetStats() (success, failure, skip int, failedFiles map[string]error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.successCount, s.failureCount, s.skipCount, s.failedFiles
}

// applyChownPermToFile 处理单个文件的所有权和权限变更
func (m *ChownManager) applyChownPermToFile(filePath string, info os.FileInfo) error {
	const targetPerm = 0777

	stat, ok := info.Sys().(*syscall.Stat_t)
	if !ok {
		return m.handleError(filePath, syscall.EINVAL, "Failed to get file stats")
	}

	if m.isOwnershipMatch(stat) {
		return m.handleExistingOwnership(filePath, info, targetPerm)
	}

	return m.updateOwnershipAndPerm(filePath, targetPerm)
}

// isOwnershipMatch 检查文件当前所有权是否匹配目标
func (m *ChownManager) isOwnershipMatch(stat *syscall.Stat_t) bool {
	return int(stat.Uid) == m.uid && int(stat.Gid) == m.gid
}

// handleError 统一处理错误情况
func (m *ChownManager) handleError(filePath string, err error, msg string) error {
	m.stats.IncrementFailure(filePath, err)
	log.Logger.Warn(msg,
		zap.String("path", filePath),
		zap.Error(err))
	return nil
}

// handleExistingOwnership 处理已匹配所有权的情况
func (m *ChownManager) handleExistingOwnership(filePath string, info os.FileInfo, targetPerm os.FileMode) error {
	currentPerm := info.Mode().Perm()
	if currentPerm != targetPerm {
		if err := os.Chmod(filePath, targetPerm); err != nil {
			return m.handleError(filePath, err, "Failed to change permissions to 700")
		}
		log.Logger.Debug("Updated permissions to 700",
			zap.String("path", filePath))
	}

	m.stats.IncrementSkip()
	log.Logger.Debug("Skipped file with matching ownership",
		zap.String("path", filePath),
		zap.Int("uid", m.uid),
		zap.Int("gid", m.gid))
	return nil
}

// updateOwnershipAndPerm 更新文件所有权和权限
func (m *ChownManager) updateOwnershipAndPerm(filePath string, targetPerm os.FileMode) error {
	if err := os.Chown(filePath, m.uid, m.gid); err != nil {
		return m.handleError(filePath, err, "Failed to change ownership")
	}

	if err := os.Chmod(filePath, targetPerm); err != nil {
		return m.handleError(filePath, err, "Failed to change permissions to 700")
	}

	m.stats.IncrementSuccess()
	log.Logger.Debug("Successfully changed ownership and set permissions to 700",
		zap.String("path", filePath),
		zap.Int("uid", m.uid),
		zap.Int("gid", m.gid))
	return nil
}

// ApplyOwnerPerm 处理指定路径下的所有文件
func (m *ChownManager) ApplyOwnerPerm(targetPaths []string) {
	currentPathChan := make(chan string, 1)
	doneChan := make(chan struct{})
	defer close(currentPathChan)
	defer close(doneChan)

	go m.logProgress(currentPathChan, doneChan)

	for _, targetPath := range targetPaths {
		select {
		case currentPathChan <- targetPath:
		default:
		}

		err := filepath.Walk(targetPath, func(filePath string, info os.FileInfo, err error) error {
			if err != nil {
				m.stats.IncrementFailure(filePath, err)
				log.Logger.Warn("Failed to access file", zap.String("path", filePath), zap.Error(err))
				return nil
			}
			if m.ignoreFunc(filePath) {
				return nil
			}
			return m.applyChownPermToFile(filePath, info)
		})
		if err != nil {
			log.Logger.Error("Error during chown operation",
				zap.String("path", targetPath),
				zap.Error(err))
		}
	}

	doneChan <- struct{}{}

	success, failure, skip, failedFiles := m.stats.GetStats()
	log.Logger.Info("Chown operation completed",
		zap.Int("success_count", success),
		zap.Int("failure_count", failure),
		zap.Int("skip_count", skip))

	if failure > 0 {
		log.Logger.Warn("Files with failed modifications",
			zap.Any("failed_files", failedFiles))
	}
}

// WatchOwnerPerm 监控文件系统并执行权限变更
func (m *ChownManager) WatchOwnerPerm(targetPaths []string) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Logger.Error("Failed to create watcher", zap.Error(err))
		return
	}
	defer watcher.Close()

	var wg sync.WaitGroup

	for _, path := range targetPaths {
		err = m.addWatchRecursive(watcher, path)
		if err != nil {
			log.Logger.Error("Failed to add watch", zap.String("path", path), zap.Error(err))
			continue
		}
		log.Logger.Info("Started watching", zap.String("path", path))
	}

	wg.Add(1)
	go m.handleEvents(watcher, &wg)
	go m.logWatchProgress()
	wg.Wait()
}

// logProgress 每秒记录处理进度
func (m *ChownManager) logProgress(currentPathChan <-chan string, doneChan chan struct{}) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	var lastPath string
	for {
		select {
		case path, ok := <-currentPathChan:
			if !ok {
				return
			}
			lastPath = path
		case <-ticker.C:
			success, failure, skip, _ := m.stats.GetStats()
			log.Logger.Info("Processing status",
				zap.String("current_path", lastPath),
				zap.Int("success_count", success),
				zap.Int("failure_count", failure),
				zap.Int("skip_count", skip))
		case <-doneChan:
			return
		}
	}
}

// addWatchRecursive 递归添加监控路径
func (m *ChownManager) addWatchRecursive(watcher *fsnotify.Watcher, path string) error {
	return filepath.Walk(path, func(walkPath string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		if m.ignoreFunc(walkPath) {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}
		if info.IsDir() {
			err = watcher.Add(walkPath)
			if err != nil {
				log.Logger.Warn("Failed to watch directory", zap.String("path", walkPath), zap.Error(err))
			}
		}
		return nil
	})
}

// handleEvents 处理文件系统事件
func (m *ChownManager) handleEvents(watcher *fsnotify.Watcher, wg *sync.WaitGroup) {
	defer wg.Done()
	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}
			if m.ignoreFunc(event.Name) {
				continue
			}
			if event.Op&(fsnotify.Create|fsnotify.Write|fsnotify.Chmod|fsnotify.Rename) != 0 {
				m.handleFileEvent(event.Name, event.Op, watcher)
			}
		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			log.Logger.Error("Watcher error", zap.Error(err))
		}
	}
}

// handleFileEvent 处理单个文件事件
func (m *ChownManager) handleFileEvent(filePath string, eventOp fsnotify.Op, watcher *fsnotify.Watcher) {
	info, err := os.Stat(filePath)
	if err != nil {
		log.Logger.Warn("Failed to stat file", zap.String("path", filePath), zap.Error(err))
		return
	}

	if info.IsDir() {
		err = watcher.Add(filePath)
		if err != nil {
			log.Logger.Warn("Failed to watch new directory", zap.String("path", filePath), zap.Error(err))
		}
	}

	log.Logger.Info("Watching file changed", zap.String("file", filePath), zap.String("eventOp", eventOp.String()))
	err = m.applyChownPermToFile(filePath, info)
	if err != nil {
		log.Logger.Warn("Failed to process file event", zap.String("path", filePath), zap.Error(err))
	}

	// 检查文件是否以 ".mp" 结尾
	if strings.HasSuffix(filePath, ".mp") {
		// 去掉 ".mp" 后缀生成新文件名
		newFilePath := strings.TrimSuffix(filePath, ".mp")
		newInfo, err := os.Stat(newFilePath)
		if err != nil {
			log.Logger.Warn("Failed to stat file without .mp suffix", zap.String("path", newFilePath), zap.Error(err))
			return
		}
		// 对新文件名再次应用权限和所有权
		err = m.applyChownPermToFile(newFilePath, newInfo)
		if err != nil {
			log.Logger.Warn("Failed to process file without .mp suffix", zap.String("path", newFilePath), zap.Error(err))
		}
	}
}

// logWatchProgress 定期记录监控进度
func (m *ChownManager) logWatchProgress() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		success, failure, skip, _ := m.stats.GetStats()
		log.Logger.Info("Watcher status",
			zap.Int("success_count", success),
			zap.Int("failure_count", failure),
			zap.Int("skip_count", skip))
	}
}
