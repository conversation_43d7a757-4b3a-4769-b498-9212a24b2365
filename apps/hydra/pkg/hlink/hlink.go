package hlink

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// HardLinkFiles processes hard linking based on provided parameters and configuration
func HardLinkFiles(param *model.HlinkParam, hlinkConfig *model.HlinkConfiguration) []model.PathLinkStatus {
	secondsBefore, err := util.ParseGolangTimeToSeconds(param.GetTimesBefore())
	if err != nil {
		log.Logger.Panic("Failed to parse time: "+param.GetTimesBefore(), zap.Error(err))
	}

	helper := NewHlinkHelper(secondsBefore, param.GetPathPattern())
	var pathLinkStatuses []model.PathLinkStatus

	for _, mapping := range hlinkConfig.IteratePathsMapping() {
		if !helper.MatchPathPattern(mapping.OriPath) {
			log.Logger.Warn("Skipping path", zap.String("path", mapping.OriPath))
			continue
		}

		log.Logger.Info("Starting path mapping",
			zap.String("source", mapping.OriPath),
			zap.String("destination", mapping.DestPath),
			zap.Bool("rename", mapping.Rename),
			zap.Int("text_replacements", len(mapping.TextReplace)),
			zap.Int("linux_acl", mapping.GetLinuxACL()))

		status := processHardLink(mapping.OriPath, mapping, hlinkConfig, helper)
		pathLinkStatuses = append(pathLinkStatuses, status)
	}
	return pathLinkStatuses
}

// DisplayLinkResults renders the linking results in a tabulated format
func DisplayLinkResults(statuses []model.PathLinkStatus, output io.Writer) model.PathLinkStatus {
	t := table.NewWriter()
	t.SetOutputMirror(output)
	t.AppendHeader(table.Row{"Source", "Destination", "Total", "Valid", "Linked", "Renamed"})

	var total model.PathLinkStatus
	for _, status := range statuses {
		total.TotalNum += status.TotalNum
		total.ValidNum += status.ValidNum
		total.LinkedNum += status.LinkedNum
		total.RenamedNum += status.RenamedNum
		t.AppendRow(table.Row{status.OriPath, status.DestPath, status.TotalNum, status.ValidNum, status.LinkedNum, status.RenamedNum})
	}

	t.AppendRow(table.Row{"Total", "", total.TotalNum, total.ValidNum, total.LinkedNum, total.RenamedNum})
	t.Render()
	return total
}

// hlinkHelper assists in file filtering and path matching
type hlinkHelper struct {
	secondsBefore     int
	expireTime        time.Time
	disableTimeFilter bool
	pathPattern       string
}

// NewHlinkHelper creates a new helper instance
func NewHlinkHelper(secondsBefore int, pathPattern string) *hlinkHelper {
	disableTimeFilter := secondsBefore <= 0
	expireTime := time.Now().Add(-time.Duration(secondsBefore) * time.Second)

	log.Logger.Info("Initialized time filter",
		zap.Time("expire_time", expireTime),
		zap.Bool("disable_time_filter", disableTimeFilter))

	return &hlinkHelper{
		secondsBefore:     secondsBefore,
		expireTime:        expireTime,
		disableTimeFilter: disableTimeFilter,
		pathPattern:       pathPattern,
	}
}

func (h *hlinkHelper) ShouldLink(info os.FileInfo) bool {
	return h.disableTimeFilter || info.ModTime().After(h.expireTime)
}

func (h *hlinkHelper) MatchPathPattern(p string) bool {
	return strings.Contains(p, h.pathPattern)
}

// RenameDestPath handles path renaming based on configuration
func RenameDestPath(pp string, param model.HlinkPathMapping) (string, bool) {
	newName := pp
	renamed := false

	if !param.Rename && !param.HasTextReplace() {
		return newName, renamed
	}

	if param.HasTextReplace() {
		if replaced := param.ReplaceText(newName); replaced != newName {
			return replaced, true
		}
		return newName, renamed
	}

	for _, rule := range defaultRenameRules {
		invalid, valid, match, exit := rule.JudgeInvalidSeriesNames(pp)
		if exit {
			break
		}
		if match {
			base := filepath.Base(pp)
			newBase := strings.NewReplacer(
				invalid, valid,
				"[", "",
				"]", "",
			).Replace(base)
			return strings.Replace(pp, base, newBase, 1), true
		}
	}

	if strings.Contains(newName, "[") {
		newName = strings.NewReplacer("[", "", "]", "").Replace(newName)
		renamed = true
	}
	return newName, renamed
}

// processHardLink performs the actual hard linking operation
func processHardLink(oriPath string, mapping model.HlinkPathMapping, config *model.HlinkConfiguration, helper *hlinkHelper) model.PathLinkStatus {
	var status model.PathLinkStatus
	linkFunc := config.GetLinkFunc()
	status.OriPath, status.DestPath = oriPath, mapping.DestPath
	madeDirs := make(map[string]bool)

	err := filepath.Walk(oriPath, func(filePath string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() || !strings.Contains(filePath, ".") {
			return err
		}

		status.TotalNum++
		suffix := strings.TrimPrefix(filepath.Ext(filePath), ".")
		if suffix == "" || !shouldProcessFile(filePath, info, mapping, config, helper) {
			return nil
		}

		status.ValidNum++
		newDestPath, renamed := RenameDestPath(strings.Replace(filePath, oriPath, mapping.DestPath, 1), mapping)
		if err := ensureDirExists(filepath.Dir(newDestPath), madeDirs); err != nil {
			return err
		}

		if _, err := os.Stat(newDestPath); err == nil {
			log.Logger.Debug("Skipping existing file", zap.String("path", newDestPath))
			return nil
		}

		if renamed {
			status.RenamedNum++
		}
		return createHardLink(filePath, newDestPath, mapping, linkFunc, &status)
	})

	if err != nil {
		log.Logger.Error("Error walking path", zap.Error(err))
	}
	return status
}

// shouldProcessFile determines if a file should be processed
func shouldProcessFile(filePath string, info os.FileInfo, mapping model.HlinkPathMapping, config *model.HlinkConfiguration, helper *hlinkHelper) bool {
	return (mapping.SizeLimitMB <= 0 || info.Size() > int64(mapping.SizeLimitMB*1024*1024)) &&
		(mapping.IncludeAllFile || config.MatchFileSuffix(filepath.Ext(filePath)[1:])) &&
		!config.MatchExcludeFolder(filePath) &&
		!config.MatchExcludeRegx(filePath) &&
		helper.ShouldLink(info)
}

// ensureDirExists creates directories if they don't exist
func ensureDirExists(dir string, madeDirs map[string]bool) error {
	if madeDirs[dir] {
		return nil
	}

	log.Logger.Debug("Creating directory", zap.String("path", dir))
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		log.Logger.Error("Failed to create directory", zap.Error(err), zap.String("path", dir))
		return err
	}
	madeDirs[dir] = true
	return nil
}

// createHardLink creates the hard link and sets permissions
func createHardLink(src, dest string, mapping model.HlinkPathMapping, linkFunc func(string, string) error, status *model.PathLinkStatus) error {
	log.Logger.Info("Creating hard link", zap.String("destination", dest))
	if err := linkFunc(src, dest); err != nil {
		log.Logger.Error("Failed to create hard link",
			zap.Error(err),
			zap.String("source", src),
			zap.String("destination", dest))
		return err
	}

	status.LinkedNum++
	perm, _ := strconv.ParseUint(fmt.Sprintf("%d", mapping.GetLinuxACL()), 8, 32)
	log.Logger.Debug("Setting file permissions",
		zap.String("path", dest),
		zap.String("permission", fmt.Sprintf("%o", perm)))

	if err := os.Chmod(dest, os.FileMode(perm)); err != nil {
		log.Logger.Error("Failed to set file permissions",
			zap.Error(err),
			zap.String("path", dest))
	}
	return nil
}
