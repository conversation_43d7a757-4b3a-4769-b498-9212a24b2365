package hlink

import (
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"testing"
)

func TestRenameDestPath(t *testing.T) {

	m := model.HlinkPathMapping{
		TextReplace: []model.TextReplaceRule{
			{
				From:          "_Unable.",
				To:            ".",
				CaseSensitive: true,
			},
		},
	}
	type args struct {
		pp    string
		param model.HlinkPathMapping
	}
	tests := []struct {
		name        string
		args        args
		wantNewName string
		wantRenamed bool
	}{
		{
			args:        args{pp: "xx_yy_XaC_Unable.mkv", param: m},
			wantNewName: "xx_yy_XaC.mkv",
			wantRenamed: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNewName, gotRenamed := RenameDestPath(tt.args.pp, tt.args.param)
			if gotNewName != tt.wantNewName {
				t.Errorf("RenameDestPath() gotNewName = %v, want %v", gotNewName, tt.wantNewName)
			}
			if gotRenamed != tt.wantRenamed {
				t.<PERSON>rf("RenameDestPath() gotRenamed = %v, want %v", gotRenamed, tt.wantRenamed)
			}
		})
	}
}
