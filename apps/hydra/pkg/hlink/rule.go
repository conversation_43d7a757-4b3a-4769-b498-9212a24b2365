package hlink

import (
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/samber/lo"
)

var defaultChineseSeriesNums = []string{"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十"}

// 0 to 200 in str
var defaultArabicSeriesNums = lo.Reverse([]string{
	"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10",
	"11", "12", "13", "14", "15", "16", "17", "18", "19", "20",
	"21", "22", "23", "24", "25", "26", "27", "28", "29", "30",
	"31", "32", "33", "34", "35", "36", "37", "38", "39", "40",
	"41", "42", "43", "44", "45", "46", "47", "48", "49", "50",
	"51", "52", "53", "54", "55", "56", "57", "58", "59", "60",
	"61", "62", "63", "64", "65", "66", "67", "68", "69", "70",
	"71", "72", "73", "74", "75", "76", "77", "78", "79", "80",
	"81", "82", "83", "84", "85", "86", "87", "88", "89", "90",
	"91", "92", "93", "94", "95", "96", "97", "98", "99", "100",
	"101", "102", "103", "104", "105", "106", "107", "108", "109", "110",
	"111", "112", "113", "114", "115", "116", "117", "118", "119", "120",
	"121", "122", "123", "124", "125", "126", "127", "128", "129", "130",
	"131", "132", "133", "134", "135", "136", "137", "138", "139", "140",
	"141", "142", "143", "144", "145", "146", "147", "148", "149", "150",
	"151", "152", "153", "154", "155", "156", "157", "158", "159", "160",
	"161", "162", "163", "164", "165", "166", "167", "168", "169", "170",
	"171", "172", "173", "174", "175", "176", "177", "178", "179", "180",
	"181", "182", "183", "184", "185", "186", "187", "188", "189", "190",
	"191", "192", "193", "194", "195", "196", "197", "198", "199", "200"})

var defaultRenameRules = []model.RenameRule{

	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".E{}.", ValidNameTemplate: ".S01E{}.", ExitNameRegexps: []string{".*S\\d+\\.E\\d+\\..*", ".*S\\d+E00\\..*", ".*S\\d+\\.E00\\..*", ".*\\.E00\\..*"}},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".E0{}.", ValidNameTemplate: ".S01E0{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: "[E0{}", ValidNameTemplate: "[S01E0{}"},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: "[E{}", ValidNameTemplate: "[S01E{}"},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".E00{}.", ValidNameTemplate: ".S01E00{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " E{} ", ValidNameTemplate: " S01E{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " E0{} ", ValidNameTemplate: " S01E0{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " EP0{} ", ValidNameTemplate: " S01EP0{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " E00{} ", ValidNameTemplate: " S01E00{} "},

	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".e{}.", ValidNameTemplate: ".S01E{}.", ExitNameRegexps: []string{".*S\\d+\\.e\\d+\\..*", ".*S\\d+e00\\..*", ".*S\\d+\\.e00\\..*", ".*\\.e00\\..*"}},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".e0{}.", ValidNameTemplate: ".S01E0{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: "[e0{}", ValidNameTemplate: "[S01E0{}"},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: "[e{}", ValidNameTemplate: "[S01E{}"},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".e00{}.", ValidNameTemplate: ".S01E00{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " e{} ", ValidNameTemplate: " S01E{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " e0{} ", ValidNameTemplate: " S01E0{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " e00{} ", ValidNameTemplate: " S01E00{} "},

	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " EP0{} ", ValidNameTemplate: " S01EP0{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".EP0{}.", ValidNameTemplate: ".S01EP0{}.", ExitNameRegexps: []string{".*S\\d+\\.EP\\d+\\..*", ".*S\\d+EP00\\..*", ".*S\\d+\\.EP00\\..*", ".*\\.EP00\\..*"}},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".EP{}.", ValidNameTemplate: ".S01EP{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " EP{} ", ValidNameTemplate: " S01EP{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".EP{}.", ValidNameTemplate: ".S01EP{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".EP0{}.", ValidNameTemplate: ".S01EP0{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".EP00{}.", ValidNameTemplate: ".S01EP00{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".EP00{}.", ValidNameTemplate: ".S01EP00{}."},

	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " ep0{} ", ValidNameTemplate: " S01EP0{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".ep0{}.", ValidNameTemplate: ".S01EP0{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".ep{}.", ValidNameTemplate: ".S01EP{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " ep{} ", ValidNameTemplate: " S01EP{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".ep{}.", ValidNameTemplate: ".S01EP{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".ep0{}.", ValidNameTemplate: ".S01EP0{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".ep00{}.", ValidNameTemplate: ".S01EP00{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".ep00{}.", ValidNameTemplate: ".S01EP00{}."},

	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " Ep0{} ", ValidNameTemplate: " S01EP0{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".Ep0{}.", ValidNameTemplate: ".S01EP0{}.", ExitNameRegexps: []string{".*S\\d+\\.Ep0\\d+\\..*", ".*S\\d+Ep0\\..*", ".*S\\d+\\.Ep0\\..*", ".*\\.Ep0\\..*"}},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".Ep{}.", ValidNameTemplate: ".S01EP{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: " Ep{} ", ValidNameTemplate: " S01EP{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".Ep{}.", ValidNameTemplate: ".S01EP{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".Ep0{}.", ValidNameTemplate: ".S01EP0{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".Ep00{}.", ValidNameTemplate: ".S01EP00{}."},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: ".Ep00{}.", ValidNameTemplate: ".S01EP00{}."},

	{SeriesNums: defaultChineseSeriesNums, InvalidNameTemplate: "第{}集", ValidNameTemplate: "S01E{} "},
	{SeriesNums: defaultArabicSeriesNums, InvalidNameTemplate: "{}.ts", ValidNameTemplate: "S01E{}.ts"},
}
