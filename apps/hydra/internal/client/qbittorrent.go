package client

import (
	"context"
	"github.com/pen1120/qbapi"
	conf "github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/pkg/errors"
	"strconv"
	"time"
)

func InitQBClient(optFuncs ...DownloaderOptionFunc) (*qbapi.QBAPI, error) {
	opt := &conf.DownloaderOption{}
	for _, f := range optFuncs {
		f(opt)
	}

	var opts []qbapi.Option
	opts = append(opts, qbapi.WithAuth(opt.Username, opt.Password))
	opts = append(opts, qbapi.WithHost(opt.DSN))
	if opt.SSL {
		opts = append(opts, qbapi.WithSSL())
	}
	opts = append(opts, qbapi.WithTimeout(time.Second*time.Duration(opt.Timeout)))
	qbClient, err := qbapi.NewAPI(opts...)

	if err != nil {
		return nil, err
	}

	if err := qbClient.Login(context.Background()); err != nil {
		return nil, errors.WithMessage(err, "ping qbittorrent客户端失败")
	}

	return qbClient, nil
}

type DownloaderOptionFunc func(option *conf.DownloaderOption)

func WithUsername(username string) DownloaderOptionFunc {
	return func(option *conf.DownloaderOption) {
		option.Username = username
	}
}

func WithSSL() DownloaderOptionFunc {
	return func(option *conf.DownloaderOption) {
		option.SSL = true
	}
}

func WithPassword(password string) DownloaderOptionFunc {
	return func(option *conf.DownloaderOption) {
		option.Password = password
	}
}

func WithDSN(dsn string) DownloaderOptionFunc {
	return func(option *conf.DownloaderOption) {
		option.DSN = dsn
	}
}

func WithTimeout(timeout int) DownloaderOptionFunc {
	return func(option *conf.DownloaderOption) {
		option.Timeout = timeout
	}
}

func BuildDownloaderOptionFuncs(i conf.Client) []DownloaderOptionFunc {
	var funcs []DownloaderOptionFunc
	funcs = append(funcs, WithUsername(i.Username))
	funcs = append(funcs, WithPassword(i.Password))
	if i.HTTPS {
		funcs = append(funcs, WithDSN("https://"+i.Host+":"+strconv.Itoa(int(i.Port))))
		funcs = append(funcs, WithSSL())
	} else {
		funcs = append(funcs, WithDSN("http://"+i.Host+":"+strconv.Itoa(int(i.Port))))
	}

	return funcs
}
