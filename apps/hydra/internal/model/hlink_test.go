package model

import (
	"regexp"
	"sync"
	"testing"
)

func TestHlinkConfiguration_MatchExcludeRegx(t *testing.T) {
	type fields struct {
		LinkType            string
		PathsMapping        []HlinkPathMapping
		Includes            []string
		Excludes            []string
		ExcludeFolderNames  []string
		ExcludeNameRegex    []string
		KeepDirStruct       bool
		MkdirIfSingle       bool
		OpenCache           bool
		DeleteDir           bool
		once                sync.Once
		onceExcludeNameRegs sync.Once
		onceFolder          sync.Once
		includes            map[string]bool
		excludes            map[string]bool
		excludeFolders      map[string]bool
		excludeNameRegs     map[string]regexp.Regexp
	}
	type args struct {
		path string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			fields: fields{
				ExcludeNameRegex: []string{
					"pt/\\.qbcache",
				},
			},
			args: args{path: "./ /volume2/pt/.qbcache/incomplete/三大队.The.Lonely.Warrior.S01.2023.2160p.WEB-DL.H265.EDR.DDP5.1-HHWEB/The.Lonely.Warrior.S01E02.2023.2160p.WEB-DL.H265.EDR.DDP5.1-HHWEB.mkv"},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &HlinkConfiguration{
				LinkType:            tt.fields.LinkType,
				PathsMapping:        tt.fields.PathsMapping,
				Includes:            tt.fields.Includes,
				Excludes:            tt.fields.Excludes,
				ExcludeFolderNames:  tt.fields.ExcludeFolderNames,
				ExcludeNameRegex:    tt.fields.ExcludeNameRegex,
				KeepDirStruct:       tt.fields.KeepDirStruct,
				MkdirIfSingle:       tt.fields.MkdirIfSingle,
				OpenCache:           tt.fields.OpenCache,
				DeleteDir:           tt.fields.DeleteDir,
				once:                tt.fields.once,
				onceExcludeNameRegs: tt.fields.onceExcludeNameRegs,
				onceFolder:          tt.fields.onceFolder,
				includes:            tt.fields.includes,
				excludes:            tt.fields.excludes,
				excludeFolders:      tt.fields.excludeFolders,
				excludeNameRegs:     tt.fields.excludeNameRegs,
			}
			if got := i.MatchExcludeRegx(tt.args.path); got != tt.want {
				t.Errorf("MatchExcludeRegx() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReplaceText(t *testing.T) {
	type args struct {
		input         string
		old           string
		new           string
		caseSensitive bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{args: args{input: "XaC_Unable", old: "_Unable", caseSensitive: false}, want: "XaC"},
		{args: args{input: "XaC_Unable", old: "_unable", caseSensitive: false}, want: "XaC"},
		{args: args{input: "XaC_Unable", old: "_Unable", caseSensitive: true}, want: "XaC"},
		{args: args{input: "XaC_Unable", old: "_unable", caseSensitive: true}, want: "XaC_Unable"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ReplaceText(tt.args.input, tt.args.old, tt.args.new, tt.args.caseSensitive); got != tt.want {
				t.Errorf("ReplaceText() = %v, want %v", got, tt.want)
			}
		})
	}
}
