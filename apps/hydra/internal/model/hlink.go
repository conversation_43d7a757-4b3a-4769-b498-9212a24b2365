package model

import (
	"fmt"
	"github.com/pkg/errors"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"unicode"
)

type PathLinkStatus struct {
	OriPath    string
	DestPath   string
	TotalNum   int
	ValidNum   int
	LinkedNum  int
	RenamedNum int
}

func (s PathLinkStatus) String() string {
	return fmt.Sprintf("总数:%d,有效文件:%d,新增链接:%d,路径:%s ",
		s.<PERSON>Num, s.ValidNum, s.LinkedNum, s.OriPath)
}

type RenameRule struct {
	SeriesNums          []string
	InvalidNameTemplate string
	ValidNameTemplate   string
	ExitNameRegexps     []string

	exitNameRegexps []*regexp.Regexp
	once            sync.Once
}

func (i *RenameRule) JudgeInvalidSeriesNames(path string) (oldName string, newName string, match bool, exit bool) {
	i.once.Do(func() {
		for _, r := range i.ExitNameRegexps {
			i.exitNameRegexps = append(i.exitNameRegexps, regexp.MustCompile(r))
		}
	})

	// path match regex
	for _, r := range i.exitNameRegexps {
		if r.MatchString(path) {
			return "", "", true, true
		}
	}

	for idx, seriesNum := range i.SeriesNums {
		invalidName := strings.ReplaceAll(i.InvalidNameTemplate, "{}", seriesNum)
		if strings.Contains(path, invalidName) {
			return invalidName, strings.ReplaceAll(i.ValidNameTemplate, "{}", strconv.Itoa(len(i.SeriesNums)-idx-1)), true, false
		}
	}
	return "", "", false, false
}

type HlinkPathMapping struct {
	OriPath        string            `json:"ori_path"`
	DestPath       string            `json:"dest_path"`
	Rename         bool              `json:"rename"`
	Priority       int               `json:"priority"`
	IncludeAllFile bool              `json:"include_all_file"`
	SizeLimitMB    int               `json:"size_limit_mb"`
	TextReplace    []TextReplaceRule `json:"text_replace"`
	LinuxACL       int               `json:"linux_acl"`
}

func (i *HlinkPathMapping) GetLinuxACL() int {
	if i.LinuxACL <= 0 {
		return 0755
	}
	return i.LinuxACL
}

func (i HlinkPathMapping) HasTextReplace() bool {
	return len(i.TextReplace) > 0
}

/*
*

when case_sensitive is true, replace text with case sensitive:
XaC_Unable,_Unable -> XaC
XaC_Unable,_nnable -> XaC_Unable

when case_sensitive is false, replace text with case insensitive:
XaC_Unable,_Unable -> XaC
XaC_Unable,_nnable -> XaC
*/
func (i HlinkPathMapping) ReplaceText(name string) string {
	for _, rule := range i.TextReplace {
		name = ReplaceText(name, rule.From, rule.To, rule.CaseSensitive)
	}
	return name
}

// ReplaceText 根据 case_sensitive 参数，使用字符遍历的方式进行大小写敏感或不敏感的替换
func ReplaceText(input, old, new string, caseSensitive bool) string {
	inputLen := len(input)
	oldLen := len(old)
	var result strings.Builder

	for i := 0; i < inputLen; {
		// 判断是否能匹配到 old 字符串
		if i+oldLen <= inputLen && compareSubstring(input[i:i+oldLen], old, caseSensitive) {
			// 如果匹配成功，替换为 new
			result.WriteString(new)
			i += oldLen
		} else {
			// 不匹配时保留原字符
			result.WriteByte(input[i])
			i++
		}
	}

	return result.String()
}

// compareSubstring 比较两个字符串，是否在 case_sensitive 下相等
func compareSubstring(s1, s2 string, caseSensitive bool) bool {
	if len(s1) != len(s2) {
		return false
	}
	for i := range s1 {
		if caseSensitive {
			// 大小写敏感比较
			if s1[i] != s2[i] {
				return false
			}
		} else {
			// 大小写不敏感比较
			if unicode.ToLower(rune(s1[i])) != unicode.ToLower(rune(s2[i])) {
				return false
			}
		}
	}
	return true
}

type TextReplaceRule struct {
	From          string `json:"from"`
	To            string `json:"to"`
	CaseSensitive bool   `json:"case_sensitive"`
}

type HlinkConfiguration struct {
	LinkType           string             `json:"link_type"`
	PathsMapping       []HlinkPathMapping `json:"paths_mapping"`
	Includes           []string           `json:"includes"`
	Excludes           []string           `json:"excludes"`
	ExcludeFolderNames []string           `json:"exclude_folder_names"`
	ExcludeNameRegex   []string           `json:"exclude_name_regex"`
	KeepDirStruct      bool               `json:"keep_dir_struct"`
	MkdirIfSingle      bool               `json:"mkdir_if_single"`
	OpenCache          bool               `json:"open_cache"`
	DeleteDir          bool               `json:"delete_dir"`

	once                sync.Once
	onceExcludeNameRegs sync.Once
	onceFolder          sync.Once

	includes        map[string]bool
	excludes        map[string]bool
	excludeFolders  map[string]bool
	excludeNameRegs map[string]regexp.Regexp
}

type LinkFunc func(string, string) error

func (i *HlinkConfiguration) GetLinkFunc() LinkFunc {
	if i.LinkType == "" {
		return os.Symlink
	}
	if i.LinkType == "hard" {
		return os.Link
	}
	return os.Symlink
}

func (i *HlinkConfiguration) Validate() error {
	if len(i.PathsMapping) == 0 {
		return errors.New("\"没有配置路径映射\"")
	}
	return nil
}

// MatchFileSuffix 大小写不敏感
func (i *HlinkConfiguration) MatchFileSuffix(fileSuffix string) bool {
	i.once.Do(func() {
		i.includes = make(map[string]bool)
		for _, v := range i.Includes {
			i.includes[strings.ToLower(v)] = true
		}
		i.excludes = make(map[string]bool)
		for _, v := range i.Excludes {
			i.excludes[strings.ToLower(v)] = true
		}
	})
	return i.includes[strings.ToLower(fileSuffix)] && !i.excludes[strings.ToLower(fileSuffix)]
}

// MatchExcludeFolder 大小写不敏感
func (i *HlinkConfiguration) MatchExcludeFolder(fileDir string) bool {
	i.onceFolder.Do(func() {
		i.excludeFolders = make(map[string]bool)
		for _, v := range i.ExcludeFolderNames {
			i.excludeFolders[strings.ToLower(v)] = true
		}
	})
	for _, token := range strings.Split(fileDir, "/") {
		if i.excludeFolders[strings.ToLower(token)] {
			return true
		}
	}
	return false
}

func (i *HlinkConfiguration) MatchExcludeRegx(path string) bool {
	i.onceExcludeNameRegs.Do(func() {
		i.excludeNameRegs = make(map[string]regexp.Regexp)
		for _, v := range i.ExcludeNameRegex {
			i.excludeNameRegs[v] = *regexp.MustCompile(v)
		}
	})
	for _, reg := range i.excludeNameRegs {
		if reg.MatchString(path) {
			return true
		}
	}
	return false
}

func (i *HlinkConfiguration) IteratePathsMapping() []HlinkPathMapping {
	sort.Slice(i.PathsMapping, func(k, j int) bool {
		return i.PathsMapping[k].Priority > i.PathsMapping[j].Priority
	})
	return i.PathsMapping
}
