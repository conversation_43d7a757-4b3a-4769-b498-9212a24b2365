package model

import (
	"net/url"
	"os"
	"strings"
	"time"
)

var (
	PathSeparator = string(os.PathSeparator)
)

type BencodeTorrent struct {
	Announce  string `bencode:"announce"`
	CreatedBy string `bencode:"created by,omitempty"`
	CreatedAt int    `bencode:"creation date,omitempty"`
	Comment   string `bencode:"comment"`
	Encoding  string `bencode:"encoding"`
	Info      struct {
		Files       []TorrentFile `bencode:"files"`
		Name        string        `bencode:"name"`
		Pieces      string        `bencode:"pieces"`
		PieceLength uint64        `bencode:"piece length"`
		Private     int           `bencode:"private"`
		Source      string        `bencode:"source"`
	} `bencode:"info"`
}

func (i BencodeTorrent) ToTorrentMatcher(ap string) TorrentMatcher {
	return TorrentMatcher{
		Files:        i.Info.Files,
		Name:         i.Info.Name,
		Announce:     i.Announce,
		AbsolutePath: ap,
	}
}

type TorrentFile struct {
	Length uint64   `bencode:"length"`
	Path   []string `bencode:"path"`
}

func (i TorrentFile) BuildJoinedPath() string {
	return strings.Join(i.Path, PathSeparator)
}

func (i *TorrentMatcher) AppendFile(tf TorrentFile) {
	if i.Files == nil {
		i.Files = make([]TorrentFile, 0)
	}
	i.Files = append(i.Files, tf)
}

type TorrentMatcher struct {
	Files        []TorrentFile
	Announce     string
	Name         string
	AbsolutePath string
}

func (i TorrentMatcher) HashCode() string {
	return ""
}

// IsMatch 判断当前torrent与数据目录是否一致
func (i TorrentMatcher) IsMatch(j TorrentMatcher) bool {
	return false
}

type TreeNode struct {
	FullPath  string
	Name      string
	Childrens []*TreeNode
}

func (i *TreeNode) AddChildren(t *TreeNode) {
	i.Childrens = append(i.Childrens, t)
}

type SendParam struct {
	TorrentDir               string
	DownloadDir              string
	AliasDownloadDir         string
	LogLevel                 string
	MaxDepth                 int
	ThreadNum                int
	ConfigFile               string
	BreakNum                 int
	IgnoreFilesNameMatchRule bool
	Announce                 string
	DryRun                   bool
}

type TransferParam struct {
	SafeMode                             bool
	IgnoreTrackers                       []string
	TransferSizeLimit                    int
	IgnoreSavePaths                      []string
	RetentionDays                        int
	DryRun                               bool
	TorrentSizeLimit                     int
	TrToTr                               bool
	RemoveTransmissionDownloadingTorrent bool
}

type ProxyParam struct {
	ListenAddress string
	Socks5Address string
}

func (i TransferParam) ShouldIgnoreSavePath(savePath string) bool {
	if len(i.IgnoreSavePaths) == 0 {
		return false
	}
	for _, p := range i.IgnoreSavePaths {
		if strings.Contains(strings.ToLower(savePath), strings.ToLower(p)) {
			return true
		}
	}
	return false
}

// IsRetentionTorrent 是否还在保留时间内
func (i TransferParam) IsRetentionTorrent(completeOnTimestamp int) bool {
	ut := int64(completeOnTimestamp) + (int64(i.RetentionDays) * 3600 * 24)
	rt := time.Now().Unix()
	return ut > rt
}

func (i TransferParam) IsSmallTorrent(sizeInByte uint64) bool {
	return i.TorrentSizeLimit > 0 && sizeInByte < uint64(i.TorrentSizeLimit)
}

func (i SendParam) IsValidAnnounce(announce string) bool {
	if announce == "" {
		return false
	}
	if i.Announce == "" {
		return true
	}
	u, err := url.Parse(announce)
	if err != nil {
		return false
	}
	hostname := u.Hostname()
	return strings.Contains(hostname, i.Announce)
}

type TMDBParam struct {
	APIKey             string
	ProxySocks5Address string
	ID                 int64
}

type LinkParam struct {
	LogLevel   string
	ConfigFile string
	APIKey     string
	ProxyURL   string
}

type HlinkParam struct {
	LogLevel    string
	ConfigFile  string
	TimesBefore string
	PathPattern string
}

func (h HlinkParam) GetLogLevel() string {
	return h.LogLevel
}
func (h HlinkParam) GetConfigFile() string {
	return h.ConfigFile
}
func (h HlinkParam) GetTimesBefore() string {
	return h.TimesBefore
}
func (h HlinkParam) GetPathPattern() string {
	return h.PathPattern
}

type BuildIndexParam struct {
	LogLevel   string
	ConfigFile string
}

type SearchParam struct {
	LogLevel   string
	ConfigFile string
	Query      string
}
