package config

import (
	"github.com/penwyp/hydra/apps/hydra/internal/json"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/pkg/errors"
	"os"
)

var DefaultConfig = &Configuration{
	TransmissionClient: Client{
		Host:     "*************",
		Port:     9091,
		Username: "nas",
		Password: "acce-s6se/nas",
		HTTPS:    false,
	},
	TransmissionSmallClient: Client{
		Host:     "*************",
		Port:     9091,
		Username: "nas",
		Password: "acce-s6se/nas",
		HTTPS:    false,
	},
	QBitTorrentClient: Client{
		Host:     "*************",
		Port:     8085,
		Username: "nas",
		Password: "acce-s6se/nas",
		HTTPS:    true,
	},
	QBSSD: Client{
		Host:     "*************",
		Port:     8085,
		Username: "nas",
		Password: "acce-s6se/nas",
		HTTPS:    false,
	},
}

func ParseConfig(file string) (*Configuration, error) {
	c := Configuration{}
	content, err := os.ReadFile(file)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := json.Unmarshal(content, &c); err != nil {
		return nil, errors.WithStack(err)
	}
	return &c, nil
}

func ParseHlinkConfig(file string) (*model.HlinkConfiguration, error) {
	c := model.HlinkConfiguration{}
	content, err := os.ReadFile(file)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if err := json.Unmarshal(content, &c); err != nil {
		return nil, errors.WithStack(err)
	}
	return &c, nil
}
