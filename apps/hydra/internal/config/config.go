package config

import (
	"fmt"
	"net/url"
)

type Configuration struct {
	TransmissionClient      Client `json:"transmission_client"`
	TransmissionSmallClient Client `json:"transmission_small_client"`
	QBitTorrentClient       Client `json:"qbbittorrent_client"`
	QBSSD                   Client `json:"qbbittorrent_ssd_client"`
}

type Client struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Host     string `json:"host"`
	Port     uint16 `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	HTTPS    bool   `json:"https"`
}

type ClientList []Client

type DownloaderOption struct {
	Username string
	Password string
	DSN      string
	Timeout  int
	SSL      bool
}

func (i Client) GetTransmissionDSN() string {
	//"***********************************/transmission/rpc"
	return fmt.Sprintf("http://%s:%s@%s:%d/transmission/rpc", i.Username, url.QueryEscape(i.Password), i.Host, i.Port)
}

func (i Client) GetServiceUrl() string {
	if i.Type == "" {
		return fmt.Sprintf("http://%s:%d", i.Host, i.Port)
	} else {
		return fmt.Sprintf("%s://%s:%d", i.Type, i.Host, i.Port)
	}
}
