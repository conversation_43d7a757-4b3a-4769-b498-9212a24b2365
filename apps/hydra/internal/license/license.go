package license

import (
	"context"
	"fmt"
	"github.com/denisbrodbeck/machineid"
	"github.com/dgrijalva/jwt-go"
	"log"
	"time"
)

const (
	FLAG_KEY            = "key"
	FLAG_LICENSE        = "license"
	APP_NAME     string = "laixi_win"
)

var jwtSecret = []byte("2d1e4682-c052-4d45-9a9b-a67892affc68")

type LicenseInfo struct {
	MachineID  string
	ExpireTime int64
}

// GenerateMachineID generate license from local machine
func GenerateMachineID() (string, error) {
	machineID, err := machineid.ProtectedID(APP_NAME)
	if err != nil {
		return "", err
	}
	return machineID, nil
}

// Claim是一些实体（通常指的用户）的状态和额外的元数据
type Claims struct {
	MachineID string `json:"MachineID"`
	jwt.StandardClaims
}

func GenerateToken(machineId string, validateDayDuration int) (string, error) {
	//设置token有效时间
	nowTime := time.Now()
	expireTime := nowTime.Add(time.Duration(validateDayDuration*24) * time.Hour)

	claims := Claims{
		MachineID: machineId,
		StandardClaims: jwt.StandardClaims{
			// 过期时间
			ExpiresAt: expireTime.Unix(),
			// 指定token发行人
			Issuer: "licenser",
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	//该方法内部生成签名字符串，再用于获取完整、已签名的token
	token, err := tokenClaims.SignedString(jwtSecret)
	return token, err
}

// VerifyLicense verify license if expired
func VerifyLicense(ctx context.Context, tokenStr string) (bool, error) {
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("invalid signing method: %v", token.Header["alg"])
		}
		return jwtSecret, nil
	})
	if err != nil {
		return false, err
	}
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		localMachineID, err := machineid.ProtectedID(APP_NAME)
		if err != nil {
			log.Println(fmt.Sprintf("get local machine id failed. err:%v", err))
			return false, err
		}
		machineID, _ := claims["MachineID"]
		if machineID != localMachineID {
			err = fmt.Errorf(fmt.Sprintf("invalid machine id. machine id -> %s", machineID))
			log.Println(err)
			return false, err
		}
		expireTime := int64(claims["exp"].(float64))
		if expireTime < time.Now().Unix() {
			err = fmt.Errorf(fmt.Sprintf("license expired. expired time:%s", time.Unix(expireTime, 0).Format("2006-01-02 15:04:05")))
			return false, err
		}
		return true, nil
	}
	x := tokenStr
	if len(x) > 20 {
		x = x[:20]
	}
	err = fmt.Errorf(fmt.Sprintf("invalid license. license -> %s", x))
	log.Println(err)
	return false, err
}
