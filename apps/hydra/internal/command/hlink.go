package command

import (
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/pkg/hlink"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"os"
)

func NewHlinkCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "hlink",
		Short: "硬链接",
		Long:  fmt.Sprintf(""),
		Run:   doHlinkProcess,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&hlinkConfigFile, "configFile", "c", "/volume2/service/dat/hydra/etc/hlink/hlink_go.json", "-c /volume2/service/dat/hydra/etc/hlink/hlink_go.json")
	cmd.PersistentFlags().StringVarP(&timesBefore, "timesBefore", "t", "1d", "-t 1m 1d 1h, all means all time")
	cmd.PersistentFlags().StringVarP(&pathPattern, "pathPattern", "p", "", "pattern for match path")
	return cmd
}

func doHlinkProcess(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	param := buildHlinkParam()
	hlinkConfig, err := config.ParseHlinkConfig(hlinkConfigFile)
	if err != nil {
		log.Logger.Panic("解析配置失败", zap.Error(err), zap.String("conf", hlinkConfigFile))
	}
	log.Logger.Info("工作信息", zap.Any("param", param))

	if validateErr := hlinkConfig.Validate(); validateErr != nil {
		log.Logger.Panic("配置校验失败"+param.GetConfigFile(), zap.Error(validateErr))
	}

	linkStatuses := hlink.HardLinkFiles(param, hlinkConfig)
	hlink.DisplayLinkResults(linkStatuses, os.Stdout)
}

func buildHlinkParam() *model.HlinkParam {
	param := &model.HlinkParam{
		LogLevel:    logLevel,
		ConfigFile:  hlinkConfigFile,
		TimesBefore: timesBefore,
		PathPattern: pathPattern,
	}
	return param
}
