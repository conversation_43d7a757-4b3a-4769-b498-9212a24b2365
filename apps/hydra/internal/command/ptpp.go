package command

import (
	"fmt"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
)

func NewPtppCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ptpp",
		Short: "chrome插件ptpp，输出下载路径",
		Long:  fmt.Sprintf(""),
		Run:   walkPtDownloadDirs,
	}
	cmd.PersistentFlags().StringVarP(&walkDir, "walkDir", "w", "/volume2/pt", "")
	return cmd
}

func walkPtDownloadDirs(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	walkErr := filepath.WalkDir(walkDir,
		func(filePath string, info fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			// ignore some dirs
			if !info.IsDir() || info.Name() == "." || info.Name() == ".." {
				return nil
			}
			relativePath := strings.TrimLeft(filePath, walkDir)
			if strings.Count(relativePath, string(os.PathSeparator)) >= 1 || matchIgnorePathName(removePath) {
				return nil
			}
			walkPath := getWalkDepth(relativePath)
			log.Logger.Debug("walk destination", zap.String("filePath", filePath), zap.Int("walkPath", walkPath))
			walkDestinationPath(filePath, walkPath)
			return nil
		})
	if walkErr != nil {
		log.Logger.Error(walkErr.Error())
	}

	log.Logger.Info("处理完成")
}

func walkDestinationPath(rootDir string, walkDepth int) {
	rootPathSeparatorNum := strings.Count(rootDir, string(os.PathSeparator))
	err := filepath.WalkDir(rootDir,
		func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			if matchIgnorePathName(path) {
				return nil
			}
			if d.IsDir() && strings.Count(path, string(os.PathSeparator)) == walkDepth+rootPathSeparatorNum {
				log.Logger.Debug("walking", zap.String("rootDir", rootDir), zap.String("path", path), zap.Int("walkDepth", walkDepth), zap.Int("rootPathSeparatorNum", rootPathSeparatorNum))
				fmt.Println(path)
				return nil
			}
			return nil
		})
	if err != nil {
		return
	}
}

func getWalkDepth(relativePath string) int {
	switch relativePath {
	case "演唱会", "动画", "纪录片", "电子书":
		return 0
	case "电影", "电视剧", "电视节目", "etc", "入党申请书", "其他", "R18":
		return 1
	default:
		return 0
	}
}

func matchIgnorePathName(path string) bool {
	return strings.Contains(path, "#recycle") || strings.Contains(path, "@eaDir")
}
