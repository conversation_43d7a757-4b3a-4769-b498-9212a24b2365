package command

import (
	"context"
	"fmt"
	"github.com/hekmon/transmissionrpc/v3"
	"github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/hardcode"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"strings"
)

func NewAfterTransferCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "after_transfer",
		Short: "移动辅种后，开启暂停任务",
		Long:  fmt.Sprintf(""),
		Run:   doAfterTransferStaffs,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&configFile, "configFile", "c", "/volume2/service/bin/hydra/config.json", "-c etc/conf/config.json")
	cmd.PersistentFlags().BoolVarP(&dryRun, "dryRun", "r", false, "仅仅解析，不发送")
	return cmd
}

func doAfterTransferStaffs(cmd *cobra.Command, args []string) {
	ctx := context.Background()
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
	config, err := config.ParseConfig(configFile)
	if err != nil {
		log.Logger.Panic("解析配置失败", zap.Error(err), zap.String("conf", configFile))
	}
	log.Logger.Info("工作信息", zap.Any("config", config))

	log.Logger.Info("初始化客户端")
	_, trClient, trsClient, err := hardcode.InitClients(ctx, config)
	if err != nil {
		log.Logger.Panic("初始化客户端失败", zap.Error(err))
	}

	log.Logger.Info("获取TR种子列表")
	trTorrents, err := trClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Panic("获取TR种子列表", zap.Error(err))
	}

	for _, trTorrent := range trTorrents {
		isRemoved := remoteTorrent(trTorrent, trClient)
		if isRemoved {
			continue
		}
		if *trTorrent.Status == transmissionrpc.TorrentStatusStopped {
			percentDone := *trTorrent.PercentDone * 100
			if percentDone >= 0 && percentDone < 100 {
				log.Logger.Info("跳过种子", zap.String("name", *trTorrent.Name), zap.Any("进度", percentDone))
				continue
			}
			if startErr := trClient.TorrentStartNowIDs(ctx, []int64{*trTorrent.ID}); startErr != nil {
				log.Logger.Error("辅种TR种子", zap.String("name", *trTorrent.Name), zap.Error(startErr))
				continue
			}
			log.Logger.Debug("辅种TR种子", zap.String("name", *trTorrent.Name), zap.Any("percentDone", percentDone))
		}

	}

	log.Logger.Info("获取TR-SMALL种子列表")
	trTorrents, err = trsClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Panic("获取TR-SMALL种子列表", zap.Error(err))
	}

	for _, trTorrent := range trTorrents {
		isRemoved := remoteTorrent(trTorrent, trsClient)
		if isRemoved {
			continue
		}
		if *trTorrent.Status == transmissionrpc.TorrentStatusStopped {
			percentDone := *trTorrent.PercentDone * 100
			if percentDone >= 0 && percentDone < 100 {
				log.Logger.Info("跳过种子", zap.String("name", *trTorrent.Name), zap.Any("进度", percentDone))
				continue
			}
			if startErr := trsClient.TorrentStartNowIDs(ctx, []int64{*trTorrent.ID}); startErr != nil {
				log.Logger.Error("辅种TR种子", zap.String("name", *trTorrent.Name), zap.Error(startErr))
				continue
			}
			log.Logger.Debug("辅种TR-SMALL种子", zap.String("name", *trTorrent.Name))
		}

	}
	log.Logger.Info("-------------------")
}

func remoteTorrent(trTorrent transmissionrpc.Torrent, client *transmissionrpc.Client) bool {
	if hasToDelete, deleteMessage := judgeDeleteInfo(trTorrent); hasToDelete {
		log.Logger.Info("准备删除TR种子", zap.String("deleteMessage", deleteMessage), zap.String("name", *trTorrent.Name))
		removeErr := client.TorrentRemove(context.TODO(), transmissionrpc.TorrentRemovePayload{
			IDs:             []int64{*trTorrent.ID},
			DeleteLocalData: false,
		})
		if removeErr != nil {
			log.Logger.Error("删除TR种子", zap.String("name", *trTorrent.Name), zap.Error(removeErr))
			return false
		}
		return true
	}
	return false
}

func judgeDeleteInfo(trTorrent transmissionrpc.Torrent) (bool, string) {
	if *trTorrent.Status == transmissionrpc.TorrentStatusDownload {
		return true, "TR种子仅需辅种，不需要下载"
	}

	if *trTorrent.Status == transmissionrpc.TorrentStatusStopped && *trTorrent.PercentDone == 0 {
		return true, "TR种子已经暂停/停止，且进度为0"
	}

	if strings.Contains(*trTorrent.ErrorString, "种子尚未上传或者已经被删除") ||
		strings.Contains(*trTorrent.ErrorString, "orrent not registered with this tracker") ||
		strings.Contains(*trTorrent.ErrorString, "No data found") {
		return true, *trTorrent.ErrorString
	}
	return false, ""
}
