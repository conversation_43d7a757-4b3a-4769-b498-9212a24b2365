package command

import (
	"fmt"
	"github.com/KnutZuidema/go-qbittorrent"
	"github.com/penwyp/hydra/apps/hydra/internal/json"
	"github.com/penwyp/hydra/shared/log"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"path"
	"strings"
)

func NewQBCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "qb",
		Short: "增加分类",
		Long:  fmt.Sprintf(""),
		Run:   TryQB,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&configFile, "configFile", "c", "/volume2/service/bin/hydra/config.json", "-c etc/conf/config.json")
	return cmd
}

type category struct {
	Name  string `json:"name"`
	Path  string `json:"path"`
	QBTag string `json:"qbtag"`
}

const categoryJSON = `
[
  {
    "name": "other-study",
    "path": "其他/学习",
    "qbtag": "其他 - 学习"
  },
  {
    "name": "other-binary",
    "path": "其他/二进制",
    "qbtag": "其他 - 二进制"
  },
  {
    "name": "other-live",
    "path": "演唱会",
    "qbtag": "演唱会"
  },
  {
    "name": "动画",
    "path": "动画",
    "qbtag": "动画"
  },
  {
    "name": "other-documentary",
    "path": "纪录片",
    "qbtag": "纪录片"
  },
  {
    "name": "other-music",
    "path": "其他/音乐",
    "qbtag": "其他 - 音乐"
  },
  {
    "name": "r18-hk",
    "path": "R18/香港",
    "qbtag": "R18 - 香港"
  },
  {
    "name": "movie-china",
    "path": "电影/中国",
    "qbtag": "电影 - 中国"
  },
  {
    "name": "movie-russia",
    "path": "电影/俄罗斯",
    "qbtag": "电影 - 俄罗斯"
  },
  {
    "name": "movie-india",
    "path": "电影/印度",
    "qbtag": "电影 - 印度"
  },
  {
    "name": "movie-taiwan",
    "path": "电影/台湾",
    "qbtag": "电影 - 台湾"
  },
  {
    "name": "movie-japan",
    "path": "电影/日本",
    "qbtag": "电影 - 日本"
  },
  {
    "name": "movie-europe",
    "path": "电影/欧洲",
    "qbtag": "电影 - 欧洲"
  },
  {
    "name": "movie-america",
    "path": "电影/美国",
    "qbtag": "电影 - 美国"
  },
  {
    "name": "movie-thailand",
    "path": "电影/泰国",
    "qbtag": "电影 - 泰国"
  },
  {
    "name": "movie-korea",
    "path": "电影/韩国",
    "qbtag": "电影 - 韩国"
  },
  {
    "name": "movie-hk",
    "path": "电影/香港",
    "qbtag": "电影 - 香港"
  },
  {
    "name": "series-china",
    "path": "电视剧/中国",
    "qbtag": "电视剧 - 中国"
  },
  {
    "name": "series-america",
    "path": "电视剧/美国",
    "qbtag": "电视剧 - 美国"
  },
  {
    "name": "series-england",
    "path": "电视剧/英国",
    "qbtag": "电视剧 - 英国"
  },
  {
    "name": "tvshow-china",
    "path": "电视节目/中国",
    "qbtag": "电视节目 - 中国"
  },
  {
    "name": "tvshow-america",
    "path": "电视节目/美国",
    "qbtag": "电视节目 - 美国"
  },
  {
    "name": "tvshow-england",
    "path": "电视节目/英国",
    "qbtag": "电视节目 - 英国"
  },
  {
    "name": "rudang-pdf",
    "path": "/volume2/personal/入党申请书/PDF",
    "qbtag": "入党 - PDF"
  }
]
`

func TryQB(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	cs := make([]category, 0)
	if err := json.Unmarshal([]byte(categoryJSON), &cs); err != nil {
		log.Logger.Panic("登入失败分类", zap.Error(err))
	}

	logger := logrus.New()
	client := qbittorrent.NewClient("http://192.168.6.1:8085", logger)
	if err := client.Login("nas", "acce-s6se/nas"); err != nil {
		log.Logger.Panic("登入失败", zap.Error(err))
	}

	existCs, err := client.Torrent.GetCategories()
	if err != nil {
		log.Logger.Panic("获取分类失败", zap.Error(err))
	}
	eixstCCs := make([]string, 0)
	for _, existC := range existCs {
		eixstCCs = append(eixstCCs, existC.Name)
	}
	if err := client.Torrent.RemoveCategory(eixstCCs); err != nil {
		log.Logger.Error("删除分类失败", zap.Error(err))
	}
	log.Logger.Info("删除分类成功", zap.Error(err))
	for _, c := range cs {

		var p string
		if strings.Index(c.Path, "/volume2/") == 0 {
			p = c.Path
		} else {
			p = path.Join("/volume2/pt/", c.Path)
		}
		log.Logger.Info(p)

		if err := client.Torrent.AddCategory(c.QBTag, p); err != nil {
			log.Logger.Error("创建分类失败", zap.Error(err))
		}
	}

}
