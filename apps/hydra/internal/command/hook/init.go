package hook

import "time"

var query string
var logLevel string
var barkKey string
var dbFile string
var port int

// Server 结构体表示服务器信息
type Server struct {
	Name    string `json:"Name"`
	Id      string `json:"Id"`
	Version string `json:"Version"`
}

// ExternalUrl 结构体表示外部链接
type ExternalUrl struct {
	Name string `json:"Name"`
	Url  string `json:"Url"`
}

// ProviderIds 结构体表示提供商的 ID
type ProviderIds struct {
	Metatube       string `json:"metatube"`
	Javscraper     string `json:"javscraper"`
	JavscraperJson string `json:"javscraper-json"`
	JavscraperUrl  string `json:"javscraper-url"`
}

// Item 结构体表示项目信息
type Item struct {
	Name                    string            `json:"Name"`
	OriginalTitle           string            `json:"OriginalTitle"`
	ServerId                string            `json:"ServerId"`
	Id                      string            `json:"Id"`
	DateCreated             time.Time         `json:"DateCreated"`
	Container               string            `json:"Container"`
	SortName                string            `json:"SortName"`
	PremiereDate            time.Time         `json:"PremiereDate"`
	ExternalUrls            []ExternalUrl     `json:"ExternalUrls"`
	Path                    string            `json:"Path"`
	OfficialRating          string            `json:"OfficialRating"`
	Overview                string            `json:"Overview"`
	Taglines                []string          `json:"Taglines"`
	Genres                  []string          `json:"Genres"`
	CommunityRating         float64           `json:"CommunityRating"`
	RunTimeTicks            int64             `json:"RunTimeTicks"`
	Size                    int64             `json:"Size"`
	FileName                string            `json:"FileName"`
	Bitrate                 int               `json:"Bitrate"`
	ProductionYear          int               `json:"ProductionYear"`
	RemoteTrailers          []string          `json:"RemoteTrailers"`
	ProviderIds             ProviderIds       `json:"ProviderIds"`
	IsFolder                bool              `json:"IsFolder"`
	ParentId                string            `json:"ParentId"`
	Type                    string            `json:"Type"`
	Studios                 []string          `json:"Studios"`
	GenreItems              []string          `json:"GenreItems"`
	TagItems                []string          `json:"TagItems"`
	PrimaryImageAspectRatio float64           `json:"PrimaryImageAspectRatio"`
	ImageTags               map[string]string `json:"ImageTags"`
	BackdropImageTags       []string          `json:"BackdropImageTags"`
	MediaType               string            `json:"MediaType"`
	Width                   int               `json:"Width"`
	Height                  int               `json:"Height"`
}

// Notification 结构体表示通知信息
type Notification struct {
	Title       string    `json:"Title"`
	Description string    `json:"Description"`
	Date        time.Time `json:"Date"`
	Event       string    `json:"Event"`
	Server      Server    `json:"Server"`
	Item        *Item     `json:"Item,omitempty"` // Item 是可选的，只有在特定事件中才会出现
}
