package hook

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/penwyp/hydra/apps/hydra/pkg/remove"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"io"
	"net/http"
	"path"
	"strings"
	"time"
)

var dbConn *sql.DB

func NewEmbyAVCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "emby_av",
		Short: "emby_av",
		Long:  fmt.Sprintf(""),
		Run:   doEmbyAvHook,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&barkKey, "barkKey", "k", "eyANTXqT2poiMTYrEkjMPk_not_exist", "Bark key to send notification")
	cmd.PersistentFlags().StringVarP(&dbFile, "dbFile", "d", "/config/hydra_hook.db", "path to db file")
	cmd.PersistentFlags().IntVarP(&port, "port", "p", 7832, "listen port")
	return cmd
}

func doEmbyAvHook(cmd *cobra.Command, args []string) {

	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	dbConn = initSQLiteDB()

	r := gin.Default()

	r.GET("/ping", pingHandler)
	r.POST("/hook", hookHandler)

	log.Logger.Info(fmt.Sprintf("Starting server on 0.0.0.0:%d...", port))
	if err := r.Run(fmt.Sprintf(":%d", port)); err != nil {
		log.Logger.Panic("Error starting server", zap.Error(err))
	}
}

func initSQLiteDB() *sql.DB {
	db, err := sql.Open("sqlite3", dbFile)
	if err != nil {
		log.Logger.Panic("Error opening SQLite database", zap.Error(err))
	}

	// 创建表
	createTableSQL := `CREATE TABLE IF NOT EXISTS hook_records (
		"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
		"title" TEXT,
		"content" TEXT,
		"create_time" DATETIME,
		"update_time" DATETIME
	);`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		log.Logger.Panic("Error creating table", zap.Error(err))
	}
	return db
}

func pingHandler(c *gin.Context) {
	c.String(http.StatusOK, "pong")
}

func hookHandler(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error reading request body"})
		return
	}

	response := Notification{}
	if err := json.Unmarshal(body, &response); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Error parsing JSON data"})
		return
	}

	log.Logger.Info("Received JSON data", zap.String("data", string(body)))

	sendBackNotification(barkKey, response)

	if response.Event != "library.deleted" {
		c.Status(http.StatusOK)
		return
	}

	saveNotification(response, string(body))

	filePath := response.Item.Path
	realFilePath := strings.ReplaceAll(filePath, "/data/日本电影", "/volume2/onlyhub/avis_japan")
	realFileDir := path.Dir(realFilePath)
	fileName := path.Base(realFilePath)
	toDeleteFiles := getToDeleteFiles(realFileDir, fileName)

	for _, toDeleteFile := range toDeleteFiles {
		log.Logger.Info(toDeleteFile)
	}

	ro := remove.RemoveOption{
		Paths:     toDeleteFiles,
		CanRemove: true,
	}
	remove.RemoveFiles(ro)

	c.Status(http.StatusOK)
}

func saveNotification(response Notification, body string) {
	// 插入数据
	insertPostSQL := `INSERT INTO hook_records (title, content, create_time, update_time) VALUES (?, ?, ?, ?)`
	stmt, err := dbConn.Prepare(insertPostSQL)
	if err != nil {
		log.Logger.Error("Error preparing insert statement", zap.Error(err))
	}
	defer stmt.Close()

	now := time.Now()
	_, err = stmt.Exec(response.Title, body, now, now)
	if err != nil {
		log.Logger.Error("Error inserting data", zap.Error(err))
	}
}

func sendBackNotification(barkKey string, response Notification) {
	client := resty.New()

	// 定义URL和要替换的字符串
	baseURL := "https://api.day.app/%s/EmbyHook/%s"

	title := fmt.Sprintf("Delete %s from %s", path.Base(response.Item.FileName), response.Server.Name)
	// 构建最终的URL
	url := fmt.Sprintf(baseURL, barkKey, title)

	// 发送GET请求
	_, _ = client.R().Get(url)
}

func getToDeleteFiles(realFileDir, videoFileName string) []string {
	arr := []string{}
	videoFileExt := path.Ext(videoFileName)
	videoFileNameWithoutExt := strings.TrimSuffix(videoFileName, videoFileExt)

	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-320-10.bif"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-fanart.jpg"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-poster.jpg"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-thumb.jpg"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-backdrop.jpg"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-landscape.jpg"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-fanart.png"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-poster.png"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-thumb.png"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-backdrop.png"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+"-landscape.png"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+".jpg"))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+".png"))
	arr = append(arr, path.Join(realFileDir, videoFileName))
	arr = append(arr, path.Join(realFileDir, videoFileNameWithoutExt+".nfo"))

	return arr
}
