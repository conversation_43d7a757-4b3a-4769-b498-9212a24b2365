package command

import (
	"context"
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/hardcode"
	"github.com/penwyp/hydra/apps/hydra/pkg/transfer"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewTransferCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "transfer",
		Short: "转移种子，默认从qb到tr",
		Long:  fmt.Sprintf(""),
		Run:   transferTorrents,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&configFile, "configFile", "c", "/volume2/service/bin/hydra/config.json", "-c etc/conf/config.json")
	cmd.PersistentFlags().BoolVarP(&dryRun, "dryRun", "r", false, "仅仅解析，不发送")
	cmd.PersistentFlags().BoolVarP(&safeMode, "safeMode", "s", true, "确认目的端已经完整校验再删除种子")
	cmd.PersistentFlags().IntVarP(&transferSizeLimit, "transferSizeLimit", "t", 800, "size in GB")
	cmd.PersistentFlags().StringSliceVarP(&ignoreTrackers, "ignoreTrackers", "i", []string{"xxafasfasfafasfasfas"}, "需要过滤的tracker")
	cmd.PersistentFlags().StringSliceVarP(&ignoreSavePaths, "ignoreSavePath", "p", []string{"etc/package"}, "指定保跳过的保存路径")
	cmd.PersistentFlags().IntVarP(&retentionDays, "retentionDays", "d", 7, "保留不转移天数，为了提高上传量")
	cmd.PersistentFlags().IntVar(&torrentSizeLimit, "torrentSizeLimit", 1024*1024*1024, "小于1GB的转移到另一个TR")
	cmd.PersistentFlags().BoolVar(&trToTr, "trToTr", false, "TR之间转移，配合种子大小限制")
	cmd.PersistentFlags().BoolVarP(&removeTransmissionDownloadingTorrent, "removeTransmissionDownloadingTorrent", "x", false, "删除Transmission正在下载的种子")
	return cmd
}

func transferTorrents(cmd *cobra.Command, args []string) {
	param := model.TransferParam{
		DryRun:                               dryRun,
		SafeMode:                             safeMode,
		IgnoreTrackers:                       ignoreTrackers,
		TransferSizeLimit:                    transferSizeLimit,
		IgnoreSavePaths:                      ignoreSavePaths,
		RetentionDays:                        retentionDays,
		TorrentSizeLimit:                     torrentSizeLimit,
		TrToTr:                               trToTr,
		RemoveTransmissionDownloadingTorrent: removeTransmissionDownloadingTorrent,
	}

	ctx := context.Background()
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
	config, err := config.ParseConfig(configFile)
	if err != nil {
		log.Logger.Panic("解析配置失败", zap.Error(err), zap.String("conf", configFile))
	}
	log.Logger.Info("工作信息", zap.Any("configFile", configFile), zap.Any("param", param), zap.Any("config", config))

	qbClient, trClient, trsClient, err := hardcode.InitClients(ctx, config)
	if err != nil {
		log.Logger.Panic("初始化客户端失败", zap.Error(err))
	}

	if !param.TrToTr {
		qbTorrents, trTorrentMapping, edf := transfer.QueryQB2TrInformation(ctx, qbClient, trClient, trsClient)
		transfer.TransferQBTorrent(ctx, qbTorrents, param, edf, trClient, trsClient, trTorrentMapping, qbClient)
	} else {
		bigTrTorrents, smallTorrentMapping := transfer.QueryTr2TrInformation(ctx, trClient, trsClient)
		transfer.TransferTrTorrent(ctx, bigTrTorrents, param, trClient, trsClient, smallTorrentMapping)
	}
	log.Logger.Info("-------------------")
}
