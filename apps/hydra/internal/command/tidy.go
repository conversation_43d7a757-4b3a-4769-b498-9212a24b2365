package command

import (
	"context"
	"fmt"
	"github.com/hekmon/transmissionrpc/v3"
	"github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"net/url"
)

func NewTidyCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "tidy",
		Short: "解析种子，删除没有Announce的",
		Long:  fmt.Sprintf(""),
		Run:   tidyTorrents,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&configFile, "configFile", "c", "/volume2/service/bin/hydra/config.json", "-c etc/conf/config.json")
	return cmd
}

func tidyTorrents(cmd *cobra.Command, args []string) {
	param := model.LinkParam{
		LogLevel:   logLevel,
		ConfigFile: configFile,
		APIKey:     apiKey,
		ProxyURL:   proxySocks5Address,
	}

	ctx := context.Background()
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
	config, err := config.ParseConfig(configFile)
	if err != nil {
		log.Logger.Panic("解析配置失败", zap.Error(err), zap.String("conf", configFile))
	}
	log.Logger.Info("工作信息", zap.Any("param", param), zap.String("torrentDir", torrentDir),
		zap.String("downloadDir", downloadDir), zap.Any("config", config))

	endpoint, err := url.Parse(config.TransmissionClient.GetTransmissionDSN())
	if err != nil {
		log.Logger.Panic("初始化transmission DSN失败", zap.Error(err))
	}
	trClient, err := transmissionrpc.New(endpoint, nil)
	if err != nil {
		log.Logger.Panic("初始化transmission客户端失败", zap.Error(err))
	}
	ok, serverVersion, serverMinimumVersion, err := trClient.RPCVersion(ctx)
	if err != nil {
		log.Logger.Panic("ping transmission客户端失败", zap.Error(err))
	}
	log.Logger.Info(fmt.Sprintf("ping transmission, status:%v, version:%d.%d", ok, serverVersion, serverMinimumVersion))

	torrents, err := trClient.TorrentGetAll(ctx)
	if err != nil {
		log.Logger.Panic("获取torrents失败", zap.Error(err))
	}

	for _, torrent := range torrents {
		torrentName := *torrent.Name
		log.Logger.Debug("Torrent信息", zap.String("name", torrentName))
		if len(torrent.Trackers) == 0 {
			log.Logger.Info("空Torrent", zap.String("name", torrentName))
		}
	}
}
