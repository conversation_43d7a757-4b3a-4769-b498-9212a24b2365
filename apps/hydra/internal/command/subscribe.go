package command

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/penwyp/hydra/apps/hydra/internal/json"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"io/fs"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
)

func NewSubscribeCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:  "subscribe",
		Long: fmt.Sprintf(""),
		Run:  doSubscribeStartUp,
	}
	return cmd
}

var s S

type S struct {
	sync.RWMutex
	files []fs.FileInfo
}

func (i *S) SetFiles(files []fs.FileInfo) {
	i.Lock()
	defer i.Unlock()
	i.files = files
}

func (i *S) GetFiles() []fs.FileInfo {
	i.RLock()
	defer i.RUnlock()
	return i.files
}

func getPort() string {
	// exec command and fetch output
	cmd := exec.Command("/usr/local/bin/v2ray", "get-port")
	port, _ := cmd.CombinedOutput()
	return string(port)
}

const v2rayConfDir = "/etc/v2ray/conf/"

func listProxy() {
	// walk dir

	fileInfos := make([]fs.FileInfo, 0)
	walkErr := filepath.WalkDir(v2rayConfDir,
		func(filePath string, info fs.DirEntry, err error) error {
			if info.IsDir() {
				return nil
			}

			fileInfo, err := info.Info()
			if err != nil {
				return err
			}
			fileInfos = append(fileInfos, fileInfo)
			return nil
		},
	)
	if walkErr != nil {
		fmt.Println(walkErr)
		return
	}
	sort.Slice(fileInfos, func(i, j int) bool {
		return fileInfos[i].ModTime().Before(fileInfos[j].ModTime())
	})
	s.SetFiles(fileInfos)
}

func newProxy(port string) string {
	cmd := exec.Command("/usr/local/bin/v2ray", "add", "VMess-TCP")
	ret, _ := cmd.CombinedOutput()
	return string(ret)
}

type ProxyDTO struct {
	Name    string `json:"name"`
	ModTime string `json:"mod_time,omitempty"`
	Status  string `json:"status,omitempty"`
}

func createProxyHandler(c *gin.Context) {
	proxyDtos := createNewProxy()
	c.JSON(200, proxyDtos)
}

func createNewProxy() []ProxyDTO {
	listProxy()
	oldProxies := s.GetFiles()

	pm := make(map[string]ProxyDTO)
	for _, oldProxy := range oldProxies {
		pm[oldProxy.Name()] = ProxyDTO{
			Name:    oldProxy.Name(),
			ModTime: oldProxy.ModTime().Format("2006-01-02 15:04:05"),
		}
	}

	port := getPort()
	newProxy(port)
	listProxy()
	newProxies := s.GetFiles()

	for _, newProxy := range newProxies {
		if _, ok := pm[newProxy.Name()]; !ok {
			pm[newProxy.Name()] = ProxyDTO{
				Name:    newProxy.Name(),
				ModTime: newProxy.ModTime().Format("2006-01-02 15:04:05"),
				Status:  "new",
			}
		}
	}

	proxyDtos := lo.Values(pm)
	return proxyDtos
}

func autoRemoveProxyHandler(c *gin.Context) {
	dtos := autoRemoveProxy()
	c.JSON(200, dtos)
}

func refreshProxyHandler(c *gin.Context) {
	listProxy()
	proxies := s.GetFiles()
	pm := lo.Associate(proxies, func(i fs.FileInfo) (string, *ProxyDTO) {
		return i.Name(), &ProxyDTO{
			Name:    i.Name(),
			ModTime: i.ModTime().Format("2006-01-02 15:04:05"),
			Status:  "removed",
		}
	})

	port := getPort()
	newProxy(port)
	newProxy(port)
	autoRemoveProxy()

	listProxy()

	latestProxies := s.GetFiles()
	for _, latestProxy := range latestProxies {
		if _, ok := pm[latestProxy.Name()]; !ok {
			pm[latestProxy.Name()] = &ProxyDTO{
				Name:    latestProxy.Name(),
				ModTime: latestProxy.ModTime().Format("2006-01-02 15:04:05"),
				Status:  "new",
			}
		} else {
			pm[latestProxy.Name()].Status = ""
		}
	}

	c.JSON(200, lo.Values(pm))
}

func autoRemoveProxy() []*ProxyDTO {
	listProxy()
	files := s.GetFiles()

	pm := make(map[string]*ProxyDTO)
	for _, file := range files {
		pm[file.Name()] = &ProxyDTO{
			Name:    file.Name(),
			ModTime: file.ModTime().Format("2006-01-02 15:04:05"),
		}
	}

	summary := ProxyDTO{
		Name:   "Summary",
		Status: "contains less than 2 proxies, skip remove action",
	}

	// reserve 2 files
	if len(files) > 2 {
		for i := 0; i < len(files)-2; i++ {
			err := os.Remove(v2rayConfDir + files[i].Name())
			if err != nil {
				fmt.Println(err)
			}
			pm[files[i].Name()].Status = "removed"
		}
		summary.Status = "removed all proxies except last 2"
	} else {
		summary = ProxyDTO{
			Name:   "Summary",
			Status: "contains less than 2 proxies, skip remove action",
		}

	}

	dtos := lo.Values(pm)
	dtos = append(dtos, &summary)
	return dtos
}

func listProxyHandler(c *gin.Context) {
	listProxy()
	files := s.GetFiles()
	proxyDtos := make([]ProxyDTO, 0)
	for _, file := range files {
		proxyDtos = append(proxyDtos, ProxyDTO{
			Name:    file.Name(),
			ModTime: file.ModTime().Format("2006-01-02 15:04:05"),
		})
	}

	c.JSON(200, proxyDtos)
}

func getKlConf(c *gin.Context) {
	listProxy()
	files := s.GetFiles()

	confs := make([]V2rayConfItem, 0, len(files))
	tags := make([]string, 0, len(files))
	proxyConfigs := make([]string, 0, len(files))

	ips := getLocalIP()

	fmt.Println(fmt.Sprintf("got %d confs and %d ips", len(files), len(ips)))

	for _, file := range files {
		cc, err := os.ReadFile(v2rayConfDir + file.Name())
		if err != nil {
			fmt.Println(err)
			continue
		}
		var conf V2rayConfItem
		err = json.Unmarshal(cc, &conf)
		if err != nil {
			fmt.Println(err)
			continue
		}
		confs = append(confs, conf)
		proxyConfigs = append(proxyConfigs, conf.GetProxyStr(ips[0]))
		tags = append(tags, conf.Inbounds[0].Tag)
	}

	var confStr string
	confStr = strings.ReplaceAll(confTemplate, "|2|", strings.Join(tags, ","))
	confStr = strings.ReplaceAll(confStr, "|1|", strings.Join(proxyConfigs, "\n"))

	fileName := "/tmp/xn-subscribe.conf." + uuid.NewString()
	err := os.WriteFile(fileName, []byte(confStr), 0666)
	if err != nil {
		fmt.Println(err)
		c.JSON(200, err.Error())
		return
	}
	c.File(fileName)
}

func getProxyIpHandler(c *gin.Context) {
	ips := getLocalIP()
	c.JSON(200, ips)
}

func doSubscribeStartUp(cmd *cobra.Command, args []string) {
	registerCrontab()
	listenAndServe()
}

func listenAndServe() {
	gin.SetMode(gin.DebugMode)
	r := gin.Default()
	r.GET("/proxy/xn-subscribe.conf", getKlConf)
	r.GET("/proxy/list", listProxyHandler)
	r.GET("/proxy/new", createProxyHandler)
	r.GET("/proxy/create", createProxyHandler)
	r.GET("/proxy/remove", autoRemoveProxyHandler)
	r.GET("/proxy/delete", autoRemoveProxyHandler)
	r.GET("/proxy/refresh", refreshProxyHandler)
	r.GET("/proxy/ip", getProxyIpHandler)

	for _, port := range []int{80, 6008, 8341} {
		portS := strconv.Itoa(port)
		fmt.Println("listen on " + portS)
		if err := r.Run("0.0.0.0:" + portS); err != nil {
			fmt.Println("listen failed, err: " + err.Error())
			continue
		} else {
			fmt.Println("listen on " + portS + " success")
		}
	}
}

func registerCrontab() {
	c := cron.New()
	c.AddFunc("0 3 * * *", func() {
		createNewProxy()
		createNewProxy()
		autoRemoveProxy()
	})
	c.Start()
}

const confTemplate = `
[General]
loglevel = notify
ipv6 = true
bypass-system = true
skip-proxy = 127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/10,localhost,*.local,e.crashlytics.com,captive.apple.com,::ffff:0:0:0:0/1,::ffff:128:0:0:0/1
#DNS设置或根据自己网络情况进行相应设置
bypass-tun = ***********/16,10.0.0.0/8,**********/12
dns-server = ************,*********

[Proxy]
|1|

[Proxy Group]
节点指定 = select,|2|

[Rule]
FINAL,|2|

[URL Rewrite]
^https?://(www.)?(g|google)\.cn https://www.google.com 302

[MITM]
hostname = *.google.cn,*.googlevideo.com`

type V2rayConfItem struct {
	Inbounds []struct {
		Tag      string `json:"tag"`
		Port     int    `json:"port"`
		Listen   string `json:"listen"`
		Protocol string `json:"protocol"`
		Settings struct {
			Method   string `json:"method"`
			Password string `json:"password"`
			Network  string `json:"network"`
			Clients  []struct {
				ID string `json:"id"`
			} `json:"clients"`
		} `json:"settings"`
		StreamSettings struct {
			Network     string `json:"network"`
			TCPSettings struct {
				Header struct {
					Type string `json:"type"`
				} `json:"header"`
			} `json:"tcpSettings"`
		} `json:"streamSettings"`
		Sniffing struct {
			Enabled      bool     `json:"enabled"`
			DestOverride []string `json:"destOverride"`
		} `json:"sniffing"`
	} `json:"inbounds"`
}

func (i *V2rayConfItem) GetProxyStr(ip string) string {
	// Server = Vmess, xx.xx.xx.xx, 443, username=xxx-xxx-xxx-xxx-xxx, ws=true, ws-path=/xxxxx, ws-headers=Host:"www.xxxxx.monster"
	return fmt.Sprintf("%s = %s, %s, %d, username=%s, proto=none, alterId=0, method=auto, plugin=''", i.Inbounds[0].Tag, i.Inbounds[0].Protocol, ip, i.Inbounds[0].Port, i.Inbounds[0].Settings.Clients[0].ID)
}

func getLocalIP() []string {

	ipFile := "/root/hydra.ip.log"
	// check if ipFile is exist
	if _, err := os.Stat(ipFile); !os.IsNotExist(err) {
		// fetch content from file
		content, err := os.ReadFile(ipFile)
		if err != nil {
			fmt.Println(err)
			return nil
		}
		ipStr := string(content)
		ipStr = strings.TrimSpace(ipStr)
		return []string{ipStr}
	}

	interfaces, err := net.Interfaces()
	if err != nil {
		fmt.Println("无法获取网络接口信息:", err)
		return nil
	}

	ips := make([]string, 0)
	for _, inter := range interfaces {
		if inter.Flags&net.FlagLoopback == 0 && inter.Flags&net.FlagUp != 0 {
			addrs, err := inter.Addrs()
			if err != nil {
				fmt.Println("无法获取接口地址信息:", err)
				continue
			}

			for _, addr := range addrs {
				switch v := addr.(type) {
				case *net.IPNet:
					if v.IP.To4() != nil {
						ip := v.IP
						// 排除私有地址和保留地址
						if !isPrivateIP(ip) && !isReservedIP(ip) {
							ips = append(ips, ip.String())
						}
					}
				}
			}
		}
	}
	return ips
}

func isPrivateIP(ip net.IP) bool {
	privateBlocks := []*net.IPNet{
		parseCIDR("10.0.0.0/8"),
		parseCIDR("**********/12"),
		parseCIDR("***********/16"),
	}
	for _, block := range privateBlocks {
		if block.Contains(ip) {
			return true
		}
	}
	return false
}

func isReservedIP(ip net.IP) bool {
	reservedBlocks := []*net.IPNet{
		parseCIDR("0.0.0.0/8"),
		parseCIDR("***********/16"),
		parseCIDR("*********/24"),
		parseCIDR("*********/24"),
		parseCIDR("***********/24"),
		parseCIDR("**********/15"),
		parseCIDR("************/24"),
		parseCIDR("***********/24"),
		parseCIDR("*********/4"),
		parseCIDR("240.0.0.0/4"),
	}
	for _, block := range reservedBlocks {
		if block.Contains(ip) {
			return true
		}
	}
	return false
}

func parseCIDR(cidr string) *net.IPNet {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		panic(err)
	}
	return ipnet
}
