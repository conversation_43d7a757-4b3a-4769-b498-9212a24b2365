package command

var (
	torrentDir         string
	downloadDir        string
	logLevel           string
	dbPath             string
	username           string
	userGroup          string
	redisAddress       string
	folders            []string
	targetPaths        []string
	ignorePaths        []string
	caseSensitive      bool
	version            bool
	configFile         string
	hlinkConfigFile    string
	timesBefore        string
	pathPattern        string
	sqlDBPath          string
	forceDelete        bool
	apiKey             string
	proxySocks5Address string
	dryRun             bool
	safeMode           bool
	ignoreTrackers     []string
	transferSizeLimit  int
	query              string
	command            string
	removePath         string
	removePaths        []string
	walkDir            string

	ignoreSavePaths                      []string
	torrentSizeLimit                     int
	retentionDays                        int
	uploadRetentionHours                 int
	downloadRetentionHours               int
	downloadSpeedLowLevel                int
	trToTr                               bool
	removeTransmissionDownloadingTorrent bool
	interval                             int
	imgFolder                            string
	pdfFolder                            string

	numRequests int
	timeout     int

	// ebook classifier parameters
	sourceDir      string
	targetDir      string
	defaultMove    bool
	llmApiKey      string
	llmApiUrl      string
	llmModel       string
	forceReprocess bool
	showStats      bool
)
