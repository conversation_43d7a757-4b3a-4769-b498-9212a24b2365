package command

import (
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"os/exec"
	"strings"
)

var (
	filter string
)

func NewPruneHistoryCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "prune_history",
		Short: "Prune transfer history records from database",
		Long:  "Delete records from transferhistory table based on specified filter",
		Run:   pruneTransferHistory,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "Log level (info/debug)")
	cmd.PersistentFlags().StringVarP(&dbPath, "dbPath", "d", "/volume2/service/bin/moviepilot2/config/user.db", "SQLite database path")
	cmd.PersistentFlags().BoolVarP(&dryRun, "dry-run", "n", true, "Dry run (no actual deletion)")
	cmd.PersistentFlags().StringVarP(&filter, "filter", "f", "local", "Filter value for dest_storage field")
	return cmd
}

func pruneTransferHistory(cmd *cobra.Command, args []string) {
	// Initialize logger
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	log.Logger.Info("Starting prune operation on database", zap.String("dbPath", dbPath))

	// Instead of using go-sqlite3 which requires CGO, use the sqlite3 command-line tool
	// First, get the count of records
	countCmd := exec.Command("sqlite3", dbPath, "-csv",
		`SELECT COUNT(*) FROM transferhistory WHERE dest_storage = '`+filter+`'`)

	countOutput, err := countCmd.CombinedOutput()
	if err != nil {
		log.Logger.Fatal("Error counting records",
			zap.Error(err),
			zap.String("output", string(countOutput)))
	}

	// Parse count from outputmysql://3XpA6zRuPTSNjpf.root:<PASSWORD>@gateway01.ap-southeast-1.prod.aws.tidbcloud.com:4000/fortune500
	count := strings.TrimSpace(string(countOutput))
	log.Logger.Info("Found records to process",
		zap.String("count", count),
		zap.String("filter", filter),
		zap.String("dest_storage", filter))

	if dryRun {
		log.Logger.Info("DRY RUN: No records will be deleted",
			zap.String("wouldDeleteCount", count),
			zap.String("dest_storage", filter))
		log.Logger.Info("To execute deletion, run again with --dry-run=false")
		return
	}

	// Execute deletion using sqlite3 command
	deleteCmd := exec.Command("sqlite3", dbPath,
		`DELETE FROM transferhistory WHERE dest_storage = '`+filter+`'`)

	deleteOutput, err := deleteCmd.CombinedOutput()
	if err != nil {
		log.Logger.Fatal("Error executing delete",
			zap.Error(err),
			zap.String("output", string(deleteOutput)))
	}

	// Get the number of changes (SQLite keeps track of this)
	changesCmd := exec.Command("sqlite3", dbPath, "SELECT changes()")
	changesOutput, err := changesCmd.CombinedOutput()
	if err != nil {
		log.Logger.Fatal("Error getting number of changes",
			zap.Error(err),
			zap.String("output", string(changesOutput)))
	}

	rowsAffected := strings.TrimSpace(string(changesOutput))
	log.Logger.Info("Successfully deleted records",
		zap.String("deletedCount", rowsAffected),
		zap.String("dest_storage", filter))
}
