package command

import (
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/pkg/chown"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewChownCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "chown",
		Short: "chown",
		Long:  fmt.Sprintf(""),
		Run:   doChown,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&username, "username", "u", "infusee", "Linux User Name")
	cmd.PersistentFlags().StringVarP(&userGroup, "userGroup", "g", "users", "Linux User Group")
	cmd.PersistentFlags().StringSliceVarP(&targetPaths, "targetPaths", "p", []string{"/volume2/pt", "/volume2/theatre", "/volume2/onlyhub"}, "Linux Target Paths")
	cmd.PersistentFlags().StringSliceVarP(&ignorePaths, "ignorePaths", "n", []string{"/volume2/pt/.imcomplete", "/volume2/pt/.qbcache"}, "Linux Ignore Paths")
	return cmd
}

func doChown(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	manager, err := chown.NewChownManager(username, userGroup, ignorePaths)
	if err != nil {
		log.Logger.Panic("创建ChownManager失败", zap.Error(err))
	}
	manager.ApplyOwnerPerm(targetPaths)
	manager.WatchOwnerPerm(targetPaths)
}
