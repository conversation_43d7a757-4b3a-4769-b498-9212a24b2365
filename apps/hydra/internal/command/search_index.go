package command

import (
	"context"
	"os"
	"strings"

	"github.com/penwyp/hydra/apps/hydra/internal/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/pkg/common"
	"github.com/penwyp/hydra/apps/hydra/pkg/index"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

// NewSearchCommand creates a new Cobra command for searching files and directories.
func NewSearchCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "search",
		Short: "Search for files and directories in the NAS filesystem",
		Long:  "Searches the NAS filesystem index for files and directories matching the query.",
		Run:   searchCommand,
	}

	// Define persistent flags
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "Log level: info/debug")
	cmd.PersistentFlags().StringVarP(&configFile, "configFile", "c", "/volume2/service/bin/hydra/config.json", "Path to config file, e.g., -c etc/conf/config.json")
	cmd.PersistentFlags().StringVarP(&query, "query", "q", "not-set-yet", "Search keywords, separated by '|'")
	cmd.PersistentFlags().StringVarP(&redisAddress, "redisAddress", "r", constants.RedisAddr, "Redis server address")
	cmd.PersistentFlags().BoolVarP(&version, "version", "v", false, "Display index version and exit")
	cmd.PersistentFlags().BoolVarP(&caseSensitive, "caseSensitive", "s", false, "Enable case-sensitive search")

	return cmd
}

// searchCommand executes the search logic.
func searchCommand(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
	rdb := cache.OpenCacheDB(constants.RedisAddr)

	// Handle version flag
	if version {
		displayVersion(cmd.Context(), rdb)
		return
	}

	// Process query and search
	queryTokens := prepareQuery(query)
	searchResult := index.SearchFromIndexDB(cmd.Context(), rdb, queryTokens)

	// Output results
	logSearchResults(searchResult)
}

// displayVersion prints the index version and exits.
func displayVersion(ctx context.Context, rdb cache.Cache) {
	versionStr := index.GetIndexDBVersion(ctx, rdb)
	log.Logger.Info("Index version", zap.String("version", versionStr))
	os.Exit(0)
}

// prepareQuery processes the query string into tokens.
func prepareQuery(query string) []string {
	if !caseSensitive {
		query = strings.ToLower(query)
	}
	return strings.Split(query, "|")
}

// logSearchResults logs and prints the search results.
func logSearchResults(result *index.SearchResult) {
	files := result.GetFiles()
	dirs := result.GetDirs()

	// Log and print files
	if len(files) > 0 {
		log.Logger.Info("Files found", zap.Int("count", len(files)))
		for _, file := range files {
			common.PrintHitFile(file.FilePath)
		}
		println("")
	} else {
		log.Logger.Info("No files found")
	}

	// Log and print directories
	if len(dirs) > 0 {
		log.Logger.Info("Directories found", zap.Int("count", len(dirs)))
		for _, dir := range dirs {
			common.PrintHitFile(dir.FilePath)
		}
		println("")
	} else {
		log.Logger.Info("No directories found")
	}

	// Summary
	log.Logger.Info("Search summary",
		zap.Int("files", len(files)),
		zap.Int("dirs", len(dirs)))
}
