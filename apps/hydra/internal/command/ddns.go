package command

import (
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/pkg/ddns"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
)

func NewDDNSCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ddns",
		Short: "ddns",
		Long:  fmt.Sprintf(""),
		Run:   doDDNS,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	return cmd
}

func doDDNS(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	ddns.Update()
}
