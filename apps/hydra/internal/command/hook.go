package command

import (
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/internal/command/hook"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
)

func NewHookCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "hook",
		Short: "hook",
		Long:  fmt.Sprintf(""),
		Run:   doHook,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")

	cmd.AddCommand(hook.NewEmbyAVCommand())
	return cmd
}

func doHook(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
}
