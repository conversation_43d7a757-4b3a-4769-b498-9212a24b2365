package command

import (
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/internal/command/systemctl"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
)

func NewSystemctlCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "sc",
		Short: "systemctl",
		Long:  fmt.Sprintf(""),
		Run:   doSystemctl,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")

	cmd.AddCommand(systemctl.NewUnitFileCommand())
	cmd.AddCommand(systemctl.NewDescCommand())
	return cmd
}

func doSystemctl(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
}
