package command

import (
	"fmt"
	"os"
	"time"

	"github.com/penwyp/hydra/apps/hydra/pkg/ebook"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewEbookClassifierCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ebook-classify",
		Short: "扫描电子书并根据LLM进行自动分类",
		Long: `扫描指定目录中的电子书文件，根据文件名检索书本信息，
然后使用LLM对书本进行分类，并将文件移动到相应的分类目录中。

支持的电子书格式：
- PDF (.pdf)
- EPUB (.epub)
- MOBI (.mobi)
- AZW (.azw, .azw3)
- TXT (.txt)

索引功能：
- 自动维护已处理文件的索引，避免重复处理
- 索引文件保存在目标目录的 .ebook_index.json 中
- 支持强制重新处理已索引的文件

示例用法：
  # 基本分类
  hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key your-api-key

  # 试运行模式（不实际移动文件）
  hydra ebook-classify -s /books/unsorted -t /books/sorted --dry-run

  # 强制重新处理所有文件（忽略索引）
  hydra ebook-classify -s /books/unsorted -t /books/sorted --force-reprocess

  # 显示索引统计信息
  hydra ebook-classify -t /books/sorted --show-stats`,
		Run: doEbookClassify,
	}

	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "日志级别 (info/debug)")
	cmd.PersistentFlags().StringVarP(&sourceDir, "source", "s", "", "源目录路径（必需）")
	cmd.PersistentFlags().StringVarP(&targetDir, "target", "t", "", "目标分类目录路径（必需）")
	cmd.PersistentFlags().StringVar(&llmApiKey, "llm-api-key", "", "LLM API密钥")
	cmd.PersistentFlags().StringVar(&llmApiUrl, "llm-api-url", "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "LLM API地址")
	cmd.PersistentFlags().StringVar(&llmModel, "llm-model", "deepseek-v3-1-250821", "LLM模型名称")
	cmd.PersistentFlags().BoolVar(&dryRun, "dry-run", false, "试运行模式，不实际移动文件")
	cmd.PersistentFlags().BoolVar(&forceReprocess, "force-reprocess", false, "强制重新处理已索引的文件")
	cmd.PersistentFlags().BoolVar(&showStats, "show-stats", false, "显示索引统计信息后退出")

	// 标记必需参数
	cmd.MarkPersistentFlagRequired("source")
	cmd.MarkPersistentFlagRequired("target")

	return cmd
}

func doEbookClassify(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	// 验证参数
	if sourceDir == "" || targetDir == "" {
		log.Logger.Fatal("源目录和目标目录都是必需参数")
		return
	}

	// 验证目录存在性
	if _, err := os.Stat(sourceDir); os.IsNotExist(err) {
		log.Logger.Fatal("源目录不存在", zap.String("path", sourceDir))
		return
	}

	// 创建目标目录（如果不存在）
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		log.Logger.Fatal("创建目标目录失败", zap.String("path", targetDir), zap.Error(err))
		return
	}

	log.Logger.Info("开始电子书分类任务",
		zap.String("source", sourceDir),
		zap.String("target", targetDir),
		zap.Bool("dryRun", dryRun),
		zap.Bool("forceReprocess", forceReprocess),
		zap.String("llmModel", llmModel))

	// 0. 初始化索引管理器
	indexManager := ebook.NewIndexManager(targetDir)
	if err := indexManager.LoadIndex(); err != nil {
		log.Logger.Error("加载索引失败", zap.Error(err))
		// 继续执行，但不使用索引功能
	}

	// 如果只是显示统计信息，则显示后退出
	if showStats {
		indexManager.PrintStats()
		return
	}

	// 1. 扫描电子书文件
	scanner := ebook.NewScanner(sourceDir)
	files, err := scanner.ScanDirectory()
	if err != nil {
		log.Logger.Fatal("扫描目录失败", zap.Error(err))
		return
	}

	if len(files) == 0 {
		log.Logger.Info("未找到电子书文件")
		return
	}

	log.Logger.Info("扫描完成", zap.Int("fileCount", len(files)))

	// 2. 过滤已处理的文件（如果不强制重新处理）
	var filesToProcess []ebook.EbookFile
	var skippedFromIndex int
	bookInfos := make(map[string]*ebook.BookInfo)
	classifications := make(map[string]*ebook.ClassificationResult)

	if !forceReprocess {
		for _, file := range files {
			if entry := indexManager.FindByFileName(file.Name); entry != nil {
				// 检查目标文件是否仍然存在
				if _, err := os.Stat(entry.TargetPath); err == nil {
					// 文件已处理且目标文件存在，跳过
					bookInfos[file.Path] = entry.BookInfo
					classifications[file.Path] = entry.Classification
					skippedFromIndex++
					log.Logger.Debug("跳过已处理文件",
						zap.String("fileName", file.Name),
						zap.String("targetPath", entry.TargetPath))
					continue
				} else {
					// 目标文件不存在，需要重新处理
					log.Logger.Warn("目标文件不存在，将重新处理",
						zap.String("fileName", file.Name),
						zap.String("targetPath", entry.TargetPath))
				}
			}
			filesToProcess = append(filesToProcess, file)
		}
	} else {
		filesToProcess = files
	}

	log.Logger.Info("文件过滤完成",
		zap.Int("totalFiles", len(files)),
		zap.Int("toProcess", len(filesToProcess)),
		zap.Int("skippedFromIndex", skippedFromIndex))

	// 3. 检索书本信息（仅处理需要处理的文件）
	if len(filesToProcess) > 0 {
		metadataRetriever := ebook.NewMetadataRetriever()
		if googleApiKey := os.Getenv("GOOGLE_BOOKS_API_KEY"); googleApiKey != "" {
			metadataRetriever.SetAPIKey("google", googleApiKey)
		}

		log.Logger.Info("开始检索书本信息...")
		newBookInfos := metadataRetriever.BatchRetrieveMetadata(filesToProcess)

		// 合并新检索的信息
		for path, info := range newBookInfos {
			bookInfos[path] = info
		}

		log.Logger.Info("书本信息检索完成", zap.Int("retrievedCount", len(newBookInfos)))

		// 4. LLM分类（仅处理需要处理的文件）
		classifier := ebook.NewLLMClassifier(llmApiKey, llmApiUrl, llmModel)

		log.Logger.Info("开始LLM分类...")
		newClassifications := classifier.BatchClassifyBooks(newBookInfos)

		// 合并新分类结果
		for path, classification := range newClassifications {
			classifications[path] = classification
		}

		log.Logger.Info("LLM分类完成", zap.Int("classifiedCount", len(newClassifications)))
	}

	// 5. 整理文件
	organizer := ebook.NewFileOrganizer(ebook.OrganizeOptions{
		SourceDir:   sourceDir,
		TargetDir:   targetDir,
		DefaultMove: false, // 分类失败时不移动文件
		DryRun:      dryRun,
	})

	log.Logger.Info("开始整理文件...")
	result := organizer.OrganizeFiles(files, bookInfos, classifications)

	// 6. 更新索引（仅在非试运行模式下）
	if !dryRun {
		log.Logger.Info("更新索引...")
		for filePath, targetPath := range result.ProcessedFiles {
			// 找到对应的文件信息
			var file ebook.EbookFile
			for _, f := range files {
				if f.Path == filePath {
					file = f
					break
				}
			}

			if bookInfo, exists := bookInfos[filePath]; exists {
				if classification, exists := classifications[filePath]; exists {
					indexManager.AddEntry(file, bookInfo, classification, targetPath)
				}
			}
		}

		// 保存索引
		if err := indexManager.SaveIndex(); err != nil {
			log.Logger.Error("保存索引失败", zap.Error(err))
		} else {
			log.Logger.Info("索引更新完成")
		}

		// 清理过期索引条目（30天未访问）
		removedCount := indexManager.CleanupStaleEntries(30 * 24 * time.Hour)
		if removedCount > 0 {
			if err := indexManager.SaveIndex(); err != nil {
				log.Logger.Error("保存清理后的索引失败", zap.Error(err))
			}
		}
	}

	// 7. 保存错误日志
	if len(result.ErrorLog) > 0 {
		if err := organizer.SaveErrorLog(result); err != nil {
			log.Logger.Error("保存错误日志失败", zap.Error(err))
		}
	}

	// 8. 输出最终结果
	fmt.Printf("\n=== 电子书分类完成 ===\n")
	fmt.Printf("总文件数: %d\n", result.TotalFiles)
	fmt.Printf("成功处理: %d\n", result.SuccessCount)
	fmt.Printf("处理失败: %d\n", result.FailureCount)
	fmt.Printf("跳过处理: %d\n", result.SkippedCount)

	if skippedFromIndex > 0 {
		fmt.Printf("从索引跳过: %d\n", skippedFromIndex)
	}

	if len(result.CategoryStats) > 0 {
		fmt.Printf("\n本次分类统计:\n")
		for category, count := range result.CategoryStats {
			fmt.Printf("  %s: %d 本\n", category, count)
		}
	}

	if len(result.ErrorLog) > 0 {
		fmt.Printf("\n注意: 处理过程中出现了 %d 个错误，详情请查看错误日志文件\n", len(result.ErrorLog))
	}

	// 显示索引统计信息
	if !dryRun {
		indexManager.PrintStats()
	}

	if dryRun {
		fmt.Printf("\n注意: 这是试运行模式，文件未实际移动，索引未更新\n")
	}

	log.Logger.Info("电子书分类任务完成")
}
