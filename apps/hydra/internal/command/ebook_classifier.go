package command

import (
	"fmt"
	"os"

	"github.com/penwyp/hydra/apps/hydra/pkg/ebook"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewEbookClassifierCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ebook-classify",
		Short: "扫描电子书并根据LLM进行自动分类",
		Long: `扫描指定目录中的电子书文件，根据文件名检索书本信息，
然后使用LLM对书本进行分类，并将文件移动到相应的分类目录中。

支持的电子书格式：
- PDF (.pdf)
- EPUB (.epub)
- MOBI (.mobi)
- AZW (.azw, .azw3)
- TXT (.txt)

示例用法：
  hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key your-api-key
  hydra ebook-classify -s /books/unsorted -t /books/sorted --default-move`,
		Run: doEbookClassify,
	}

	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "日志级别 (info/debug)")
	cmd.PersistentFlags().StringVarP(&sourceDir, "source", "s", "", "源目录路径（必需）")
	cmd.PersistentFlags().StringVarP(&targetDir, "target", "t", "", "目标分类目录路径（必需）")
	cmd.PersistentFlags().BoolVarP(&defaultMove, "default-move", "d", false, "分类失败时是否移动到默认目录")
	cmd.PersistentFlags().StringVar(&llmApiKey, "llm-api-key", "", "LLM API密钥")
	cmd.PersistentFlags().StringVar(&llmApiUrl, "llm-api-url", "https://api.openai.com/v1/chat/completions", "LLM API地址")
	cmd.PersistentFlags().StringVar(&llmModel, "llm-model", "gpt-3.5-turbo", "LLM模型名称")

	// 标记必需参数
	cmd.MarkPersistentFlagRequired("source")
	cmd.MarkPersistentFlagRequired("target")

	return cmd
}

func doEbookClassify(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	// 验证参数
	if sourceDir == "" || targetDir == "" {
		log.Logger.Fatal("源目录和目标目录都是必需参数")
		return
	}

	// 验证目录存在性
	if _, err := os.Stat(sourceDir); os.IsNotExist(err) {
		log.Logger.Fatal("源目录不存在", zap.String("path", sourceDir))
		return
	}

	// 创建目标目录（如果不存在）
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		log.Logger.Fatal("创建目标目录失败", zap.String("path", targetDir), zap.Error(err))
		return
	}

	log.Logger.Info("开始电子书分类任务",
		zap.String("source", sourceDir),
		zap.String("target", targetDir),
		zap.Bool("defaultMove", defaultMove),
		zap.String("llmModel", llmModel))

	// 1. 扫描电子书文件
	scanner := ebook.NewScanner(sourceDir)
	files, err := scanner.ScanDirectory()
	if err != nil {
		log.Logger.Fatal("扫描目录失败", zap.Error(err))
		return
	}

	if len(files) == 0 {
		log.Logger.Info("未找到电子书文件")
		return
	}

	log.Logger.Info("扫描完成", zap.Int("fileCount", len(files)))

	// 2. 检索书本信息
	metadataRetriever := ebook.NewMetadataRetriever()
	if googleApiKey := os.Getenv("GOOGLE_BOOKS_API_KEY"); googleApiKey != "" {
		metadataRetriever.SetAPIKey("google", googleApiKey)
	}

	log.Logger.Info("开始检索书本信息...")
	bookInfos := metadataRetriever.BatchRetrieveMetadata(files)
	log.Logger.Info("书本信息检索完成", zap.Int("retrievedCount", len(bookInfos)))

	// 3. LLM分类
	classifier := ebook.NewLLMClassifier(llmApiKey, llmApiUrl, llmModel)

	log.Logger.Info("开始LLM分类...")
	classifications := classifier.BatchClassifyBooks(bookInfos)
	log.Logger.Info("LLM分类完成", zap.Int("classifiedCount", len(classifications)))

	// 4. 整理文件
	organizer := ebook.NewFileOrganizer(ebook.OrganizeOptions{
		SourceDir:   sourceDir,
		TargetDir:   targetDir,
		DefaultMove: defaultMove,
		DryRun:      false,
	})

	log.Logger.Info("开始整理文件...")
	result := organizer.OrganizeFiles(files, bookInfos, classifications)

	// 5. 保存错误日志
	if len(result.ErrorLog) > 0 {
		if err := organizer.SaveErrorLog(result); err != nil {
			log.Logger.Error("保存错误日志失败", zap.Error(err))
		}
	}

	// 6. 输出最终结果
	fmt.Printf("\n=== 电子书分类完成 ===\n")
	fmt.Printf("总文件数: %d\n", result.TotalFiles)
	fmt.Printf("成功处理: %d\n", result.SuccessCount)
	fmt.Printf("处理失败: %d\n", result.FailureCount)
	fmt.Printf("跳过处理: %d\n", result.SkippedCount)

	if len(result.CategoryStats) > 0 {
		fmt.Printf("\n分类统计:\n")
		for category, count := range result.CategoryStats {
			fmt.Printf("  %s: %d 本\n", category, count)
		}
	}

	if len(result.ErrorLog) > 0 {
		fmt.Printf("\n注意: 处理过程中出现了 %d 个错误，详情请查看错误日志文件\n", len(result.ErrorLog))
	}

	log.Logger.Info("电子书分类任务完成")
}
