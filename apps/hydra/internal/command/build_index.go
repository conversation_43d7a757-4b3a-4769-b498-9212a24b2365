package command

import (
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/pkg/index"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
)

func NewBuildIndexCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "build-index",
		Short: "为所有文件创建inode索引",
		Long: `

/path/to/file1 -> inode1
/link/to/file1 -> inode1
/path/to/file3 -> inode3
/path/to/file4 -> inode4

inode1 -> [/path/to/file1,/link/to/file1]
inode3 -> [/path/to/file3]
inode4 -> [/path/to/file4]

`,
		Run: doBuildIndexCommand,
	}

	cmd.PersistentFlags().StringSliceVarP(&folders, "folders", "s", constants.IndexFolders, "info/debug")
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", constants.LogLevelInfo, "info/debug")
	cmd.PersistentFlags().StringVarP(&redisAddress, "redisAddress", "r", constants.RedisAddr, "Redis Address")
	return cmd
}

func doBuildIndexCommand(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})
	index.BuildIndex(cmd.Context(), folders, redisAddress)
}
