package command

import (
	"fmt"
	"github.com/penwyp/hydra/apps/hydra/pkg/remove"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
)

var canRemove bool
var purgeEmptyDirs bool
var dirSizeThreshold int64

func NewRemoveCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "rm",
		Short: "删除所有硬链接",
		Long:  fmt.Sprintf(""),
		Run:   doRemove,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringSliceVarP(&removePaths, "removePaths", "p", []string{"/not/exist/path"}, "需要删除的文件或文件夹")
	cmd.PersistentFlags().BoolVarP(&canRemove, "canRemove", "e", false, "是否仅查看，为true时，触发删除动作")
	cmd.PersistentFlags().BoolVarP(&purgeEmptyDirs, "purgeEmptyDirs", "r", false, "是否删除空目录")
	cmd.PersistentFlags().Int64VarP(&dirSizeThreshold, "dirSizeThreshold", "s", 5000, "空目录大小阈值")
	return cmd
}

func doRemove(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	ro := remove.RemoveOption{
		Paths:            removePaths,
		CanRemove:        canRemove,
		DirSizeThreshold: dirSizeThreshold,
	}
	if purgeEmptyDirs {
		for {
			deleteNum := remove.RemoveDirs(ro)
			if deleteNum == 0 {
				break
			}
			if !canRemove {
				break
			}
		}
	} else {
		remove.RemoveFiles(ro)
	}
}
