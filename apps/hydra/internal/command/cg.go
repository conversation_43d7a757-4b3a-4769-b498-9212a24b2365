package command

import (
	"fmt"
	"github.com/jung-kurt/gofpdf/v2"
	"github.com/penwyp/hydra/shared/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"image"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
)

func NewCGCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "cg",
		Short: "将IMG转换成PDF",
		Long:  fmt.Sprintf(""),
		Run:   buildCGPdfs,
	}
	cmd.PersistentFlags().StringVarP(&imgFolder, "imgFolder", "i", "/path/to/image", "")
	cmd.PersistentFlags().StringVarP(&pdfFolder, "pdfFolder", "p", "/path/to/pdf", "")
	return cmd
}

func buildCGPdfs(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	if imgFolder == "" || imgFolder == "/path/to/image" {
		log.Logger.Warn("imgFolder is empty")
		return
	}
	if pdfFolder == "" || pdfFolder == "/path/to/pdf" {
		log.Logger.Warn("pdfFolder is empty")
		return
	}

	er := os.MkdirAll(pdfFolder, 0755)
	if er != nil {
		log.Logger.Fatal("mkdir pdfFolder failed")
		return
	}

	todoFiles := checkTodoFiles(imgFolder, pdfFolder)
	log.Logger.Info("待处理文件", zap.Int("files", len(todoFiles)))

	buildPDFs(imgFolder, pdfFolder, todoFiles)
	log.Logger.Info("处理完成")
}

func buildPDFs(imgFolder string, pdfFolder string, todoImgDirs []string) {
	routineNum := runtime.NumCPU() / 2
	if routineNum <= 0 {
		routineNum = 1
	}
	log.Logger.Info("使用CPU核心数", zap.Int("num", routineNum))
	errG := errgroup.Group{}
	errG.SetLimit(routineNum)

	for _, todoImgDir := range todoImgDirs {
		todoImgDir := todoImgDir

		errG.Go(
			func() error {

				imgRelativePath := strings.Replace(todoImgDir, imgFolder, "", 1)

				log.Logger.Info("开始处理图像", zap.String("imgDir", imgRelativePath))

				err := buildPDF(imgFolder, pdfFolder, todoImgDir)
				if err != nil {
					log.Logger.Error("处理失败", zap.String("imgDir", imgRelativePath), zap.Error(err))
				} else {
					log.Logger.Info("生成PDF成功", zap.String("file", imgRelativePath+".pdf"))
				}
				return nil

			})
	}
	errG.Wait()
}

func checkTodoFiles(imgFolder, pdfFolder string) []string {
	existPDFs := make([]string, 0, 3000)
	allImgs := make([]string, 0, 3000)

	walkPDFErr := filepath.Walk(pdfFolder,
		func(filePath string, info os.FileInfo, err error) error {
			if info.IsDir() {
				return nil
			}
			if filepath.Ext(info.Name()) == ".pdf" {
				existPDFs = append(existPDFs, filePath[:len(filePath)-4])
			}
			return nil
		})
	if walkPDFErr != nil {
		log.Logger.Error(walkPDFErr.Error())
	}

	walkImgErr := filepath.Walk(imgFolder,
		func(filePath string, info os.FileInfo, err error) error {
			if filePath == imgFolder {
				return nil
			}

			if info.IsDir() {
				hasImage := containImages(filePath)
				if hasImage {
					allImgs = append(allImgs, filePath)
				}
			}
			return nil
		})
	if walkImgErr != nil {
		log.Logger.Error(walkImgErr.Error())
	}

	originImages := make([]string, 0, len(existPDFs))
	for _, existPDF := range existPDFs {
		originImages = append(originImages, strings.Replace(existPDF, pdfFolder, imgFolder, 1))
	}

	todoFiles, _ := lo.Difference(allImgs, originImages)
	return todoFiles
}

func containImages(folder string) bool {
	files, err := os.ReadDir(folder)
	if err != nil {
		log.Logger.Error("读取文件夹失败", zap.String("folder", folder), zap.Error(err))
		return false
	}

	hasImage := false
	for _, file := range files {
		if !file.IsDir() && isImageFile(file.Name()) {
			hasImage = true
			break
		}
	}
	return hasImage
}

func isImageFile(filename string) bool {
	imageExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp"} // 可根据需要添加其他图片格式的扩展名

	for _, ext := range imageExtensions {
		if strings.HasSuffix(strings.ToLower(filename), ext) {
			return true
		}
	}

	return false
}

var imageTp = map[string]string{
	".jpg":  "JPG",
	".jpeg": "JPG",
	".png":  "PNG",
}

type BuildPDFRequest struct {
	ImageFolder string
	PdfFolder   string
	ImageDir    string
	LogLevel    string
}

func buildPDF(imgFolder string, pdfFolder string, imgDir string) error {
	imageMaterialFolder := imgDir
	pdfArtifactPath := strings.Replace(imgDir+".pdf", imgFolder, pdfFolder, 1)
	pdf := gofpdf.New("P", "mm", "A3", "")

	mkdirErr := os.MkdirAll(filepath.Dir(pdfArtifactPath), 0755)
	if mkdirErr != nil {
		return errors.WithStack(mkdirErr)
	}

	imgPages := make([]string, 0)
	imgExts := make(map[string]string)
	err := filepath.Walk(imageMaterialFolder,
		func(filePath string, info os.FileInfo, err error) error {
			if filePath == imageMaterialFolder {
				return nil
			}
			if info.IsDir() {
				return nil
			}
			if strings.Contains(filePath, "@eaDir") || strings.Contains(filePath, ".DS_Store") {
				return nil
			}

			fileExt := filepath.Ext(info.Name())
			switch fileExt {
			case ".jpg", ".JPG", ".jpeg", ".JPEG", ".png", ".PNG", ".webp":
				if strings.HasPrefix(info.Name(), ".") {
					log.Logger.Warn("删除隐藏图片文件", zap.String("file", filePath))
					os.Remove(filePath)
					return nil
				}

				imgPages = append(imgPages, filePath)
				imgExts[filePath] = imageTp[fileExt]
			default:
				log.Logger.Warn("不支持的文件类型", zap.String("file", filePath))
			}
			return nil
		})
	if err != nil {
		return errors.Wrapf(err, "遍历文件失败")
	}

	// sort imgPages
	sort.Strings(imgPages)
	invalidImgNum := 0

	for _, imgPage := range imgPages {
		//readBuffer := &bytes.Buffer{}
		//pdf.RegisterImageOptionsReader(imgPage, gofpdf.ImageOptions{
		//	ImageType:             imgExts[imgPage],
		//	ReadDpi:               false,
		//	AllowNegativePosition: false,
		//}, readBuffer)
		imageProperty, err := extractImageProperty(imgPage)
		if err != nil {
			log.Logger.Warn("解析图片失败,跳过并继续处理下一张图片", zap.String("file", imgPage), zap.Error(err))
			continue
		}
		invalidImgNum++
		pdf.AddPageFormat("P", gofpdf.SizeType{Wd: imageProperty.Width, Ht: imageProperty.Height})
		pdf.Image(imgPage, 0, 0, imageProperty.Width, imageProperty.Height, false, imgExts[imgPage], 0, "")
	}

	if invalidImgNum == 0 {
		log.Logger.Warn("没有有效图片，退出", zap.String("folder", imageMaterialFolder))
		return nil
	}

	writeErr := pdf.OutputFileAndClose(pdfArtifactPath)
	if writeErr != nil {
		return errors.Wrapf(writeErr, "写入PDF文件失败")
	}
	return nil
}

type ImageProperty struct {
	Width  float64
	Height float64
}

func extractImageProperty(filePath string) (*ImageProperty, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	c, _, err := image.DecodeConfig(file)
	if err != nil {
		return nil, err
	}
	log.Logger.Debug("图片信息", zap.String("file", filePath), zap.Int("width", c.Width), zap.Int("height", c.Height))
	return &ImageProperty{
		Width:  float64(c.Width),
		Height: float64(c.Height),
	}, nil
}
