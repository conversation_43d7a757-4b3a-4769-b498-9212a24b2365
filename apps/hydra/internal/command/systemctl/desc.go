package systemctl

import (
	"fmt"
	"github.com/coreos/go-systemd/v22/dbus"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func NewDescCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "desc",
		Short: "desc",
		Long:  fmt.Sprintf(""),
		Run:   doDesc,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&query, "query", "q", "restart-natter-emby302.service", "query text of unit files")
	return cmd
}

func doDesc(cmd *cobra.Command, args []string) {

	validKeys := map[string]bool{
		"Id":           true,
		"TriggeredBy":  true,
		"FragmentPath": true,
		"Names":        true,
		"Description":  true,
		"After":        true,
	}

	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	conn, err := dbus.NewSystemdConnectionContext(cmd.Context())
	if err != nil {
		log.Logger.Error("new systemd connection failed", zap.Error(err))
		return
	}
	defer conn.Close()

	properties, getErr := conn.GetUnitPropertiesContext(cmd.Context(), query)
	if getErr != nil {
		log.Logger.Error("get unit property failed", zap.Error(getErr))
		return
	}
	for key, value := range properties {
		if _, ok := validKeys[key]; !ok {
			continue
		}
		log.Logger.Info("unit property", zap.String("key", key), zap.Any("value", value))
	}
}
