package systemctl

import (
	"fmt"
	"github.com/coreos/go-systemd/v22/dbus"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"strings"
)

func NewUnitFileCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "file",
		Short: "unitfiles",
		Long:  fmt.Sprintf(""),
		Run:   doUnitFiles,
	}
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().StringVarP(&query, "query", "q", "emby", "query text of unit files")
	return cmd
}

func doUnitFiles(cmd *cobra.Command, args []string) {
	log.InitLogger(log.LoggerOption{LogLevel: logLevel})

	conn, err := dbus.NewSystemdConnectionContext(cmd.Context())
	if err != nil {
		log.Logger.Error("new systemd connection failed", zap.Error(err))
		return
	}
	defer conn.Close()

	unitFiles, getErr := conn.ListUnitFilesContext(cmd.Context())
	if getErr != nil {
		log.Logger.Error("list unit files failed", zap.Error(getErr))
		return
	}
	for _, unitFile := range unitFiles {
		if strings.Contains(unitFile.Path, query) {
			log.Logger.Info("unit file", zap.String("type", unitFile.Type), zap.String("path", unitFile.Path))
		}
	}
}
