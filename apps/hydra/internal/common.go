package inner

import (
	"strconv"
)

const base = 10

var (
	gitVersion string
	buildTime  string
)

func GetBuildTime() string {
	return buildTime
}

func GetGitVersion() string {
	return gitVersion
}

// Int 泛型
type Int interface {
	int | int8 | int16 | int32 | int64
}

// Uint 泛型
type Uint interface {
	uint | uint8 | uint16 | uint32 | uint64
}

// FormatInt Int类型转换为十进制字符串
func FormatInt[T Int](i T) string {
	return strconv.FormatInt(int64(i), base)
}

// FormatUint Uint类型转换为十进制字符串
func FormatUint[T Uint](i T) string {
	return strconv.FormatUint(uint64(i), base)
}
