package util

import (
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
	"os/user"
	"strconv"
)

// LookupUserAndGroup 获取用户和组的 UID 和 GID
func LookupUserAndGroup(userName, userGroup string) (uid, gid int, err error) {
	usr, err := user.Lookup(userName)
	if err != nil {
		log.Logger.Error("Failed to lookup user", zap.String("user", userName), zap.Error(err))
		return 0, 0, err
	}

	grp, err := user.LookupGroup(userGroup)
	if err != nil {
		log.Logger.Error("Failed to lookup group", zap.String("group", userGroup), zap.Error(err))
		return 0, 0, err
	}

	uid, err = strconv.Atoi(usr.Uid)
	if err != nil {
		log.Logger.Error("Invalid UID", zap.String("uid", usr.Uid), zap.Error(err))
		return 0, 0, err
	}

	gid, err = strconv.Atoi(grp.Gid)
	if err != nil {
		log.Logger.Error("Invalid GID", zap.String("gid", grp.Gid), zap.Error(err))
		return 0, 0, err
	}

	return uid, gid, nil
}
