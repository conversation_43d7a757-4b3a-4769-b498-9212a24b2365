package util

import (
	"crypto/md5"
	"encoding/hex"
	"io"
	"os"
)

// FileMD5 计算指定文件的 MD5 值
func FileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	_, err = io.CopyBuffer(hash, file, make([]byte, 32*1024))
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(hash.Sum(nil)), nil
}

// CompareMD5 比较两个文件的 MD5 值是否相同
func CompareMD5(filePathA, filePathB string) (bool, error) {
	hashA, err := FileMD5(filePathA)
	if err != nil {
		return false, err
	}
	hashB, err := FileMD5(filePathB)
	if err != nil {
		return false, err
	}
	return hashA == hashB, nil
}
