package util

import (
	"regexp"
	"strconv"
	"time"

	"github.com/pkg/errors"
)

// ParseUnixTimestampStringToTime 将 Unix 时间戳字符串解析为 time.Time
func ParseUnixTimestampStringToTime(timestampStr string) (time.Time, error) {
	if timestampStr == "" {
		return time.Time{}, errors.New("empty timestamp string")
	}

	timestampFloat, err := strconv.ParseFloat(timestampStr, 64)
	if err != nil {
		return time.Time{}, errors.Wrap(err, "parsing timestamp failed")
	}

	if timestampFloat < 0 {
		return time.Time{}, errors.New("negative timestamp not supported")
	}

	timestampInt := int64(timestampFloat)
	timestampNano := int64((timestampFloat - float64(timestampInt)) * 1e9)
	return time.Unix(timestampInt, timestampNano), nil
}

// timeUnitMap 定义时间单位到秒的映射
var timeUnitMap = map[byte]int{
	'd': 24 * 60 * 60,
	'h': 60 * 60,
	'm': 60,
	's': 1,
}

// timeRegex 用于解析时间格式的正则表达式
var timeRegex = regexp.MustCompile(`^(\d+d)?(\d+h)?(\d+m)?(\d+s)?$`)

// ParseGolangTimeToSeconds 将时间字符串解析为秒数，支持格式：1d, 1h, 1m, 1s, 1d1h, 或纯数字（秒）
func ParseGolangTimeToSeconds(val string) (int, error) {
	if val == "all" {
		return -1, nil
	}

	if seconds, err := strconv.Atoi(val); err == nil {
		if seconds < 0 {
			return 0, errors.New("negative seconds not supported")
		}
		return seconds, nil
	}

	matches := timeRegex.FindStringSubmatch(val)
	if matches == nil || matches[0] == "" {
		return 0, errors.New("invalid time format")
	}

	totalSeconds := 0
	for _, match := range matches[1:] {
		if match == "" {
			continue
		}

		value, err := strconv.Atoi(match[:len(match)-1])
		if err != nil {
			return 0, errors.Wrap(err, "parsing time value failed")
		}
		if value < 0 {
			return 0, errors.New("negative time value not supported")
		}

		unit := match[len(match)-1]
		totalSeconds += value * timeUnitMap[unit]
	}

	return totalSeconds, nil
}
