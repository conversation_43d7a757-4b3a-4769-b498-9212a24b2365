package util

import "fmt"

// FormatSpace 将字节数转换为人类可读的格式（B, KB, MB, GB, TB）
func FormatSpace(bytes int64) string {
	const unit = 1024
	units := []string{"B", "KB", "MB", "GB", "TB"}

	if bytes < unit {
		return fmt.Sprintf("%d%s", bytes, units[0])
	}

	// 从 KB 开始计算
	size := float64(bytes)
	for i := 1; i < len(units); i++ {
		if size < unit {
			return fmt.Sprintf("%.2f%s", size, units[i])
		}
		size /= unit
	}
	// 如果超出最大单位（TB），返回最后一个单位的结果
	return fmt.Sprintf("%.2f%s", size, units[len(units)-1])
}
