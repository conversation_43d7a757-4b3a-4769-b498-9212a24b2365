package util

import (
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/slack-go/slack"
	"strings"
)

func EscapeSlackText(t string) string {
	t = strings.ReplaceAll(t, "&", "&amp;")
	t = strings.ReplaceAll(t, "<", "&lt;")
	t = strings.ReplaceAll(t, ">", "&gt;")
	return t
}

func NewMarkdownBlockObj(str string) *slack.TextBlockObject {
	encoded := EscapeSlackText(str)
	return slack.NewTextBlockObject(slack.MarkdownType, encoded, false, false)
}

func NewPlainTextBlockObj(str string) *slack.TextBlockObject {
	encoded := EscapeSlackText(str)
	return slack.NewTextBlockObject(slack.PlainTextType, encoded, false, false)
}

func NewSectionBlockObj(str string) *slack.SectionBlock {
	return slack.NewSectionBlock(NewMarkdownBlockObj(str), nil, nil)
}

func NewSectionBlockObjWithSearchMoreButton(str string) *slack.SectionBlock {
	buttonElement := slack.NewButtonBlockElement(string(constants.MoreAction), str, NewPlainTextBlockObj("More..."))
	return slack.NewSectionBlock(NewMarkdownBlockObj(str), nil, slack.NewAccessory(buttonElement))
}

func NewTextContext(str string) []slack.Block {
	blocks := make([]slack.Block, 0)
	blocks = append(blocks, slack.NewContextBlock("", NewMarkdownBlockObj(str)))
	return blocks
}

func NewTextsContext(strs []string) []slack.Block {
	blocks := make([]slack.Block, 0, len(strs))
	for _, str := range strs {
		blocks = append(blocks, slack.NewContextBlock("", NewMarkdownBlockObj(str)))
	}
	return blocks
}

func NewTextContextBlockOption(str string) slack.MsgOption {
	return slack.MsgOptionBlocks(NewTextContext(str)...)
}

func NewTextsContextBlockOption(strs []string) slack.MsgOption {
	return slack.MsgOptionBlocks(NewTextsContext(strs)...)
}
