package cache

import (
	"context"
	"github.com/penwyp/hydra/apps/hydra/internal/json"
	"github.com/penwyp/hydra/shared/log"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"time"
)

type HydraCache struct {
	cache redis.UniversalClient
}

// NewCache new cache
func NewCache(cache redis.UniversalClient) Cache {
	return HydraCache{cache: cache}
}

// Cache : represent the oss's cache contract
type Cache interface {
	HSet(ctx context.Context, key string, values map[string]interface{}) *redis.IntCmd
	HKeys(ctx context.Context, key string) *redis.StringSliceCmd
	HMGet(ctx context.Context, key string, fields ...string) *redis.SliceCmd
	HGet(ctx context.Context, key, field string) *redis.StringCmd

	Get(ctx context.Context, key string) (string, error)
	GetUnmarshal(ctx context.Context, key string, v any) error
	MGet(ctx context.Context, keys ...string) ([]any, error)
	MGetUnmarshal(ctx context.Context, vs []any, keys ...string) error

	Set(ctx context.Context, key, value string, expirationSecond int) error
	SetMarshal(ctx context.Context, key string, value any, expirationSecond int) error

	ZAdd(ctx context.Context, key string, members ...redis.Z) error
	ZRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	ZRevRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)
	ZRevRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)

	FlushDB(ctx context.Context) error
	Pipeline() redis.Pipeliner
}

// Set set value
func (i HydraCache) Set(ctx context.Context, key, value string, expirationSecond int) error {
	err := i.cache.Set(ctx, key, value, time.Second*time.Duration(expirationSecond)).Err()
	if err != nil {
		log.Logger.Error("set cache failed", zap.String("key", key), zap.String("value", value),
			zap.Error(err))
		return err
	}
	return nil
}

// FlushDB flush all
func (i HydraCache) FlushDB(ctx context.Context) error {
	err := i.cache.FlushDB(ctx).Err()
	if err != nil {
		log.Logger.Error("flush cache failed", zap.Error(err))
		return err
	}
	return nil
}

// Pipeline
func (i HydraCache) Pipeline() redis.Pipeliner {
	return i.cache.Pipeline()
}

// Set set value
func (i HydraCache) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	err := i.cache.ZAdd(ctx, key, members...).Err()
	if err != nil {
		log.Logger.Error("set zset cache failed", zap.String("key", key),
			zap.Error(err))
		return err
	}
	return nil
}

// ZRange set value
func (i HydraCache) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	cmd := i.cache.ZRange(ctx, key, start, stop)
	err := cmd.Err()
	if err != nil {
		log.Logger.Error("range zset cache failed", zap.String("key", key),
			zap.Error(err))
		return nil, err
	}
	return cmd.Result()
}

// ZRangeByScore set value
func (i HydraCache) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	cmd := i.cache.ZRangeByScore(ctx, key, opt)
	err := cmd.Err()
	if err != nil {
		log.Logger.Error("range zset cache failed", zap.String("key", key),
			zap.Error(err))
		return nil, err
	}
	return cmd.Result()
}

// ZRevRangeByScore set value
func (i HydraCache) ZRevRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	cmd := i.cache.ZRevRangeByScore(ctx, key, opt)
	err := cmd.Err()
	if err != nil {
		log.Logger.Error("range zset cache failed", zap.String("key", key),
			zap.Error(err))
		return nil, err
	}
	return cmd.Result()
}

// ZRevRange set value
func (i HydraCache) ZRevRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	cmd := i.cache.ZRevRange(ctx, key, start, stop)
	err := cmd.Err()
	if err != nil {
		log.Logger.Error("rev range zset cache failed", zap.String("key", key),
			zap.Error(err))
		return nil, err
	}
	return cmd.Result()
}

// hset
func (i HydraCache) HSet(ctx context.Context, key string, values map[string]interface{}) *redis.IntCmd {
	return i.cache.HSet(ctx, key, values)
}

func (i HydraCache) HKeys(ctx context.Context, key string) *redis.StringSliceCmd {
	return i.cache.HKeys(ctx, key)
}

func (i HydraCache) HGet(ctx context.Context, key, field string) *redis.StringCmd {
	return i.cache.HGet(ctx, key, field)
}

func (i HydraCache) HMGet(ctx context.Context, key string, fields ...string) *redis.SliceCmd {
	return i.cache.HMGet(ctx, key, fields...)
}

// SetMarshal set value with marshal
func (i HydraCache) SetMarshal(ctx context.Context, key string, value any, expirationSecond int) error {
	encoded, marshalErr := json.Marshal(value)
	if marshalErr != nil {
		return marshalErr
	}
	vv := string(encoded)
	if setErr := i.cache.Set(ctx, key, vv, time.Second*time.Duration(expirationSecond)).Err(); setErr != nil {
		log.Logger.Error("set cache failed", zap.String("key", key), zap.String("value", vv),
			zap.Error(setErr))
		return setErr
	}
	return nil
}

// Get get value
func (i HydraCache) Get(ctx context.Context, key string) (string, error) {
	getResponse := i.cache.Get(ctx, key)
	if getErr := getResponse.Err(); getErr != nil {
		return "", getErr
	}
	bytes, err := getResponse.Bytes()
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// GetUnmarshal get value and unmarshal
func (i HydraCache) GetUnmarshal(ctx context.Context, key string, v any) error {
	getResponse := i.cache.Get(ctx, key)
	if getErr := getResponse.Err(); getErr != nil {
		return getErr
	}
	bytes, err := getResponse.Bytes()
	if err != nil {
		return err
	}
	if unmarshalErr := json.Unmarshal(bytes, v); unmarshalErr != nil {
		return unmarshalErr
	}
	return nil
}

// MGetUnmarshal get value and unmarshal
func (i HydraCache) MGetUnmarshal(ctx context.Context, vs []any, keys ...string) error {
	mgetResponse := i.cache.MGet(ctx, keys...)
	if getErr := mgetResponse.Err(); getErr != nil {
		return getErr
	}
	results, err := mgetResponse.Result()
	if err != nil {
		return err
	}
	for idx, result := range results {
		v := vs[idx]
		resultStr, ok := result.(string)
		if !ok {
			continue
		}
		if unmarshalErr := json.UnmarshalString(resultStr, &v); unmarshalErr != nil {
			return unmarshalErr
		}
		vs[idx] = v
	}
	return nil
}

// MGet mget valu
func (i HydraCache) MGet(ctx context.Context, keys ...string) ([]any, error) {
	mgetResponse := i.cache.MGet(ctx, keys...)
	if getErr := mgetResponse.Err(); getErr != nil {
		return nil, getErr
	}
	results, err := mgetResponse.Result()
	if err != nil {
		return nil, err
	}
	return results, nil
}
