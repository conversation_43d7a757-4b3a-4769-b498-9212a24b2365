package cache

import (
	"context"
	"github.com/penwyp/hydra/shared/log"
	"github.com/redis/go-redis/v9"
)

func OpenCacheDB(address string) Cache {
	return OpenCacheDBWithDB(address, 0)
}

func OpenCacheDBWithDB(address string, db int) Cache {
	rdb := redis.NewClient(&redis.Options{
		Addr:     address,
		Password: "", // no password set
		DB:       db, // use specified DB
	})

	if pingErr := rdb.Ping(context.Background()).Err(); pingErr != nil {
		log.Logger.Error(pingErr.Error())
	}

	return NewCache(rdb)
}
