package opts

import (
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/shared/log"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap/zapcore"
)

type ProcessOption struct {
	ChannelId string
	Client    *socketmode.Client
}

type ProcessOptionFunc func(*ProcessOption)

func OptionClient(client *socketmode.Client) ProcessOptionFunc {
	return func(o *ProcessOption) {
		o.Client = client
	}
}

func OptionChannelId(channelId string) ProcessOptionFunc {
	return func(o *ProcessOption) {
		o.ChannelId = channelId
	}
}

func BuildProcessOption(options ...ProcessOptionFunc) ProcessOption {
	var opt ProcessOption
	for _, o := range options {
		o(&opt)
	}
	return opt
}

func InitLoggerWithOption(logLevel string, options ...ProcessOptionFunc) {
	option := BuildProcessOption(options...)
	if option.Client != nil && option.ChannelId != "" {
		hookWriter := &log.SlackWriter{
			ChannelId: option.ChannelId,
			Client:    option.Client,
		}
		log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo, Writers: []zapcore.WriteSyncer{zapcore.AddSync(hookWriter)}})
	} else {
		log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: logLevel})
	}
}
