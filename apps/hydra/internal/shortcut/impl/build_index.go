package impl

import (
	"context"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut"
	_interface "github.com/penwyp/hydra/apps/hydra/internal/shortcut/interface"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/opts"
	"github.com/penwyp/hydra/apps/hydra/pkg/index"
)

type BuildIndex struct{}

func (i BuildIndex) Name() string {
	return "/build-index"
}

func (i BuildIndex) ProcessMessage(options ...opts.ProcessOptionFunc) error {
	logLevel := constants.LogLevelInfo
	folders := constants.IndexFolders

	opts.InitLoggerWithOption(logLevel, options...)

	index.BuildIndex(context.Background(), folders, constants.RedisAddr)
	return nil
}

func init() {
	sc := BuildIndex{}
	shortcut.Add(sc.Name(), func() _interface.Shortcut {
		return sc
	})
}
