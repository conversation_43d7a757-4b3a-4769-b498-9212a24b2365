package impl

import (
	"context"
	"github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/hardcode"
	_interface "github.com/penwyp/hydra/apps/hydra/internal/shortcut/interface"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/opts"
	"github.com/penwyp/hydra/apps/hydra/pkg/transfer"
	"github.com/penwyp/hydra/shared/log"
)

type TransferTorrents struct {
}

func (i TransferTorrents) Name() string {
	return "/transfer-torrents"
}

func (i TransferTorrents) ProcessMessage(options ...opts.ProcessOptionFunc) error {
	logLevel := constants.LogLevelInfo
	opts.InitLoggerWithOption(logLevel, options...)

	ctx := context.Background()

	qbClient, trClient, trsClient, err := hardcode.InitClients(ctx, config.DefaultConfig)

	qbTorrents, trTorrentMapping, edf := transfer.QueryQB2TrInformation(ctx, qbClient, trClient, trsClient)
	if err != nil {
		log.WebhookSugarLogger().Error("init client error", "error", err)
		return err
	}

	param := model.TransferParam{
		RetentionDays:     0,
		TorrentSizeLimit:  1024 * 1024 * 1024,
		TransferSizeLimit: 800,
	}
	transfer.TransferQBTorrent(ctx, qbTorrents, param, edf, trClient, trsClient, trTorrentMapping, qbClient)

	return nil
}

func init() {
	sc := TransferTorrents{}
	shortcut.Add(sc.Name(), func() _interface.Shortcut {
		return sc
	})
}
