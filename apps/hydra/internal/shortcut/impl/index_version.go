package impl

import (
	"context"
	"github.com/penwyp/hydra/apps/hydra/internal/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut"
	_interface "github.com/penwyp/hydra/apps/hydra/internal/shortcut/interface"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/opts"
	"github.com/penwyp/hydra/apps/hydra/pkg/index"
)

type IndexVersion struct {
}

func (i IndexVersion) Name() string {
	return "/index-version"
}

func (i IndexVersion) ProcessMessage(options ...opts.ProcessOptionFunc) error {
	logLevel := constants.LogLevelInfo

	opts.InitLoggerWithOption(logLevel, options...)

	rdb := cache.OpenCacheDB(constants.RedisAddr)

	index.GetIndexDBVersion(context.Background(), rdb)
	return nil
}

func init() {
	sc := IndexVersion{}
	shortcut.Add(sc.Name(), func() _interface.Shortcut {
		return sc
	})
}
