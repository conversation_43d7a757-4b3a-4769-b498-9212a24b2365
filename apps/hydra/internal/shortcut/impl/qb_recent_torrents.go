package impl

import (
	"context"
	"fmt"
	"github.com/pen1120/qbapi"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/hardcode"
	_interface "github.com/penwyp/hydra/apps/hydra/internal/shortcut/interface"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/opts"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/shared/log"
	"github.com/samber/lo"
	"github.com/slack-go/slack"
	"sort"
	"time"
)

type QbRecentTorrents struct {
}

func (i QbRecentTorrents) Name() string {
	return "/qb-recent-torrents"
}

func (i QbRecentTorrents) ProcessMessage(options ...opts.ProcessOptionFunc) error {
	logLevel := constants.LogLevelInfo
	opts.InitLoggerWithOption(logLevel, options...)

	qbClient, err := hardcode.GetQBClient()
	if err != nil {
		log.WebhookSugarLogger().Error("init qb client error", "error", err)
		return err
	}

	rsp, err := qbClient.GetMainData(context.Background(), &qbapi.GetMainDataReq{})
	if err != nil {
		log.WebhookSugarLogger().Error("get qb main data error", "error", err)
		return err
	}

	option := opts.BuildProcessOption(options...)
	client := option.Client
	channelId := option.ChannelId

	tss := make([]TorrentStatus, 0)
	counter := 0

	torrents := lo.Values(rsp.Torrents)
	sort.Slice(torrents, func(i, j int) bool {
		return torrents[i].AddedOn > torrents[j].AddedOn
	})

	for _, torrent := range torrents {
		// torrents in last 3 days
		addTime := time.Unix(int64(torrent.AddedOn), 0)
		if time.Now().Sub(addTime) < 3*24*time.Hour {
			counter++
			tss = append(tss, TorrentStatus{
				Index:    counter,
				Name:     torrent.Name,
				Progress: formatTorrentProgress(torrent.Progress),
				AddOn:    addTime.Format(constants.TimeFormat),
				Tags:     torrent.Tags,
			})
		}
	}

	// build blocks
	blocks := make([]slack.Block, 0)
	blocks = append(blocks, slack.NewDividerBlock())
	blocks = append(blocks, util.NewTextContext("*Current Torrents*")...)
	if len(tss) == 0 {
		blocks = append(blocks, util.NewSectionBlockObj("Not found"))
	} else {
		for idx, ts := range tss {
			blocks = append(blocks, util.NewSectionBlockObj(fmt.Sprintf("%d. *%s*\nTags: %s, Progress: %s, AddOn: %s", ts.Index, ts.Name, ts.Tags, ts.Progress, ts.AddOn)))
			if idx+1 >= 8 {
				break
			}
		}
	}
	blocks = append(blocks, slack.NewDividerBlock())
	blocks = append(blocks, util.NewSectionBlockObj(fmt.Sprintf("Total torrents: %d, Space left: %s", len(rsp.Torrents), util.FormatSpace(rsp.ServerState.FreeSpaceOnDisk))))

	client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))

	return nil
}

func init() {
	sc := QbRecentTorrents{}
	shortcut.Add(sc.Name(), func() _interface.Shortcut {
		return sc
	})
}

type TorrentStatus struct {
	Index    int
	Name     string
	Progress string
	AddOn    string
	Tags     string
}

func formatTorrentProgress(f float64) string {
	return fmt.Sprintf("%.2f%%", f*100)
}

func init() {
	sc := QbRecentTorrents{}
	shortcut.Add(sc.Name(), func() _interface.Shortcut {
		return sc
	})
}
