package hardcode

import (
	"context"
	"github.com/hekmon/transmissionrpc/v3"
	"github.com/pen1120/qbapi"
	"github.com/penwyp/hydra/apps/hydra/internal/client"
	conf "github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/shared/log"
	"github.com/pkg/errors"
	"net/url"
)

const QB_HOST = "https://qb.hydra.cab"
const QB_USER = "nas"
const QB_PASSWORD = "acce-s6se/nas"

func GetQBClient() (*qbapi.QBAPI, error) {
	return client.InitQBClient(
		client.WithUsername(QB_USER),
		client.WithPassword(QB_PASSWORD),
		client.WithDSN(QB_HOST),
		client.WithSSL(),
	)
}

func InitClients(ctx context.Context, config *conf.Configuration) (*qbapi.QBAPI, *transmissionrpc.Client, *transmissionrpc.Client, error) {
	qbClient, err := client.InitQBClient(client.BuildDownloaderOptionFuncs(config.QBitTorrentClient)...)
	if err != nil {
		return nil, nil, nil, errors.WithMessage(err, "初始化qbittorrent客户端失败")
	}
	log.Logger.Info("初始化qb成功")

	endpoint, err := url.Parse(config.TransmissionClient.GetTransmissionDSN())
	if err != nil {
		return nil, nil, nil, errors.WithMessage(err, "初始化transmission DSN失败")
	}
	trClient, err := transmissionrpc.New(endpoint, nil)
	if err != nil {
		return nil, nil, nil, errors.WithMessage(err, "初始化transmission客户端失败")
	}
	if _, _, _, err = trClient.RPCVersion(ctx); err != nil {
		return nil, nil, nil, errors.WithMessage(err, "ping transmission客户端失败")
	}
	log.Logger.Info("初始化tr成功")

	smallEndpoint, err := url.Parse(config.TransmissionSmallClient.GetTransmissionDSN())
	if err != nil {
		return nil, nil, nil, errors.WithMessage(err, "初始化small transmission DSN失败")
	}
	trSmallClient, err := transmissionrpc.New(smallEndpoint, nil)
	if err != nil {
		return nil, nil, nil, errors.WithMessage(err, "初始化small transmission客户端失败")
	}
	if _, _, _, err = trSmallClient.RPCVersion(ctx); err != nil {
		return nil, nil, nil, errors.WithMessage(err, "ping small transmission客户端失败")
	}
	log.Logger.Info("初始化small-tr成功")
	return qbClient, trClient, trSmallClient, err
}

func InitQbClient(ctx context.Context, config *conf.Configuration) (*qbapi.QBAPI, error) {
	qbClient, err := client.InitQBClient(client.BuildDownloaderOptionFuncs(config.QBitTorrentClient)...)
	if err != nil {
		return nil, errors.WithMessage(err, "初始化qbittorrent客户端失败")
	}
	log.Logger.Info("初始化qb成功")

	return qbClient, err
}
