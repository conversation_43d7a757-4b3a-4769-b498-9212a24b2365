package cache

import (
	"github.com/patrickmn/go-cache"
	"sync"
	"time"
)

var dedplicateCache *cache.Cache
var dedplicateOnce sync.Once

func GetMessageDeduplicateCache() *cache.Cache {
	dedplicateOnce.Do(func() {
		dedplicateCache = cache.New(5*time.Minute, 60*time.Minute)
	})
	return dedplicateCache
}

func IsMsgIdExist(msgID string) bool {
	c := GetMessageDeduplicateCache()
	_, exist := c.Get(msgID)
	return exist
}

func SetMsgId(msgID string) {
	c := GetMessageDeduplicateCache()
	c.Set(msgID, true, cache.NoExpiration)
}
