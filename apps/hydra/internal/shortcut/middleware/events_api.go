package middleware

import (
	"bytes"
	"context"
	"fmt"

	"github.com/coreos/go-systemd/v22/dbus"
	"github.com/pen1120/qbapi"
	cache2 "github.com/penwyp/hydra/apps/hydra/internal/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/config"
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/model"
	"github.com/penwyp/hydra/apps/hydra/internal/runtime"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/bot"

	"os/exec"
	"sort"
	"strconv"
	"strings"

	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/cache"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/hardcode"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/apps/hydra/pkg/hlink"
	"github.com/penwyp/hydra/apps/hydra/pkg/index"
	"github.com/penwyp/hydra/shared/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func MiddlewareEventsAPI(evt *socketmode.Event, client *socketmode.Client) {
	internalLogger := log.WebhookSugarLogger()

	eventsAPIEvent, ok := evt.Data.(slackevents.EventsAPIEvent)
	if !ok {
		internalLogger.Infof("Ignored %+v\n", evt)
		return
	}

	switch eventsAPIEvent.Type {
	case slackevents.CallbackEvent:
		innerEvent := eventsAPIEvent.InnerEvent
		switch ev := innerEvent.Data.(type) {
		case *slackevents.AppMentionEvent:
			internalLogger.Infof("We have been mentionned in %v", ev.Channel)
			_, _, err := client.Client.PostMessage(ev.Channel, slack.MsgOptionText("Yes, hello.", false))
			if err != nil {
				internalLogger.Infof("failed posting message: %v", err)
			}
		case *slackevents.MemberJoinedChannelEvent:
			internalLogger.Infof("user %q joined to channel %q", ev.User, ev.Channel)
		case *slackevents.MessageEvent:

			eventTs, err := util.ParseUnixTimestampStringToTime(ev.EventTimeStamp)
			if err != nil {
				internalLogger.Error("parse event timestamp error", zap.Error(err))
				return
			}

			text := strings.TrimSpace(ev.Text)

			if runtime.BeforeStartTime(&eventTs) {
				internalLogger.Debug("receive message before start time, ignore: ", text)
				return
			}

			if cache.IsMsgIdExist(ev.ClientMsgID) {
				internalLogger.Debug("receive message before, ignore: ", text)
				return
			} else {
				cache.SetMsgId(ev.ClientMsgID)
			}

			if ev.BotID != "" {
				internalLogger.Debug("receive message from bot, ignore: ", text)
				return
			}
			if ev.User == bot.SLACK_BOT_USER {
				internalLogger.Debug("receive message from self, ignore: ", text)
				return
			}

			internalLogger.Info("Received Message:", text, zap.String("user", ev.User))

			_, _, err = client.PostMessage(ev.User, util.NewTextContextBlockOption("Received Message: *"+text+"*"))
			if err != nil {
				internalLogger.Error("post message error", zap.Error(err))
			}

			const TEXT_COMMAND_SEARCH = "s "
			const TEXT_COMMAND_LINK = "l "
			const TEXT_COMMAND_QBITTORRENT = "qb "
			const TEXT_COMMAND_HELP = "help"
			const TEXT_COMMAND_DOCKER = "docker"
			const TEXT_COMMAND_DOCKER_COMPOSE = "docker-compose"
			const TEXT_COMMAND_SYSTEMCTL = "sc"
			const TEXT_COMMAND_BUILD_INDEX = "b"

			ctx := context.Background()

			// 真正干活
			if strings.HasPrefix(text, TEXT_COMMAND_SEARCH) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Searching..."))
				searchFromIndexDB(ctx, client, ev.User, strings.TrimPrefix(text, TEXT_COMMAND_SEARCH))
			} else if strings.HasPrefix(text, TEXT_COMMAND_QBITTORRENT) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Qbittorenting..."))
				handlerQbittorrentCommand(ctx, client, ev.User, strings.TrimPrefix(text, TEXT_COMMAND_QBITTORRENT))
			} else if strings.HasPrefix(text, TEXT_COMMAND_DOCKER) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Dockering..."))
				handlerDockerCommand(ctx, client, ev.User, strings.TrimPrefix(text, TEXT_COMMAND_DOCKER))
			} else if strings.HasPrefix(text, TEXT_COMMAND_DOCKER_COMPOSE) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Docker Composing..."))
				handlerDockerComposeCommand(ctx, client, ev.User, strings.TrimPrefix(text, TEXT_COMMAND_DOCKER_COMPOSE))
			} else if strings.HasPrefix(text, TEXT_COMMAND_LINK) || text == strings.TrimSpace(TEXT_COMMAND_LINK) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Linking..."))
				hardLinkFiles(ctx, client, ev.User, strings.TrimSpace(strings.TrimPrefix(text, strings.TrimSpace(TEXT_COMMAND_LINK))))
			} else if strings.HasPrefix(text, TEXT_COMMAND_BUILD_INDEX) || text == strings.TrimSpace(TEXT_COMMAND_BUILD_INDEX) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Building index..."))
				buildIndex(ctx, client, ev.User, strings.TrimSpace(strings.TrimPrefix(text, strings.TrimSpace(TEXT_COMMAND_BUILD_INDEX))))
			} else if strings.HasPrefix(text, TEXT_COMMAND_SYSTEMCTL) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Systemctling..."))
				handleSystemctl(ctx, client, ev.User, strings.TrimSpace(strings.TrimPrefix(text, strings.TrimSpace(TEXT_COMMAND_SYSTEMCTL))))
			} else if strings.EqualFold(text, TEXT_COMMAND_HELP) {
				_, _, _ = client.PostMessage(ev.User, util.NewTextsContextBlockOption(Help_Messages))
			} else {
				_, _, _ = client.PostMessage(ev.User, util.NewTextContextBlockOption("Not supported command"))
			}

		default:
			internalLogger.Warn("unsupported Events API event type received", ev)
		}
	default:
		client.Debugf("unsupported Events API event received")
	}
}

var Help_Messages = []string{
	"b # 构建所有文件索引",
	"s 陈奕迅 # 查询陈奕迅相关文件",
	"l # 硬链接所有文件",
	"l movie # 硬链接movie相关文件",
	"qb download 10 # 设置qb下载速度为10MB/s",
	"qb upload 10 # 设置qb上传速度为10MB/s",
	"qb recent 10 # 查看qb最近10个种子",
}

func buildIndex(ctx context.Context, client *socketmode.Client, channelId string, text string) {
	folders := constants.IndexFolders
	hookWriter := &log.SlackWriter{
		ChannelId: channelId,
		Client:    client,
	}
	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo, Writers: []zapcore.WriteSyncer{zapcore.AddSync(hookWriter)}})
	index.BuildIndex(context.Background(), folders, constants.RedisAddr)
}

func hardLinkFiles(ctx context.Context, client *socketmode.Client, channelId string, text string) {
	internalLogger := log.WebhookSugarLogger()
	var timesBefore = "1d"
	var pathPattern = ""

	text = strings.TrimSpace(text)
	if text == "" {
		timesBefore = "1d"
		pathPattern = ""
	} else {
		textTokens := strings.Split(text, " ")
		if len(textTokens) == 1 {
			timesBefore = textTokens[0]
			pathPattern = ""
		} else if len(textTokens) >= 2 {
			timesBefore = textTokens[0]
			pathPattern = textTokens[1]
		}
	}

	param := &model.HlinkParam{
		LogLevel:    constants.LogLevelInfo,
		ConfigFile:  "/volume2/service/dat/hydra/etc/hlink/hlink_webhook.json",
		TimesBefore: timesBefore,
		PathPattern: pathPattern,
	}

	hlinkConfig, err := config.ParseHlinkConfig(param.ConfigFile)
	if err != nil {
		internalLogger.Error("解析配置失败", zap.Error(err), zap.String("conf", param.ConfigFile))
		return
	}
	internalLogger.Info("工作信息", zap.Any("param", param))

	if validateErr := hlinkConfig.Validate(); validateErr != nil {
		internalLogger.Error("配置校验失败"+param.ConfigFile, zap.Error(validateErr))
		return
	}

	hookWriter := &log.SlackWriter{
		ChannelId: channelId,
		Client:    client,
	}
	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo, Writers: []zapcore.WriteSyncer{zapcore.AddSync(hookWriter)}})

	linkStatuses := hlink.HardLinkFiles(param, hlinkConfig)
	hlink.DisplayLinkResults(linkStatuses, hookWriter)
}

func searchFromIndexDB(ctx context.Context, client *socketmode.Client, channelId string, queryText string) {
	internalLogger := log.WebhookSugarLogger()
	queryTokens := strings.Split(strings.TrimSpace(queryText), "|")

	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo})
	rdb := cache2.OpenCacheDB(constants.RedisAddr)

	searchResult := index.SearchFromIndexDB(context.Background(), rdb, queryTokens)

	const LIMIT = 8

	// build blocks
	blocks := make([]slack.Block, 0)
	blocks = append(blocks, slack.NewDividerBlock())
	blocks = append(blocks, util.NewTextContext("*FILES*")...)
	files := searchResult.GetFiles()
	if len(files) == 0 {
		blocks = append(blocks, util.NewSectionBlockObj("Not found"))
	} else {
		for idx, file := range files {
			blocks = append(blocks, util.NewSectionBlockObjWithSearchMoreButton(file.FilePath))
			if idx+1 >= LIMIT {
				break
			}

		}
	}
	blocks = append(blocks, slack.NewDividerBlock())
	blocks = append(blocks, util.NewTextContext("*DIRS*")...)
	dirs := searchResult.GetDirs()
	if len(dirs) == 0 {
		blocks = append(blocks, util.NewSectionBlockObj("Not found"))
	} else {
		for idx, dir := range dirs {
			blocks = append(blocks, util.NewSectionBlockObjWithSearchMoreButton(dir.FilePath))
			if idx+1 >= 8 {
				break
			}
		}
	}
	blocks = append(blocks, slack.NewDividerBlock())
	_, _, postErr := client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))
	if postErr != nil {
		internalLogger.Error("post message error", zap.Error(postErr))
	}
}

func handleSystemctl(ctx context.Context, client *socketmode.Client, channelId string, queryText string) {
	internalLogger := log.WebhookSugarLogger()
	queryTokens := strings.Split(strings.TrimSpace(queryText), "|")

	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo})

	conn, err := dbus.NewSystemdConnectionContext(ctx)
	if err != nil {
		internalLogger.Error("new systemd connection failed", zap.Error(err))
		return
	}
	defer conn.Close()

	switch strings.ToUpper(queryTokens[0]) {
	case "SEARCH":
		files, processErr := conn.ListUnitFilesContext(ctx)
		if processErr != nil {
			internalLogger.Error("list files failed", zap.Error(processErr))
			return
		}
		queryText := ""
		if len(queryTokens) > 1 {
			queryText = queryTokens[1]
		}
		blocks := make([]slack.Block, 0)
		for _, file := range files {
			if strings.Contains(file.Path, queryText) {
				// build blocks
				blocks = append(blocks, slack.NewDividerBlock())
				blocks = append(blocks, util.NewTextContext(fmt.Sprintf("FilePath:%s, FileType:%s", file.Path, file.Type))...)

			}
		}
		if len(blocks) == 0 {
			blocks = append(blocks, util.NewTextContext("Keyword "+queryText+", Not found")...)
		}
		_, _, postErr := client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))
		if postErr != nil {
			internalLogger.Error("post message error", zap.Error(postErr))
		}
	}
}

func handlerDockerCommand(ctx context.Context, client *socketmode.Client, channelId string, queryText string) {
	internalLogger := log.WebhookSugarLogger()
	queryTokens := strings.Split(strings.TrimSpace(queryText), " ")

	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo})

	cmd := exec.CommandContext(ctx, "docker", queryTokens...)
	var stdin, stdout, stderr bytes.Buffer
	cmd.Stdin = &stdin
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// build blocks
	blocks := make([]slack.Block, 0)
	blocks = append(blocks, slack.NewDividerBlock())

	if err := cmd.Run(); err != nil {
		internalLogger.Error("run docker command failed", zap.Error(err))
		blocks = append(blocks, util.NewTextContext("执行失败："+stderr.String())...)
	} else {
		blocks = append(blocks, util.NewTextContext("执行成功："+stdout.String())...)
	}

	_, _, postErr := client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))
	if postErr != nil {
		internalLogger.Error("post message error", zap.Error(postErr))
	}
}

func handlerDockerComposeCommand(ctx context.Context, client *socketmode.Client, channelId string, queryText string) {
	internalLogger := log.WebhookSugarLogger()
	queryTokens := strings.Split(strings.TrimSpace(queryText), " ")

	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo})

	cmd := exec.CommandContext(ctx, "docker-compose", queryTokens...)
	var stdin, stdout, stderr bytes.Buffer
	cmd.Stdin = &stdin
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// build blocks
	blocks := make([]slack.Block, 0)
	blocks = append(blocks, slack.NewDividerBlock())

	if err := cmd.Run(); err != nil {
		internalLogger.Error("run docker-compose command failed", zap.Error(err))
		blocks = append(blocks, util.NewTextContext("执行失败："+stderr.String())...)
	} else {
		blocks = append(blocks, util.NewTextContext("执行成功："+stdout.String())...)
	}

	_, _, postErr := client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))
	if postErr != nil {
		internalLogger.Error("post message error", zap.Error(postErr))
	}
}

func handlerQbittorrentCommand(ctx context.Context, client *socketmode.Client, channelId string, queryText string) {
	internalLogger := log.WebhookSugarLogger()
	queryTokens := strings.Split(strings.TrimSpace(queryText), " ")

	log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo})

	var subCommand string
	var value string
	if len(queryTokens) <= 0 {
		internalLogger.Error("validate query failed", zap.Error(errors.New("参数不足:"+queryText)))
		return
	} else if len(queryTokens) == 1 {
		subCommand = queryTokens[0]
	} else {
		subCommand = queryTokens[0]
		value = queryTokens[1]
	}

	if subCommand == "download" || subCommand == "upload" {
		valueInt, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			internalLogger.Error("subcommand arg failed", zap.Error(err))
			return
		}
		qbClient, err := hardcode.InitQbClient(ctx, config.DefaultConfig)
		if err != nil {
			internalLogger.Error("init qb client failed", zap.Error(err))
			return
		}

		var oldSpeedInt int
		var newSpeedInt = valueInt * 1024 * 1024

		if subCommand == "download" {
			qbDownloadLimit, err := qbClient.GetGlobalDownloadLimit(ctx, &qbapi.GetGlobalDownloadLimitReq{})
			if err != nil {
				internalLogger.Error("get qb main data failed", zap.Error(err))
				return
			}
			oldSpeedInt = qbDownloadLimit.Speed
			_, err = qbClient.SetGlobalDownloadLimit(ctx, &qbapi.SetGlobalDownloadLimitReq{Speed: int(newSpeedInt)})
			if err != nil {
				internalLogger.Error("set qb main data failed", zap.Error(err))
				return
			}
		} else if subCommand == "upload" {
			qbUploadLimit, err := qbClient.GetGlobalUploadLimit(ctx, &qbapi.GetGlobalUploadLimitReq{})
			if err != nil {
				internalLogger.Error("get qb main data failed", zap.Error(err))
				return
			}
			oldSpeedInt = qbUploadLimit.Speed
			_, err = qbClient.SetGlobalUploadLimit(ctx, &qbapi.SetGlobalUploadLimitReq{Speed: int(newSpeedInt)})
			if err != nil {
				internalLogger.Error("set qb main data failed", zap.Error(err))
				return
			}
		}

		// build blocks
		blocks := make([]slack.Block, 0)
		blocks = append(blocks, slack.NewDividerBlock())
		blocks = append(blocks, util.NewTextContext("历史速度："+util.FormatSpace(int64(oldSpeedInt))+"/s")...)
		blocks = append(blocks, util.NewTextContext("当前速度："+util.FormatSpace(int64(newSpeedInt))+"/s")...)
		_, _, postErr := client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))
		if postErr != nil {
			internalLogger.Error("post message error", zap.Error(postErr))
		}
	} else if subCommand == "recent" {
		recentNum, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			recentNum = 10
		}

		qbClient, err := hardcode.GetQBClient()
		if err != nil {
			internalLogger.Error("init qb client error", "error", err)
			return
		}

		rsp, err := qbClient.GetMainData(context.Background(), &qbapi.GetMainDataReq{})
		if err != nil {
			internalLogger.Error("get qb main data error", "error", err)
			return
		}

		tss := make([]TorrentStatus, 0)
		counter := 0

		torrents := lo.Values(rsp.Torrents)
		sort.Slice(torrents, func(i, j int) bool {
			return torrents[i].AddedOn > torrents[j].AddedOn
		})

		for _, torrent := range torrents {
			counter++
			tss = append(tss, TorrentStatus{
				Index:    counter,
				Name:     torrent.Name,
				Progress: formatTorrentProgress(torrent.Progress),
				Tags:     torrent.Tags,
			})
		}

		// build blocks
		blocks := make([]slack.Block, 0)
		blocks = append(blocks, slack.NewDividerBlock())
		blocks = append(blocks, util.NewTextContext("*Current Torrents*")...)
		if len(tss) == 0 {
			blocks = append(blocks, util.NewSectionBlockObj("Not found"))
		} else {
			for idx, ts := range tss {
				blocks = append(blocks, util.NewSectionBlockObj(fmt.Sprintf("%d. *%s*\nTags: %s, Progress: %s, AddOn: %s", ts.Index, ts.Name, ts.Tags, ts.Progress, ts.AddOn)))
				if idx+1 >= int(recentNum) {
					break
				}
			}
		}
		blocks = append(blocks, slack.NewDividerBlock())
		blocks = append(blocks, util.NewSectionBlockObj(fmt.Sprintf("Total torrents: %d, Space left: %s", len(rsp.Torrents), util.FormatSpace(rsp.ServerState.FreeSpaceOnDisk))))

		client.PostMessage(channelId, slack.MsgOptionBlocks(blocks...))
	}
}

type TorrentStatus struct {
	Index    int
	Name     string
	Progress string
	AddOn    string
	Tags     string
}

func formatTorrentProgress(f float64) string {
	return fmt.Sprintf("%.2f%%", f*100)
}
