package middleware

import (
	"github.com/penwyp/hydra/shared/log"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
	"github.com/slack-go/slack/socketmode"
)

func MiddlewareAppMentionEvent(evt *socketmode.Event, client *socketmode.Client) {

	logger := log.WebhookSugarLogger()
	eventsAPIEvent, ok := evt.Data.(slackevents.EventsAPIEvent)
	if !ok {
		logger.Infof("Ignored %+v\n", evt)
		return
	}

	ev, ok := eventsAPIEvent.InnerEvent.Data.(*slackevents.AppMentionEvent)
	if !ok {
		logger.Infof("Ignored %+v\n", ev)
		return
	}

	logger.Infof("We have been mentionned in %v\n", ev.Channel)
	_, _, err := client.Client.PostMessage(ev.Channel, slack.MsgOptionText("Yes, hello.", false))
	if err != nil {
		logger.Infof("failed posting message: %v", err)
	}
}
