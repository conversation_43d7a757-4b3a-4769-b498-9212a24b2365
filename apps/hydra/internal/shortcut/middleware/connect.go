package middleware

import (
	"github.com/penwyp/hydra/shared/log"
	"github.com/slack-go/slack/socketmode"
)

func MiddlewareConnecting(evt *socketmode.Event, client *socketmode.Client) {
	log.WebhookSugarLogger().Info("Connecting to Slack with Socket Mode...")
}

func MiddlewareConnectionError(evt *socketmode.Event, client *socketmode.Client) {
	log.WebhookSugarLogger().Info("Connection failed. Retrying later...")
}

func MiddlewareConnected(evt *socketmode.Event, client *socketmode.Client) {
	log.WebhookSugarLogger().Info("Connected to Slack with Socket Mode.")
}

func MiddlewareHello(evt *socketmode.Event, client *socketmode.Client) {
	log.WebhookSugarLogger().Info("Slack say Hello.")
}
