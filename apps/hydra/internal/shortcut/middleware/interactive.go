package middleware

import (
	"github.com/penwyp/hydra/apps/hydra/internal/constants"
	"github.com/penwyp/hydra/apps/hydra/internal/runtime"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut"
	"github.com/penwyp/hydra/apps/hydra/internal/shortcut/opts"
	"github.com/penwyp/hydra/apps/hydra/internal/util"
	"github.com/penwyp/hydra/apps/hydra/pkg/remove"
	"github.com/penwyp/hydra/shared/log"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func MiddlewareInteractive(evt *socketmode.Event, client *socketmode.Client) {
	logger := log.WebhookSugarLogger()

	callback, ok := evt.Data.(slack.InteractionCallback)
	if !ok {
		logger.Infof("Ignored %+v\n", evt)
		return
	}

	logger.Debugf("Interaction received: %+v\n", callback)

	var payload interface{}

	callbackId := callback.CallbackID

	switch callback.Type {
	case slack.InteractionTypeBlockActions:
		for _, action := range callback.ActionCallback.BlockActions {
			actionTs, err := util.ParseUnixTimestampStringToTime(action.ActionTs)
			if err != nil {
				logger.Error("parse event timestamp error", zap.Error(err))
				continue
			}
			if runtime.BeforeStartTime(&actionTs) {
				logger.Debug("receive command before start time, ignore: ", callbackId)
				continue
			}

			switch action.ActionID {
			case string(constants.MoreAction):
				blocks := make([]slack.Block, 0)

				blocks = append(blocks, util.NewTextContext("MoreAction: *"+action.Value+"*")...)

				blocks = append(blocks, slack.NewActionBlock("",
					slack.NewButtonBlockElement(string(constants.DryRemove), action.Value, util.NewPlainTextBlockObj("DryRun")),
					slack.NewButtonBlockElement(string(constants.RunRemove), action.Value, util.NewPlainTextBlockObj("Remove")),
				))

				_, _, err = client.PostMessage(callback.User.ID, slack.MsgOptionBlocks(blocks...))
				if err != nil {
					logger.Errorf("failed posting message: %v", err)
				}
			case string(constants.DryRemove):
				_, _, err = client.PostMessage(callback.User.ID, util.NewTextContextBlockOption("Dry Remove: *"+action.Value+"*"))
				if err != nil {
					logger.Errorf("failed posting message: %v", err)
				}

				hookWriter := &log.SlackWriter{
					ChannelId: callback.User.ID,
					Client:    client,
				}
				log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo, Writers: []zapcore.WriteSyncer{zapcore.AddSync(hookWriter)}})
				remove.RemoveFiles(remove.RemoveOption{
					CanRemove:        false,
					DirSizeThreshold: 0,
					Paths:            []string{action.Value},
				})

			case string(constants.RunRemove):
				_, _, err = client.PostMessage(callback.User.ID, util.NewTextContextBlockOption("Removing: *"+action.Value+"*"))
				if err != nil {
					logger.Errorf("failed posting message: %v", err)
				}

				hookWriter := &log.SlackWriter{
					ChannelId: callback.User.ID,
					Client:    client,
				}
				log.InitLogger(log.LoggerOption{EncodeLevel: zapcore.CapitalLevelEncoder, LogLevel: constants.LogLevelInfo, Writers: []zapcore.WriteSyncer{zapcore.AddSync(hookWriter)}})
				remove.RemoveFiles(remove.RemoveOption{
					CanRemove:        true,
					DirSizeThreshold: 0,
					Paths:            []string{action.Value},
				})
			}
		}

	case slack.InteractionTypeShortcut:
		if callback.Message.EventTimestamp != "" {
			eventTs, err := util.ParseUnixTimestampStringToTime(callback.Message.EventTimestamp)
			if err != nil {
				logger.Error("parse event timestamp error", zap.Error(err))
				return
			}
			if runtime.BeforeStartTime(&eventTs) {
				logger.Debug("receive command before start time, ignore: ", callbackId)
				return
			}
		}

		_, _, err := client.PostMessage(callback.User.ID, util.NewTextContextBlockOption("Received Command: *"+callbackId+"*"))
		if err != nil {
			logger.Errorf("failed posting message: %v", err)
		}

		processor, hit := shortcut.GetShortcutProcessor(callbackId)
		if !hit {
			logger.Warn("unsupported shortcut callbackId received", zap.String("callbackId", callbackId))
			return
		}
		proErr := processor.ProcessMessage(opts.OptionChannelId(callback.User.ID), opts.OptionClient(client))
		if proErr != nil {
			logger.Errorf("failed processing message: %v", proErr)
		}

	case slack.InteractionTypeViewSubmission:
		// See https://api.slack.com/apis/connections/socket-implement#modal
	case slack.InteractionTypeDialogSubmission:
	default:

	}

	client.Ack(*evt.Request, payload)
}
