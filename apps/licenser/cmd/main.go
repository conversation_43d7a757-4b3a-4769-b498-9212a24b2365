package main

import (
	"crypto/md5"
	"flag"
	"fmt"
	"log"
	"time"
)

var machineID string
var days int

func init() {
	flag.StringVar(&machineID, "m", "machine-id", "generated unique machine id")
	flag.IntVar(&days, "d", 60, "license validate days")
}

// 简化的许可证生成函数
func generateSimpleLicense(machineID string, days int) string {
	expireTime := time.Now().AddDate(0, 0, days)
	data := fmt.Sprintf("%s:%d:%s", machineID, days, expireTime.Format("2006-01-02"))
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x-%s-%d", hash, expireTime.Format("20060102"), days)
}

func main() {
	flag.Parse()

	fmt.Printf("Licenser v1.0.0\n")
	fmt.Printf("Machine ID: %s\n", machineID)
	fmt.Printf("Valid Days: %d\n", days)

	license := generateSimpleLicense(machineID, days)
	fmt.Printf("Generated License: %s\n", license)

	log.Printf("License generated successfully for machine %s, valid for %d days", machineID, days)
}
