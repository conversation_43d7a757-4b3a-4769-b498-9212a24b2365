package main

import (
	"context"

	"github.com/penwyp/hydra/apps/tiver/internal/command"
	"github.com/spf13/cobra"
)

var rootCommand = &cobra.Command{
	Use:   "tiver",
	Short: "Tiver: A specialized tool for thread and image management",
	Long: `Tiver is a command-line tool designed for thread management and image downloading.
It provides utilities for refreshing thread data and downloading images from various sources.

Available commands:
  - refresh: Refresh thread data from sources
  - download: Download images from threads`,
	PostRun: postRunFunc,
}

// main 执行真正业务逻辑
func main() {
	addCommandWithPostRunFunc(rootCommand,
		command.NewRefreshCommand(),
		command.NewDownloadCommand(),
		command.NewServeCommand(),
	)
	cobra.CheckErr(rootCommand.ExecuteContext(context.Background()))
}

var postRunFunc = func(cmd *cobra.Command, args []string) {
	// Post-run cleanup logic can be added here
}

func addCommandWithPostRunFunc(root *cobra.Command, cmdArr ...*cobra.Command) {
	for _, cmd := range cmdArr {
		cmd.PostRun = postRunFunc
		root.AddCommand(cmd)
	}
}
