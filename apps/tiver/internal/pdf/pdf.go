package pdf

import (
	"github.com/jung-kurt/gofpdf/v2"
	"github.com/penwyp/hydra/shared/log"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"image"
	"os"
	"path/filepath"
	"sort"
)

func BuildPDF(req BuildPDFRequest) (string, error) {
	return buildPDF(req.ImageBasicFolder, req.PDFBasicFolder, req.ImageName)
}

func buildPDF(imgFolder string, pdfFolder string, imgDir string) (string, error) {
	imageMaterialFolder := filepath.Join(imgFolder, imgDir)
	pdfArtifactPath := filepath.Join(pdfFolder, imgDir+".pdf")
	pdf := gofpdf.New("P", "mm", "A3", "")

	imgPages := make([]string, 0)
	imgExts := make(map[string]string)
	err := filepath.Walk(imageMaterialFolder,
		func(filePath string, info os.FileInfo, err error) error {
			if filePath == imageMaterialFolder {
				return nil
			}
			if info.IsDir() {
				return nil
			}
			if info.Size() < 20*1024 {
				return nil
			}

			fileExt := filepath.Ext(info.Name())
			switch fileExt {
			case ".jpg", ".JPG", ".jpeg", ".JPEG", ".png", ".PNG":
				imgPages = append(imgPages, filePath)
				imgExts[filePath] = imageTp[fileExt]
			default:
				log.Logger.Warn("不支持的文件类型", zap.String("file", filePath))
			}
			return nil
		})
	if err != nil {
		return "", err
	}

	if len(imgPages) == 0 {
		log.Logger.Info("有效图片数量为0,不生成PDF文件", zap.String("imgDir", imgDir))
		return "", nil
	}
	// sort imgPages
	sort.Strings(imgPages)
	log.Logger.Debug("图片排序", zap.String("imageMaterialFolder", imageMaterialFolder), zap.Int("imgPages", len(imgPages)))
	for _, imgPage := range imgPages {
		imageProperty, err := extractImageProperty(imgPage)
		if err != nil {
			log.Logger.Error("解析图片失败", zap.String("file", imgPage), zap.Error(err))
			return "", err
		}
		pdf.AddPageFormat("P", gofpdf.SizeType{Wd: imageProperty.Width, Ht: imageProperty.Height})
		pdf.Image(imgPage, 0, 0, imageProperty.Width, imageProperty.Height, false, imgExts[imgPage], 0, "")
	}

	writeErr := pdf.OutputFileAndClose(pdfArtifactPath)
	if writeErr != nil {
		return "", errors.Wrap(writeErr, "写入PDF文件失败")
	}
	return pdfArtifactPath, nil
}

type ImageProperty struct {
	Width  float64
	Height float64
}

func extractImageProperty(filePath string) (*ImageProperty, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	c, _, err := image.DecodeConfig(file)
	if err != nil {
		return nil, err
	}
	log.Logger.Debug("图片信息", zap.String("file", filePath), zap.Int("width", c.Width), zap.Int("height", c.Height))
	return &ImageProperty{
		Width:  float64(c.Width),
		Height: float64(c.Height),
	}, nil
}

var imageTp = map[string]string{
	".jpg":  "JPG",
	".jpeg": "JPG",
	".png":  "PNG",
}

type BuildPDFRequest struct {
	ImageBasicFolder string
	PDFBasicFolder   string
	ImageName        string
}
