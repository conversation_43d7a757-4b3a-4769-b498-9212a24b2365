package internal

import (
	"sync"
	"time"

	"github.com/gocolly/colly/v2"
	"github.com/penwyp/hydra/apps/tiver/internal/config"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

var (
	location *time.Location
	domains  = []string{"http://sis001.com", "https://sis001.com", "sis001.com", "sis001.com:80", "http://sis001.com:80", "https://sis001.com:80", "https://sis001.com:443"}
	headers  = map[string]string{
		"Accept":                    `text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7`,
		"Accept-Encoding":           `gzip, deflate`,
		"Accept-Language":           `zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7`,
		"Cache-Control":             `max-age=0`,
		"Connection":                `keep-alive`,
		"Cookie":                    `cdb2_cookietime=315360000; cdb2_smile=1D1; cdb2_auth=dDCUrRjCncjp3%2FXjtIu6k4ACerNXaWM1PJ05RVewTl9HS57v1bD1hHMyuKaSlg7t%2FyITKocdhwvz46Xy; cdb2_readapmid=509; cdb2_sid=BNEvi8; cdb2_uvStat=1755968386; cf_clearance=_XUhf9BdI9W.nruZAFTMEZ7A3BMGgvJtLftlCUg.kmA-1755968387-*******-U_BfZnk2.NnCI9bqS53IwzwD.yx.M7AvYY7_Uxbet9o7LEoU2oFwWi1YTxPwoj5m5W7B5jsZ3Mdh6V32j48ejqGqLktn2TSgmUUbsiYIweZFyjMZAaz.IAm6d2XKCDMpa8S39hGDoRECGkzWcCD7lO2eyekDnF1v3odHJInqOWFLvx6sF0ITHwx.1LcJewiN9chtftd0A_JrVWLL0dbAYPG0gKJehlC0IowRdruHhw4uLF00vnY8P6HQacL6Gg4d; cdb2_fuvs=151.60; cdb2_oldtopics=D11807224D12013731D11774555D11611502D12083883D12167670D11908280D12001461D12167617D12071100D12060300D11759429D11838212D12246182D12128980D11935815D11719873D11764286D12127363D12138538D12157737D`,
		"Host":                      `sis001.com`,
		"Pragma":                    `no-cache`,
		"Sec-Ch-Ua":                 `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`,
		"Sec-Ch-Ua-Mobile":          `?0`,
		"Sec-Ch-Ua-Platform":        `"macOS"`,
		"Sec-Fetch-Dest":            `document`,
		"Sec-Fetch-Mode":            `navigate`,
		"Sec-Fetch-Site":            `none`,
		"Sec-Fetch-User":            `?1`,
		"Upgrade-Insecure-Requests": `1`,
		"User-Agent":                `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36`,
	}
)

func GetDefaultCollyCollector(proxy string, disableProxy bool) *colly.Collector {
	cfg := config.LoadConfig()

	c := colly.NewCollector(
		colly.UserAgent(cfg.HTTP.UserAgent),
		colly.AllowedDomains(domains...),
		colly.MaxDepth(2),
		colly.Async(true),
	)

	// Skip proxy setup entirely if disabled
	if !disableProxy {
		// Use proxy from config if not provided as parameter
		proxyToUse := proxy
		if proxyToUse == "" {
			proxyToUse = cfg.HTTP.Proxy
		}

		if proxyToUse != "" {
			if err := c.SetProxy(proxyToUse); err != nil {
				log.Logger.Panic("[COMMON] set proxy failed", zap.Error(err))
			}
		}
	}
	return c
}

func init() {
	location = time.UTC
}

var (
	saveErrorUrl      = make(map[string]int)
	saveErrorUrlMutex sync.RWMutex
)

// GetSaveErrorUrlCount safely retrieves the error count for a URL
func GetSaveErrorUrlCount(url string) int {
	saveErrorUrlMutex.RLock()
	defer saveErrorUrlMutex.RUnlock()
	return saveErrorUrl[url]
}

// IncrementSaveErrorUrl safely increments the error count for a URL
func IncrementSaveErrorUrl(url string) {
	saveErrorUrlMutex.Lock()
	defer saveErrorUrlMutex.Unlock()
	saveErrorUrl[url]++
}

// GetDefaultHeaders returns the default HTTP headers with configurable User-Agent and Cookie
func GetDefaultHeaders() map[string]string {
	cfg := config.LoadConfig()
	defaultHeaders := make(map[string]string)
	for k, v := range headers {
		defaultHeaders[k] = v
	}
	// Override User-Agent and Cookie from config
	defaultHeaders["User-Agent"] = cfg.HTTP.UserAgent
	defaultHeaders["Cookie"] = cfg.HTTP.Cookie
	return defaultHeaders
}
