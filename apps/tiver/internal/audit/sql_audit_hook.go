package audit

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// SQLAuditHook implements sqlhooks.Hooks interface for SQL query auditing
type SQLAuditHook struct {
	logger     *zap.Logger
	enabled    bool
	logFile    string
	mutex      sync.Mutex
	queryStart map[string]time.Time // map[queryID]startTime
}

// NewSQLAuditHook creates a new SQL audit hook
func NewSQLAuditHook(enabled bool) *SQLAuditHook {
	hook := &SQLAuditHook{
		enabled:    enabled,
		queryStart: make(map[string]time.Time),
	}

	if !enabled {
		return hook
	}

	// Setup audit directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		fmt.Printf("Warning: Failed to get home directory: %v\n", err)
		return hook
	}

	auditDir := filepath.Join(homeDir, ".tiver", "audit")
	if err := os.Mk<PERSON>ll(auditDir, 0755); err != nil {
		fmt.Printf("Warning: Failed to create audit directory: %v\n", err)
		return hook
	}

	hook.logFile = filepath.Join(auditDir, "tiver-web-sql.log")

	// Create audit logger
	hook.logger = hook.createAuditLogger()

	return hook
}

// createAuditLogger creates a dedicated logger for SQL audit
func (h *SQLAuditHook) createAuditLogger() *zap.Logger {
	// Create file writer
	file, err := os.OpenFile(h.logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("Warning: Failed to open audit log file: %v\n", err)
		return zap.NewNop()
	}

	// Custom encoder config for audit logs
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:     "time",
		MessageKey:  "msg",
		LineEnding:  zapcore.DefaultLineEnding,
		EncodeTime:  zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05.000"),
		EncodeLevel: zapcore.CapitalLevelEncoder,
	}

	encoder := zapcore.NewConsoleEncoder(encoderConfig)
	writer := zapcore.AddSync(file)
	core := zapcore.NewCore(encoder, writer, zap.InfoLevel)

	return zap.New(core)
}

// Before is called before query execution
func (h *SQLAuditHook) Before(ctx context.Context, query string, args ...interface{}) (context.Context, error) {
	if !h.enabled {
		return ctx, nil
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Generate unique query ID
	queryID := fmt.Sprintf("%p_%d", &query, time.Now().UnixNano())
	h.queryStart[queryID] = time.Now()

	// Log query start
	argsStr := h.formatArgs(args)
	h.logger.Info("QUERY START",
		zap.String("query_id", queryID),
		zap.String("sql", strings.TrimSpace(query)),
		zap.String("args", argsStr),
	)

	// Store query ID in context for After method
	return context.WithValue(ctx, "query_id", queryID), nil
}

// After is called after query execution
func (h *SQLAuditHook) After(ctx context.Context, query string, args ...interface{}) (context.Context, error) {
	if !h.enabled {
		return ctx, nil
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Get query ID from context
	queryID, ok := ctx.Value("query_id").(string)
	if !ok {
		return ctx, nil
	}

	// Calculate duration
	startTime, exists := h.queryStart[queryID]
	if !exists {
		return ctx, nil
	}

	duration := time.Since(startTime)
	delete(h.queryStart, queryID) // Clean up

	// Log query completion
	h.logger.Info("QUERY END",
		zap.String("query_id", queryID),
		zap.Float64("duration_ms", float64(duration.Nanoseconds())/1e6),
		zap.String("status", "success"),
	)

	// Add separator for readability
	h.logger.Info("---")

	return ctx, nil
}

// OnError is called when query execution fails
func (h *SQLAuditHook) OnError(ctx context.Context, err error, query string, args ...interface{}) error {
	if !h.enabled {
		return err
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Get query ID from context
	queryID, ok := ctx.Value("query_id").(string)
	if !ok {
		return err
	}

	// Calculate duration
	startTime, exists := h.queryStart[queryID]
	if exists {
		duration := time.Since(startTime)
		delete(h.queryStart, queryID) // Clean up

		// Log query error
		h.logger.Error("QUERY ERROR",
			zap.String("query_id", queryID),
			zap.Float64("duration_ms", float64(duration.Nanoseconds())/1e6),
			zap.Error(err),
		)

		// Add separator for readability
		h.logger.Info("---")
	}

	return err
}

// formatArgs formats query arguments for logging
func (h *SQLAuditHook) formatArgs(args []interface{}) string {
	if len(args) == 0 {
		return "[]"
	}

	var formattedArgs []string
	for _, arg := range args {
		switch v := arg.(type) {
		case string:
			formattedArgs = append(formattedArgs, fmt.Sprintf("\"%s\"", v))
		case nil:
			formattedArgs = append(formattedArgs, "null")
		default:
			formattedArgs = append(formattedArgs, fmt.Sprintf("%v", v))
		}
	}

	return fmt.Sprintf("[%s]", strings.Join(formattedArgs, ", "))
}

// Close closes the audit logger
func (h *SQLAuditHook) Close() error {
	if h.logger != nil {
		return h.logger.Sync()
	}
	return nil
}