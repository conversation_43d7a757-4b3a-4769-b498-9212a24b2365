/**
* @desc
* <AUTHOR>
* @version : image_download.go, v 0.1 2021/1/29 23:28 pen.wyp Exp $$
 */
package internal

import (
	"context"
	"fmt"

	"github.com/gocolly/colly/v2"
	_const "github.com/penwyp/hydra/apps/tiver/internal/const"
	"github.com/penwyp/hydra/apps/tiver/internal/db"
	"github.com/penwyp/hydra/apps/tiver/internal/progress"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"

	"io"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	pkgparam "github.com/penwyp/hydra/apps/tiver/internal/model/param"
	"github.com/penwyp/hydra/apps/tiver/internal/model/sis"
	"github.com/penwyp/hydra/apps/tiver/internal/pdf"
	"github.com/penwyp/hydra/apps/tiver/internal/util"
)

type ThreadImageDownloader struct {
	ImageProgress *progress.ImageProgress

	forbiddenCache        map[string]bool           // cache for forbidden domains
	failDomainCount       map[string]int            // 记录域名失败次数
	progressMgr           *progress.ProgressManager // 统一进度管理
	param                 *pkgparam.DownloadParam
	waitingDownloadThread chan sis.NormalThread

	headers map[string]string // 新增：请求头
}

// ImageThreadProgress 统计单个线程的图片下载进度
// key为threadInfo.GetValidName()
type ImageThreadProgress struct {
	Total   int
	Success int
	Failed  int
}

func NewThreadImageDownloader(headers map[string]string) *ThreadImageDownloader {
	if headers == nil {
		headers = map[string]string{
			"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36",
		}
	}
	return &ThreadImageDownloader{
		ImageProgress:   progress.NewImageProgress(),
		forbiddenCache:  make(map[string]bool),
		failDomainCount: make(map[string]int),
		progressMgr:     progress.NewProgressManager(),
		headers:         headers,
	}
}

func (i *ThreadImageDownloader) ApplyParam(param *pkgparam.DownloadParam) {
	i.waitingDownloadThread = make(chan sis.NormalThread, param.ThreadImageDownloadGoroutineNum)
	i.param = param
}

func (i *ThreadImageDownloader) FetchThreadToDownloadImage(ctx context.Context) {
	for {
		threads, err := db.FetchThreadsByStatus(ctx, _const.ThreadStatusQueued, 20)
		if err != nil {
			log.Logger.Panic(err.Error())
		}

		if len(threads) == 0 {
			log.Logger.Info("[IMAGE] no more thread to download, sleep 5s")
			time.Sleep(time.Second * 5)
			continue
		} else {
			log.Logger.Info("[IMAGE] got threads to download", zap.Int("threadNum", len(threads)))
		}

		threadIds := make([]int64, 0)
		for _, thread := range threads {
			threadIds = append(threadIds, thread.ID)
		}
		updateErr := db.UpdateThreadImageStatusByIDs(ctx, threadIds, _const.ThreadImageStatusDownloading)
		if updateErr != nil {
			log.Logger.Panic(updateErr.Error())
		}
		for _, thread := range threads {
			i.waitingDownloadThread <- thread
			i.ImageProgress.IncreaseTotalFetchedThreadNum()
		}
	}
}

func (i *ThreadImageDownloader) DispatchThreadToDownloadImages(ctx context.Context, thread sis.NormalThread, param *pkgparam.DownloadParam) {
	cc := GetDefaultCollyCollector(param.ProxyUrl, false)

	i.setHTMLBodyHandler(ctx, cc, param)
	cc.OnRequest(i.updateDownloadingThread())
	cc.OnError(i.retryOnErrorOccur())

	// 设置自定义headers
	cc.OnRequest(func(r *colly.Request) {
		for k, v := range i.headers {
			r.Headers.Set(k, v)
		}
	})

	requestContext := colly.NewContext()
	requestContext.Put("threadInfo", thread)

	link := _const.SISDomainHTTPS + "forum/" + thread.Link

	err := cc.Request("GET", link, nil, requestContext, nil)
	if err != nil {
		log.Logger.Error("[IMAGE] visit failed", zap.String("threadType", thread.ThreadType), zap.String("threadName", thread.GetValidName()), zap.Error(err))
	}
	cc.Wait()
}

func (i *ThreadImageDownloader) setHTMLBodyHandler(ctx context.Context, cc *colly.Collector, param *pkgparam.DownloadParam) {
	const imageSelector = "form[name^=modactions]>div[class^=mainbox]>table>tbody>tr>td[class^=postcontent]>div[class^=postmessage]>div[class^=t_msgfont]"

	cc.OnHTML(imageSelector, func(e *colly.HTMLElement) {
		threadInfo := e.Request.Ctx.GetAny("threadInfo").(sis.NormalThread)
		imageFullPath := threadInfo.GetDataFullPath(path.Join(param.DataDir, "image"))
		imageRemoteFullPath := threadInfo.GetDataFullPath(path.Join(param.DataDir, "image"))
		imageTempFullPath := threadInfo.GetTempDataFullPath(path.Join(param.DataDir, "image"))
		imageTempTypePath := threadInfo.GetTempDataTypePath(path.Join(param.DataDir, "image"))

		i.mkdirFolder(imageFullPath, threadInfo)
		i.mkdirFolder(imageTempFullPath, threadInfo)
		i.mkdirFolder(imageRemoteFullPath, threadInfo)

		log.Logger.Debug("[IMAGE] html fetched, prepare to download images",
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
		)
		imageUris := i.extractImageURIs(e, param)

		i.downloadThreadImagesToPath(threadInfo, param, imageUris, imageTempFullPath)

		i.buildThreadPDFByImages(threadInfo, param, imageUris, imageTempFullPath, imageTempTypePath, imageFullPath)

		log.Logger.Info("[IMAGE] html fetched, image download finished, all finished",
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
		)
	})
}

func (i *ThreadImageDownloader) buildThreadPDFByImages(threadInfo sis.NormalThread, param *pkgparam.DownloadParam, imageUris []string, imageTempFullPath, imageTempTypePath, imageFullPath string) {
	pdfTempTypePath := threadInfo.GetTempDataTypePath(path.Join(param.DataDir, "pdf"))
	pdfRemoteTypePath := path.Join(param.DataDir, "pdf", threadInfo.ThreadType)

	_ = i.mkdirFolder(pdfTempTypePath, threadInfo)
	_ = i.mkdirFolder(pdfRemoteTypePath, threadInfo)

	i.updateThreadPdfStatus(threadInfo, _const.ThreadPDFStatusMaking)
	log.Logger.Info("[IMAGE] html fetched, image download finished, prepare build pdf",
		zap.String("threadType", threadInfo.ThreadType),
		zap.String("threadName", threadInfo.GetValidName()),
	)
	_, buildErr := pdf.BuildPDF(pdf.BuildPDFRequest{
		ImageBasicFolder: imageTempTypePath,
		PDFBasicFolder:   pdfTempTypePath,
		ImageName:        threadInfo.GetValidName(),
	})
	if buildErr != nil {
		i.updateThreadPdfStatus(threadInfo, _const.ThreadPDFStatusMakeFailed)
		log.Logger.Error("[IMAGE] html fetched, image download finished, build pdf failed",
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
			zap.Error(buildErr),
		)
		return
	}
	i.updateThreadPdfStatus(threadInfo, _const.ThreadPDFStatusMoving)
	log.Logger.Info("[IMAGE] html fetched, image download finished, move folder",
		zap.String("threadType", threadInfo.ThreadType),
		zap.String("threadName", threadInfo.GetValidName()),
	)
	err := i.moveContents(imageTempFullPath, imageFullPath, MoveByRename)
	if err != nil {
		i.updateThreadPdfStatus(threadInfo, _const.ThreadPDFStatusMoveFailed)
		log.Logger.Error("[IMAGE] html fetched, image download finished, move folder failed",
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
			zap.Error(err),
		)
	}
	_ = os.RemoveAll(imageTempFullPath)

	i.updateThreadPdfStatus(threadInfo, _const.ThreadPDFStatusFinish)
}

func (i *ThreadImageDownloader) downloadThreadImagesToPath(threadInfo sis.NormalThread, param *pkgparam.DownloadParam, imageUris []string, imageTempFullPath string) {
	if len(imageUris) == 0 {
		log.Logger.Debug("[IMAGE] html fetched, no image to download, skip",
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
		)
	}

	log.Logger.Info("[IMAGE] html fetched, image download start",
		zap.String("threadType", threadInfo.ThreadType),
		zap.String("threadName", threadInfo.GetValidName()),
	)
	i.downloadImages(imageUris, threadInfo, imageTempFullPath, param)
	i.updateThreadImageStatus(threadInfo, _const.ThreadImageStatusDownloaded)
}

func (i *ThreadImageDownloader) extractImageURIs(e *colly.HTMLElement, param *pkgparam.DownloadParam) []string {
	imageUris := make([]string, 0)
	for _, imageUri := range e.ChildAttrs("img", "src") {
		u, err := url.Parse(imageUri)
		if err != nil || u.Host == "" {
			continue
		}
		if i.forbiddenCache[u.Host] {
			log.Logger.Warn("[IMAGE] skip forbidden domain", zap.String("domain", u.Host))
			continue
		}
		imageUris = append(imageUris, imageUri)
	}
	return imageUris
}

func (i *ThreadImageDownloader) retryOnErrorOccur() func(rs *colly.Response, err error) {
	return func(rs *colly.Response, err error) {
		log.Logger.Info("[IMAGE] fetching html contents failed", zap.String("url", rs.Request.URL.String()), zap.Error(err))
		err = rs.Request.Retry()
		return
	}
}

func (i *ThreadImageDownloader) updateDownloadingThread() func(r *colly.Request) {
	return func(r *colly.Request) {
		for k, v := range headers {
			r.Headers.Set(k, v)
		}

		threadInfo := r.Ctx.GetAny("threadInfo").(sis.NormalThread)
		log.Logger.Info("[IMAGE] html fetching",
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
		)
		i.updateThreadImageStatus(threadInfo, _const.ThreadImageStatusDownloading)
	}
}

func (i *ThreadImageDownloader) updateThreadImageStatus(thread sis.NormalThread, status string) {
	_, err := db.UpdateThreadImageStatus(context.Background(), thread, status)
	if err != nil {
		log.Logger.Error(err.Error())
	}
}

func (i *ThreadImageDownloader) updateThreadPdfStatus(thread sis.NormalThread, status string) {
	_, err := db.UpdateThreadPdfStatus(context.Background(), thread, status)
	if err != nil {
		log.Logger.Error(err.Error())
	}
}

type MoveFileFun func(string, string) error

var MoveByCopy MoveFileFun = moveFileByCopy
var MoveByRename MoveFileFun = os.Rename

func moveFileByCopy(artifactPath string, fullPath string) error {
	log.Logger.Debug("[IMAGE] moveFile", zap.String("artifactPath", artifactPath), zap.String("fullPath", fullPath))
	if !util.Exists(artifactPath) {
		log.Logger.Debug("[IMAGE] moveFile, artifactPath not exists", zap.String("artifactPath", artifactPath))
		return nil
	}
	if util.Exists(fullPath) {
		log.Logger.Debug("[IMAGE] moveFile, fullPath exists, remove it", zap.String("fullPath", fullPath))
		err := os.RemoveAll(fullPath)
		if err != nil {
			log.Logger.Error("[IMAGE] moveFile remove failed", zap.String("fullPath", fullPath), zap.Error(err))
			return err
		}
	}

	sourceFile, err := os.Open(artifactPath)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destinationFile, err := os.Create(fullPath)
	if err != nil {
		return err
	}
	defer destinationFile.Close()

	_, err = io.Copy(destinationFile, sourceFile)
	if err != nil {
		return err
	}

	// 复制成功后，删除源文件
	err = os.Remove(artifactPath)
	if err != nil {
		return err
	}
	return nil
}

// ImageTask 图片下载任务结构体
// 包含图片url、保存路径、文件名、重试次数等
type ImageTask struct {
	Idx        int
	Total      int
	ThreadInfo sis.NormalThread
	ImageUrl   string
	FolderPath string
	Param      *pkgparam.DownloadParam
	Retry      int
}

// StartImageDownloadWorkerPool 启动图片下载worker pool，支持WaitGroup
func (i *ThreadImageDownloader) StartImageDownloadWorkerPool(workerNum int, taskChan <-chan ImageTask, wg *sync.WaitGroup) {
	for w := 0; w < workerNum; w++ {
		go func(workerId int) {
			for task := range taskChan {
				i.downloadImageWithRetry(task)
				wg.Done()
			}
		}(w)
	}
}

// downloadImageWithRetry 带重试的图片下载
func (i *ThreadImageDownloader) downloadImageWithRetry(task ImageTask) {
	maxRetry := 3
	var lastErr error
	threadKey := task.ThreadInfo.GetValidName()
	for try := 0; try < maxRetry; try++ {
		// 断点续传：如果文件已存在且大小大于0，直接跳过
		imageIndex := task.Idx + 1
		arr := strings.Split(task.ImageUrl, ".")
		filenameExtension := arr[len(arr)-1]
		filePath := path.Join(task.FolderPath, fmt.Sprintf("%d.%s", imageIndex, filenameExtension))
		if util.Exists(filePath) {
			fileInfo, err := os.Stat(filePath)
			if err == nil && fileInfo.Size() > 0 {
				i.progressMgr.MarkSuccess(threadKey)
				log.LogImageEvent("skip", threadKey, imageIndex, "file exists", nil)
				return
			}
		}
		// 下载
		err := util.DownloadImage(task.FolderPath, task.ImageUrl, fmt.Sprintf("%d.%s", imageIndex, filenameExtension), task.Param.ProxyUrl, nil)
		if err == nil {
			i.progressMgr.MarkSuccess(threadKey)
			log.LogImageEvent("download", threadKey, imageIndex, "success", nil)
			return
		}
		lastErr = err
		log.LogImageEvent("download", threadKey, imageIndex, fmt.Sprintf("failed, retry %d", try+1), err)
		time.Sleep(time.Second * 2)
	}
	// 统计失败域名
	u, err := url.Parse(task.ImageUrl)
	if err == nil && u.Host != "" {
		i.failDomainCount[u.Host]++
		if i.failDomainCount[u.Host] >= 3 {
			i.forbiddenCache[u.Host] = true
			log.LogImageEvent("forbidden", threadKey, task.Idx+1, "domain forbidden", fmt.Errorf("domain: %s", u.Host))
		}
	}
	i.progressMgr.MarkFailed(threadKey)
	log.LogImageEvent("download", threadKey, task.Idx+1, "failed after retries", lastErr)
}

// 修改downloadImages为worker pool+WaitGroup实现
func (i *ThreadImageDownloader) downloadImages(imageUris []string, threadInfo sis.NormalThread, tempFolderPath string, param *pkgparam.DownloadParam) {
	totalLength := len(imageUris)
	if totalLength == 0 {
		return
	}
	workerNum := int(param.ThreadImageDownloadGoroutineNum)
	taskChan := make(chan ImageTask, totalLength)
	var wg sync.WaitGroup
	wg.Add(totalLength)
	// 初始化线程进度
	threadKey := threadInfo.GetValidName()
	i.progressMgr.AddItem(threadKey, totalLength)
	// 启动worker pool
	i.StartImageDownloadWorkerPool(workerNum, taskChan, &wg)
	// 投递任务
	for idx, imageUrl := range imageUris {
		taskChan <- ImageTask{
			Idx:        idx,
			Total:      totalLength,
			ThreadInfo: threadInfo,
			ImageUrl:   imageUrl,
			FolderPath: tempFolderPath,
			Param:      param,
			Retry:      0,
		}
	}
	close(taskChan)
	// 启动进度日志输出goroutine
	done := make(chan struct{})
	go i.progressMgr.DisplayProgress(done)
	wg.Wait()
	close(done)
	i.ImageProgress.IncreaseTotalThreadNum()
}

func (i *ThreadImageDownloader) mkdirFolder(folderPath string, threadInfo sis.NormalThread) error {
	log.Logger.Debug("[IMAGE] mkdiring",
		zap.String("threadType", threadInfo.ThreadType),
		zap.String("threadName", threadInfo.GetValidName()),
		zap.String("folderPath", folderPath),
	)
	if !util.Exists(folderPath) {
		log.Logger.Debug("[IMAGE] mkdir", zap.String("folderPath", folderPath),
			zap.String("threadType", threadInfo.ThreadType),
			zap.String("threadName", threadInfo.GetValidName()),
		)
		err := os.MkdirAll(folderPath, 0777)
		if err != nil {
			log.Logger.Error("[IMAGE] mkdir failed", zap.String("folderPath", folderPath), zap.Error(err))
			return err
		}
	}
	return nil
}

func (i *ThreadImageDownloader) moveContents(src, dest string, moveFileFunc MoveFileFun) error {
	// 1. 读取文件夹A中的内容
	contents, err := os.ReadDir(src)
	if err != nil {
		return err
	}
	srcSort, destSort := i.removeCommonPrefix(src, dest)
	log.Logger.Info("[IMAGE] moveContents", zap.String("src", srcSort), zap.String("dest", destSort), zap.Int("contentsLen", len(contents)))

	// 2. 逐个将A中的文件或子文件夹移动到B
	for _, item := range contents {
		srcPath := filepath.Join(src, item.Name())
		destPath := filepath.Join(dest, item.Name())

		if item.IsDir() {
			// 如果是子文件夹，递归调用moveContents
			err = i.moveContents(srcPath, destPath, moveFileFunc)
			if err != nil {
				return err
			}
		} else {
			// 如果是文件，移动到目标文件夹
			err = moveFileFunc(srcPath, destPath)
			if err != nil {
				return err
			}
		}
	}

	// 3. 删除文件夹A
	return os.RemoveAll(src)
}

func (i *ThreadImageDownloader) removeCommonPrefix(str1, str2 string) (string, string) {
	// 找到两个字符串的最小长度
	minLen := len(str1)
	if len(str2) < minLen {
		minLen = len(str2)
	}

	// 寻找相同前缀的长度
	commonLen := 0
	for commonLen < minLen && str1[commonLen] == str2[commonLen] {
		commonLen++
	}

	// 删除相同前缀并返回
	return str1[commonLen:], str2[commonLen:]
}
