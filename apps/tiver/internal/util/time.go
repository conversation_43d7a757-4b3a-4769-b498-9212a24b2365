/**
* @desc
* <AUTHOR>
* @version : time.go, v 0.1 2021/1/23 00:49 pen.wyp Exp $$
 */
package util

import (
	"fmt"
	"time"

	_const "github.com/penwyp/hydra/apps/tiver/internal/const"
)

// ParseTime 统一的时间字符串解析函数
// str: 时间字符串
// loc: 时区
// 返回解析后的time.Time和错误
func ParseTime(str string, loc *time.Location) (time.Time, error) {
	// 中文注释：支持多种常见格式
	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-1-2 15:04:05",
		"2006-01-02 15:04",
		"2006-1-2 15:04",
		"2006-01-02",
		"2006-1-2",
		"01-02 15:04",
		"1-2 15:04",
		"15:04",
	}
	for _, layout := range layouts {
		if t, err := time.ParseInLocation(layout, str, loc); err == nil {
			return t, nil
		}
	}
	return time.Time{}, fmt.Errorf("ParseTime failed, str=%s", str)
}

func ParseTimeOld(value string, loc *time.Location) (t time.Time, err error) {

	for _, timeFormat := range []string{
		_const.DateFormat, _const.ShortDateFormat, _const.ShortShortDateFormat,
		_const.TimeFormat, _const.ShortTimeFormat, _const.ShortShortTimeFormat,
		_const.SisTimeFormat, _const.ShortSisTimeFormat, _const.ShortShortSisTimeFormat,
	} {
		t, err = time.ParseInLocation(timeFormat, value, loc)
		if err == nil {
			return
		}
	}
	return
}
