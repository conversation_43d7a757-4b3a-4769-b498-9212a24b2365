/**
* @desc
* <AUTHOR>
* @version : net.go, v 0.1 2021/1/30 00:47 pen.wyp Exp $$
 */
package util

import (
	"github.com/go-resty/resty/v2"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
	"os"
	"path"
	"time"
)

func DownloadImage(folder, imageUri, imageName, proxy string, headers map[string]string) (err error) {
	if !Exists(folder) {
		log.Logger.Debug("[UTIL] mkdir -p ", zap.String("folder", folder))
		err = os.MkdirAll(folder, 0777)
		if err != nil {
			return
		}
	}
	client := resty.New().
		SetRetryCount(1).
		SetRetryWaitTime(time.Second * time.Duration(5)).
		SetHeaders(headers).
		SetTimeout(time.Minute * time.Duration(5))

	if proxy != "" {
		client.SetProxy(proxy)
	}

	response, err := client.R().Get(imageUri)
	if err != nil {
		return
	}
	f, err := os.Create(path.Join(folder, imageName))
	if err != nil {
		return
	}
	_, err = f.Write(response.Body())
	return
}
