/**
* @desc
* <AUTHOR>
* @version : time_test.go.go, v 0.1 2021/1/23 00:51 pen.wyp Exp $$
 */
package util

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestParseTime(t *testing.T) {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	tests := []struct {
		input  string
		expect bool
	}{
		{"2024-07-01 12:34:56", true},
		{"2024-07-01", true},
		{"07-01 12:34", true},
		{"12:34", true},
		{"invalid", false},
	}
	for _, tt := range tests {
		_, err := ParseTime(tt.input, loc)
		if tt.expect {
			assert.NoError(t, err, "input: %s", tt.input)
		} else {
			assert.Error(t, err, "input: %s", tt.input)
		}
	}
}
