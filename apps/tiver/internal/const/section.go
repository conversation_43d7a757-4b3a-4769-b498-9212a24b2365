package _const

type SectionId int

// 卡通动漫
const SectionKatongDongman SectionId = 60

// 长篇收藏
const SectionChangPianShouCang SectionId = 334

// 旧文展览馆
const SectionJiuWenZhanLanGuan SectionId = 359

// 成人原创
const SectionChengRenYuanCuan SectionId = 385

// 成人转载
const SectionChengRenZhuanZai SectionId = 368

func (i SectionId) String() string {
	switch i {
	case SectionChengRenYuanCuan:
		return "成人原创"
	case SectionChangPianShouCang:
		return "长篇收藏"
	case SectionKatongDongman:
		return "卡通动漫"
	case SectionChengRenZhuanZai:
		return "成人转载"
	case SectionJiuWenZhanLanGuan:
		return "旧文展览馆"

	}

	return ""

}
