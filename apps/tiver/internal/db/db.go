/**
* @desc
* <AUTHOR>
* @version : db.go, v 0.1 2021/1/22 23:43 pen.wyp Exp $$
 */
package db

import (
	"context"
	"crypto/tls"
	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/penwyp/hydra/apps/tiver/internal/config"
)

var (
	Conn *sqlx.DB
)

func InitDB() (err error) {
	cfg := config.LoadConfig()

	_ = mysql.RegisterTLSConfig(cfg.Database.TLSName, &tls.Config{
		MinVersion: tls.VersionTLS12,
		ServerName: cfg.Database.Host,
	})

	var connErr, setErr error

	Conn, connErr = sqlx.Connect("mysql", cfg.Database.DSN)
	if connErr != nil {
		return connErr
	}

	// Use UTC for all database operations
	_, setErr = Conn.Exec(`set time_zone = '+0:00'`)
	if setErr != nil {
		return setErr
	}

	// Ensure tables exist
	ctx := context.Background()
	if err := EnsureTablesExist(ctx); err != nil {
		return err
	}

	// Ensure indexes exist
	if err := EnsureIndexesExist(ctx); err != nil {
		return err
	}

	return
}
