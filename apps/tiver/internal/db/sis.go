/**
* @desc
* <AUTHOR>
* @version : sis.go, v 0.1 2021/1/22 23:43 pen.wyp Exp $$
 */
package db

import (
	"context"
	"strings"

	"github.com/penwyp/hydra/apps/tiver/internal/model/sis"
)

func UpsertNormalThread(ctx context.Context, ns sis.NormalThread) (lastInsertId int64, err error) {
	sql := "INSERT INTO `tiver`.`sis_normal_thread` SET thread_type=?,forum_type=?,section_id=?,section_name=?,name=?,link=?,full_link=?,url_digest=?,total_reply=?,total_click=?,author=?,first_upload_time=?,last_post_time=?,thumbs=?,is_forbidden=?" +
		" ON DUPLICATE KEY UPDATE " +
		" link=?,full_link=?,forum_type=?,section_id=?,section_name=?,total_reply=?,total_click=?,first_upload_time=?,last_post_time=?,thumbs=?,is_forbidden=? "
	r, err := Conn.ExecContext(ctx, sql, ns.ThreadType, ns.ForumType, ns.SectionId, ns.SectionName, ns.GetValidName(), ns.Link, ns.FullLink, ns.GetUrlDigest(), ns.TotalReply, ns.TotalClick, ns.Author, ns.FirstUploadTime, ns.LastPostTime, ns.Thumbs, ns.IsForbidden,
		ns.Link, ns.FullLink, ns.ForumType, ns.SectionId, ns.SectionName, ns.TotalReply, ns.TotalClick, ns.FirstUploadTime, ns.LastPostTime, ns.Thumbs, ns.IsForbidden)
	if err != nil {
		return
	}
	return r.LastInsertId()
}

func UpsertThreadStatus(ctx context.Context, ns sis.ThreadStatus) (lastInsertId int64, err error) {
	sql := "INSERT INTO `tiver`.`sis_thread_status` SET url_digest=?, image_status=?, pdf_status=? " +
		" ON DUPLICATE KEY UPDATE " +
		" image_status=?, pdf_status=? "
	r, err := Conn.ExecContext(ctx, sql, ns.UrlDigest, ns.ImageStatus, ns.PdfStatus,
		ns.ImageStatus, ns.PdfStatus)
	if err != nil {
		return
	}
	return r.LastInsertId()
}

func UpdateThreadImageStatus(ctx context.Context, ns sis.NormalThread, status string) (lastInsertId int64, err error) {
	sql := "UPDATE `tiver`.`sis_thread_status` SET image_status=? WHERE url_digest=?"
	r, err := Conn.ExecContext(ctx, sql, status, ns.GetUrlDigest())
	if err != nil {
		return
	}
	return r.LastInsertId()
}

func UpdateThreadPdfStatus(ctx context.Context, ns sis.NormalThread, status string) (lastInsertId int64, err error) {
	sql := "UPDATE `tiver`.`sis_thread_status` SET pdf_status=? WHERE url_digest=?"
	r, err := Conn.ExecContext(ctx, sql, status, ns.GetUrlDigest())
	if err != nil {
		return
	}
	return r.LastInsertId()
}

func FetchThreadsByStatus(ctx context.Context, status string, limit int) (threads []sis.NormalThread, err error) {
	sql := "SELECT s.id,s.thread_type,s.forum_type,s.section_id,s.section_name,s.name,s.author,s.link,s.full_link,s.total_reply,s.total_click,s.thumbs,s.is_forbidden,s.first_upload_time,s.last_post_time,s.url_digest,s.create_time,s.update_time FROM tiver.sis_normal_thread s JOIN tiver.sis_thread_status t ON t.url_digest=s.url_digest WHERE t.image_status=? LIMIT ?"
	err = Conn.SelectContext(ctx, &threads, sql, status, limit)
	return
}

func UpdateThreadImageStatusByIDs(ctx context.Context, ids []int64, status string) (err error) {
	sql := "UPDATE `tiver`.`sis_thread_status` SET image_status=? WHERE id IN (?)"
	_, err = Conn.ExecContext(ctx, sql, status, ids)
	return
}

// UpsertNormalThreadBatch 批量插入或更新NormalThread
// threads: 需要插入或更新的NormalThread切片
// 返回插入的数量和错误
func UpsertNormalThreadBatch(ctx context.Context, threads []sis.NormalThread) (int, error) {
	// 中文注释：如果传入为空，直接返回
	if len(threads) == 0 {
		return 0, nil
	}

	// 构建批量SQL
	sql := "INSERT INTO `tiver`.`sis_normal_thread` (thread_type, forum_type, section_id, section_name, name, link, full_link, url_digest, total_reply, total_click, author, first_upload_time, last_post_time, thumbs, is_forbidden) VALUES "
	vals := make([]interface{}, 0, len(threads)*15)
	placeholders := make([]string, 0, len(threads))
	for _, ns := range threads {
		placeholders = append(placeholders, "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)")
		vals = append(vals, ns.ThreadType, ns.ForumType, ns.SectionId, ns.SectionName, ns.GetValidName(), ns.Link, ns.FullLink, ns.GetUrlDigest(), ns.TotalReply, ns.TotalClick, ns.Author, ns.FirstUploadTime, ns.LastPostTime, ns.Thumbs, ns.IsForbidden)
	}
	sql += strings.Join(placeholders, ",")
	sql += " ON DUPLICATE KEY UPDATE link=VALUES(link), full_link=VALUES(full_link), forum_type=VALUES(forum_type), section_id=VALUES(section_id), section_name=VALUES(section_name), total_reply=VALUES(total_reply), total_click=VALUES(total_click), first_upload_time=VALUES(first_upload_time), last_post_time=VALUES(last_post_time), thumbs=VALUES(thumbs), is_forbidden=VALUES(is_forbidden)"

	// 执行批量插入
	res, err := Conn.ExecContext(ctx, sql, vals...)
	if err != nil {
		return 0, err
	}
	rows, _ := res.RowsAffected()
	return int(rows), nil
}

// EnsureTablesExist creates the sis_normal_thread and sis_thread_status tables if they don't exist
func EnsureTablesExist(ctx context.Context) error {
	// Create sis_normal_thread table
	createThreadTableSQL := `
	CREATE TABLE IF NOT EXISTS tiver.sis_normal_thread (
		id BIGINT AUTO_INCREMENT PRIMARY KEY,
		thread_type VARCHAR(50) NOT NULL DEFAULT '',
		forum_type VARCHAR(50) NOT NULL DEFAULT '',
		section_id INT NOT NULL DEFAULT 0,
		section_name VARCHAR(100) NOT NULL DEFAULT '',
		name VARCHAR(500) NOT NULL DEFAULT '',
		link VARCHAR(500) NOT NULL DEFAULT '',
		full_link VARCHAR(500) NOT NULL DEFAULT '',
		url_digest VARCHAR(32) NOT NULL DEFAULT '',
		total_reply BIGINT NOT NULL DEFAULT 0,
		total_click BIGINT NOT NULL DEFAULT 0,
		author VARCHAR(100) NOT NULL DEFAULT '',
		thumbs BIGINT NOT NULL DEFAULT 0,
		is_forbidden BOOLEAN NOT NULL DEFAULT FALSE,
		first_upload_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		last_post_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		UNIQUE KEY uk_url_digest (url_digest)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`

	if _, err := Conn.ExecContext(ctx, createThreadTableSQL); err != nil {
		return err
	}

	// Create sis_thread_status table
	createStatusTableSQL := `
	CREATE TABLE IF NOT EXISTS tiver.sis_thread_status (
		id BIGINT AUTO_INCREMENT PRIMARY KEY,
		url_digest VARCHAR(32) NOT NULL DEFAULT '',
		image_status VARCHAR(50) NOT NULL DEFAULT 'pending',
		pdf_status VARCHAR(50) NOT NULL DEFAULT 'pending',
		create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
		update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		UNIQUE KEY uk_url_digest (url_digest)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;`

	if _, err := Conn.ExecContext(ctx, createStatusTableSQL); err != nil {
		return err
	}

	return nil
}

// EnsureIndexesExist creates optimized indexes for the sis_normal_thread and sis_thread_status tables
func EnsureIndexesExist(ctx context.Context) error {
	// Check and drop old inefficient/redundant indexes first
	oldIndexesToDrop := []string{
		"idx_thread_type",
		"idx_forum_type", 
		"idx_section_id",
		"idx_thread_section",
		"idx_total_click", // Redundant - covered by idx_upload_click
		"idx_thread_section_upload", // Rarely used based on query patterns
	}
	
	for _, indexName := range oldIndexesToDrop {
		// Check if index exists first
		var indexExists int
		checkSQL := `SELECT COUNT(*) FROM information_schema.statistics 
					WHERE table_schema = 'tiver' 
					AND table_name = 'sis_normal_thread' 
					AND index_name = ?`
		err := Conn.GetContext(ctx, &indexExists, checkSQL, indexName)
		if err == nil && indexExists > 0 {
			// Index exists, drop it
			dropSQL := `ALTER TABLE tiver.sis_normal_thread DROP INDEX ` + indexName
			_, err := Conn.ExecContext(ctx, dropSQL)
			if err != nil && !strings.Contains(err.Error(), "check that column/key exists") {
				// Log error but continue - don't fail the entire operation
			}
		}
	}

	// Optimized composite indexes for sis_normal_thread table based on query patterns
	threadIndexes := []string{
		// Most common query pattern: section_id + first_upload_time with various sort orders
		// For ORDER BY total_click DESC
		`CREATE INDEX IF NOT EXISTS idx_section_upload_click ON tiver.sis_normal_thread (section_id, first_upload_time, total_click DESC)`,
		
		// For ORDER BY update_time DESC  
		`CREATE INDEX IF NOT EXISTS idx_section_upload_update ON tiver.sis_normal_thread (section_id, first_upload_time, update_time DESC)`,
		
		// For ORDER BY total_reply DESC
		`CREATE INDEX IF NOT EXISTS idx_section_upload_reply ON tiver.sis_normal_thread (section_id, first_upload_time, total_reply DESC)`,
		
		// For ORDER BY thumbs DESC
		`CREATE INDEX IF NOT EXISTS idx_section_upload_thumbs ON tiver.sis_normal_thread (section_id, first_upload_time, thumbs DESC)`,
		
		// For ORDER BY first_upload_time (ASC/DESC)
		`CREATE INDEX IF NOT EXISTS idx_section_upload_time ON tiver.sis_normal_thread (section_id, first_upload_time DESC)`,
		
		// Composite index for forum_type grouping and filtering
		`CREATE INDEX IF NOT EXISTS idx_forum_section ON tiver.sis_normal_thread (forum_type, section_id)`,
		
		// Index for queries without section_id filter but with first_upload_time
		`CREATE INDEX IF NOT EXISTS idx_upload_click ON tiver.sis_normal_thread (first_upload_time, total_click DESC)`,
		
		// Keep essential single indexes for specific queries
		`CREATE INDEX IF NOT EXISTS idx_author ON tiver.sis_normal_thread (author)`,
		`CREATE INDEX IF NOT EXISTS idx_last_post_time ON tiver.sis_normal_thread (last_post_time)`,
		`CREATE INDEX IF NOT EXISTS idx_create_time ON tiver.sis_normal_thread (create_time)`,
		`CREATE INDEX IF NOT EXISTS idx_update_time ON tiver.sis_normal_thread (update_time)`,
	}

	for _, indexSQL := range threadIndexes {
		if _, err := Conn.ExecContext(ctx, indexSQL); err != nil {
			// Check if it's a duplicate key error (index already exists in older MySQL versions)
			if !strings.Contains(err.Error(), "Duplicate key name") && 
			   !strings.Contains(err.Error(), "already exists") {
				// Return detailed error for debugging
				return err
			}
		}
	}

	// Indexes for sis_thread_status table
	statusIndexes := []string{
		`CREATE INDEX IF NOT EXISTS idx_image_status ON tiver.sis_thread_status (image_status)`,
		`CREATE INDEX IF NOT EXISTS idx_pdf_status ON tiver.sis_thread_status (pdf_status)`,
		`CREATE INDEX IF NOT EXISTS idx_digest_image ON tiver.sis_thread_status (url_digest, image_status)`,
		`CREATE INDEX IF NOT EXISTS idx_create_time ON tiver.sis_thread_status (create_time)`,
		`CREATE INDEX IF NOT EXISTS idx_update_time ON tiver.sis_thread_status (update_time)`,
	}

	for _, indexSQL := range statusIndexes {
		if _, err := Conn.ExecContext(ctx, indexSQL); err != nil {
			// Check if it's a duplicate key error (index already exists in older MySQL versions)
			if !strings.Contains(err.Error(), "Duplicate key name") && 
			   !strings.Contains(err.Error(), "already exists") {
				return err
			}
		}
	}

	return nil
}

// VerifyIndexes checks if all expected indexes exist and returns a status report
func VerifyIndexes(ctx context.Context) (map[string]bool, error) {
	expectedIndexes := []string{
		"idx_section_upload_click",
		"idx_section_upload_update",
		"idx_section_upload_reply",
		"idx_section_upload_thumbs",
		"idx_section_upload_time",
		"idx_forum_section", 
		"idx_upload_click",
		"idx_author",
		"idx_last_post_time",
		"idx_create_time",
		"idx_update_time",
	}
	
	result := make(map[string]bool)
	
	for _, indexName := range expectedIndexes {
		var indexExists int
		checkSQL := `SELECT COUNT(*) FROM information_schema.statistics 
					WHERE table_schema = 'tiver' 
					AND table_name = 'sis_normal_thread' 
					AND index_name = ?`
		err := Conn.GetContext(ctx, &indexExists, checkSQL, indexName)
		if err != nil {
			return nil, err
		}
		result[indexName] = indexExists > 0
	}
	
	return result, nil
}
