package repository

import (
	"crypto/tls"
	"database/sql"
	"fmt"
	"sort"
	"strings"

	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/penwyp/hydra/apps/tiver/internal/audit"
	"github.com/penwyp/hydra/apps/tiver/internal/config"
	"github.com/penwyp/hydra/apps/tiver/internal/model/sis"
	"github.com/penwyp/hydra/apps/tiver/internal/model/structure"
	"github.com/qustavo/sqlhooks/v2"
)

type ThreadQueryParams struct {
	ThreadType           string
	ForumType            string
	SectionId            *int
	Name                 string
	OrderBy              string
	Order                string
	Page                 int
	PageSize             int
	FirstUploadThreshold string // Format: "2006-01-02"
}

type ThreadRepository struct {
	db *sqlx.DB
}

func NewThreadRepository() *ThreadRepository {
	cfg := config.LoadConfig()
	
	// Register TLS config
	_ = mysql.RegisterTLSConfig(cfg.Database.TLSName, &tls.Config{
		MinVersion: tls.VersionTLS12,
		ServerName: cfg.Database.Host,
	})

	// Create audit hook
	auditHook := audit.NewSQLAuditHook(cfg.App.Audit.SQLEnabled)
	
	// Register MySQL driver with audit hooks
	sql.Register("mysql-with-audit", sqlhooks.Wrap(&mysql.MySQLDriver{}, auditHook))

	// Connect using the audited driver
	db, err := sqlx.Connect("mysql-with-audit", cfg.Database.DSN)
	if err != nil {
		panic(fmt.Sprintf("Failed to connect to database: %v", err))
	}

	// Use UTC for all database operations
	_, _ = db.Exec(`set time_zone = '+0:00'`)

	return &ThreadRepository{
		db: db,
	}
}

func (r *ThreadRepository) GetThreads(params ThreadQueryParams) ([]sis.NormalThread, int, error) {
	// Build base query
	query := `SELECT * FROM sis_normal_thread WHERE 1=1`
	countQuery := `SELECT COUNT(*) FROM sis_normal_thread WHERE 1=1`
	args := []interface{}{}

	// Add filters
	if params.ThreadType != "" {
		query += ` AND thread_type = ?`
		countQuery += ` AND thread_type = ?`
		args = append(args, params.ThreadType)
	}

	if params.ForumType != "" {
		query += ` AND forum_type = ?`
		countQuery += ` AND forum_type = ?`
		args = append(args, params.ForumType)
	}

	if params.SectionId != nil {
		query += ` AND section_id = ?`
		countQuery += ` AND section_id = ?`
		args = append(args, *params.SectionId)
	}

	if params.Name != "" {
		query += ` AND name LIKE ?`
		countQuery += ` AND name LIKE ?`
		args = append(args, "%"+params.Name+"%")
	}

	if params.FirstUploadThreshold != "" {
		query += ` AND first_upload_time >= ?`
		countQuery += ` AND first_upload_time >= ?`
		args = append(args, params.FirstUploadThreshold)
	}

	// Get total count
	var total int
	countArgs := make([]interface{}, len(args))
	copy(countArgs, args)
	err := r.db.Get(&total, countQuery, countArgs...)
	if err != nil {
		return nil, 0, err
	}

	// Add ordering
	orderBy := params.OrderBy
	if orderBy == "" {
		orderBy = "update_time"
	}

	// Validate order by field
	validOrderFields := map[string]bool{
		"total_reply":       true,
		"total_click":       true,
		"thumbs":            true,
		"update_time":       true,
		"create_time":       true,
		"first_upload_time": true,
	}

	if !validOrderFields[orderBy] {
		orderBy = "update_time"
	}

	order := strings.ToUpper(params.Order)
	if order != "ASC" && order != "DESC" {
		order = "DESC"
	}

	query += fmt.Sprintf(` ORDER BY %s %s`, orderBy, order)

	// Add pagination
	offset := (params.Page - 1) * params.PageSize
	query += ` LIMIT ? OFFSET ?`
	args = append(args, params.PageSize, offset)

	// Execute query
	var threads []sis.NormalThread
	err = r.db.Select(&threads, query, args...)
	if err != nil {
		return nil, 0, err
	}

	return threads, total, nil
}

func (r *ThreadRepository) GetThreadTypes() ([]string, error) {
	var types []string
	query := `SELECT DISTINCT thread_type FROM sis_normal_thread ORDER BY thread_type`

	err := r.db.Select(&types, query)
	if err != nil {
		return nil, err
	}

	return types, nil
}

type SectionInfo struct {
	Value     int    `json:"value"`
	Label     string `json:"label"`
	ForumType string `json:"forum_type"`
	Count     int    `json:"count"`
}

func (r *ThreadRepository) GetSectionIds(forumTypeFilter string) ([]SectionInfo, error) {
	// Get the complete structure from model
	forums := structure.NewDefaultStructure()

	// Get counts from database for all sections
	countQuery := `SELECT section_id, COUNT(*) as count FROM sis_normal_thread WHERE section_id IS NOT NULL`
	countArgs := []interface{}{}

	if forumTypeFilter != "" {
		countQuery += ` AND forum_type = ?`
		countArgs = append(countArgs, forumTypeFilter)
	}

	countQuery += ` GROUP BY section_id`

	type countResult struct {
		SectionId int `db:"section_id"`
		Count     int `db:"count"`
	}

	var counts []countResult
	err := r.db.Select(&counts, countQuery, countArgs...)
	if err != nil {
		return nil, err
	}

	// Create count map
	countMap := make(map[int]int)
	for _, c := range counts {
		countMap[c.SectionId] = c.Count
	}

	// Build result with complete structure
	var result []SectionInfo
	for _, forum := range forums {
		// If forum filter is specified, skip other forums
		if forumTypeFilter != "" && forum.ForumType != forumTypeFilter {
			continue
		}

		for _, section := range forum.SectionList {
			count := countMap[section.SectionId] // Will be 0 if not found
			result = append(result, SectionInfo{
				Value:     section.SectionId,
				Label:     section.SectionName,
				ForumType: forum.ForumType,
				Count:     count,
			})
		}
	}

	// Sort by count descending first, then by section ID (value) descending, then by label ascending
	sort.Slice(result, func(i, j int) bool {
		if result[i].Count != result[j].Count {
			return result[i].Count > result[j].Count // Higher counts first
		}
		if result[i].Value != result[j].Value {
			return result[i].Value > result[j].Value // Bigger values second
		}
		return result[i].Label < result[j].Label // Then by label alphabetically
	})

	return result, nil
}

type ForumInfo struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}

func (r *ThreadRepository) GetForumTypes() ([]ForumInfo, error) {
	// Get the complete structure from model
	forums := structure.NewDefaultStructure()

	// Get counts from database for all forum types
	countQuery := `SELECT forum_type, COUNT(*) as count FROM sis_normal_thread WHERE forum_type IS NOT NULL AND forum_type != '' GROUP BY forum_type`

	type countResult struct {
		ForumType string `db:"forum_type"`
		Count     int    `db:"count"`
	}

	var counts []countResult
	err := r.db.Select(&counts, countQuery)
	if err != nil {
		return nil, err
	}

	// Create count map
	countMap := make(map[string]int)
	for _, c := range counts {
		countMap[c.ForumType] = c.Count
	}

	// Build result with complete structure
	var result []ForumInfo
	for _, forum := range forums {
		count := countMap[forum.ForumType] // Will be 0 if not found
		result = append(result, ForumInfo{
			Name:  forum.ForumType,
			Count: count,
		})
	}

	// Sort by count descending first, then by forum name ascending
	sort.Slice(result, func(i, j int) bool {
		if result[i].Count != result[j].Count {
			return result[i].Count > result[j].Count // Higher counts first
		}
		return result[i].Name < result[j].Name // Then by name alphabetically
	})

	return result, nil
}
