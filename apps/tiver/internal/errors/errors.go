package errors

import (
	"fmt"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// ErrorType represents different types of errors in the application
type ErrorType string

const (
	DatabaseError      ErrorType = "DATABASE_ERROR"
	NetworkError       ErrorType = "NETWORK_ERROR"
	ValidationError    ErrorType = "VALIDATION_ERROR"
	ConfigurationError ErrorType = "CONFIGURATION_ERROR"
	ProcessingError    ErrorType = "PROCESSING_ERROR"
)

// AppError represents a structured application error
type AppError struct {
	Type    ErrorType
	Message string
	Cause   error
	Context map[string]interface{}
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// Unwrap returns the underlying error
func (e *AppError) Unwrap() error {
	return e.Cause
}

// NewAppError creates a new application error
func NewAppError(errorType ErrorType, message string, cause error) *AppError {
	return &AppError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
		Context: make(map[string]interface{}),
	}
}

// WithContext adds context information to the error
func (e *AppError) WithContext(key string, value interface{}) *AppError {
	e.Context[key] = value
	return e
}

// LogError logs the error with appropriate level based on error type
func LogError(err error) {
	if appErr, ok := err.(*AppError); ok {
		fields := []zap.Field{
			zap.String("error_type", string(appErr.Type)),
			zap.String("message", appErr.Message),
		}

		// Add context fields
		for k, v := range appErr.Context {
			fields = append(fields, zap.Any(k, v))
		}

		if appErr.Cause != nil {
			fields = append(fields, zap.Error(appErr.Cause))
		}

		// Choose log level based on error type
		switch appErr.Type {
		case ValidationError, ConfigurationError:
			log.Logger.Warn("Application error", fields...)
		case DatabaseError, NetworkError, ProcessingError:
			log.Logger.Error("Application error", fields...)
		default:
			log.Logger.Error("Unknown application error", fields...)
		}
	} else {
		// For regular errors, just log as error
		log.Logger.Error("Unexpected error", zap.Error(err))
	}
}

// HandleFatalError logs and exits for fatal errors
func HandleFatalError(err error, message string) {
	if appErr, ok := err.(*AppError); ok {
		LogError(appErr)
		log.Logger.Fatal(message, zap.String("error_type", string(appErr.Type)))
	} else {
		log.Logger.Fatal(message, zap.Error(err))
	}
}
