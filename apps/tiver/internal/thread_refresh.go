/**
* @desc
* <AUTHOR>
* @version : colly.go, v 0.1 2021/1/22 23:21 pen.wyp Exp $$
 */
package internal

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gocolly/colly/v2"
	_const "github.com/penwyp/hydra/apps/tiver/internal/const"
	"github.com/penwyp/hydra/apps/tiver/internal/db"
	"github.com/penwyp/hydra/apps/tiver/internal/model/param"
	"github.com/penwyp/hydra/apps/tiver/internal/model/sis"
	"github.com/penwyp/hydra/apps/tiver/internal/model/structure"
	"github.com/penwyp/hydra/apps/tiver/internal/progress"
	"github.com/penwyp/hydra/apps/tiver/internal/util"
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

type ThreadRefresher struct {
	imageParam  *param.RefreshParam
	progressMgr *progress.ProgressManager

	urlLock       *sync.Mutex
	exitWaitGroup *sync.WaitGroup

	location      *time.Location        // 新增：时区
	headers       map[string]string     // 新增：请求头
	saveErrorUrl  map[string]int        // 新增：错误链接重试计数
	saveErrorLock *sync.Mutex           // 新增：保护saveErrorUrl
	forumHelper   structure.ForumHelper // 新增：论坛结构助手
}

func NewThreadRefresher(imageParam *param.RefreshParam, location *time.Location, headers map[string]string) *ThreadRefresher {
	if location == nil {
		location = time.UTC
	}
	if headers == nil {
		headers = map[string]string{
			"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36",
		}
	}
	return &ThreadRefresher{
		progressMgr:   progress.NewProgressManager(),
		urlLock:       &sync.Mutex{},
		exitWaitGroup: &sync.WaitGroup{},
		location:      location,
		headers:       headers,
		saveErrorUrl:  make(map[string]int),
		saveErrorLock: &sync.Mutex{},
		imageParam:    imageParam,
		forumHelper:   structure.NewForumHelper(),
	}
}

func (i *ThreadRefresher) Wait() {
	i.exitWaitGroup.Wait()
}

func (i *ThreadRefresher) Done() {
	i.exitWaitGroup.Done()
}

func (i *ThreadRefresher) AddOne() {
	i.exitWaitGroup.Add(1)
}

func (i *ThreadRefresher) refreshNormalThread(normalThreadChan chan sis.NormalThread, urlStatusChan chan sis.ThreadStatus, param *param.RefreshParam) {

	c := GetDefaultCollyCollector(param.Proxy, param.NoProxy)

	// Remove domain restrictions for testing
	c.AllowURLRevisit = true

	i.setLoginValidator(c)
	i.setRequestHeader(c)
	i.setHTMLBodyHandler(c, normalThreadChan, urlStatusChan)
	i.setResponseHandler(c, urlStatusChan)
	i.setErrorHandler(c)

	// Initialize global progress tracking  
	totalPages := len(param.Sections) * 1999 // maxIndex-1 = 1999 per section
	i.progressMgr.AddItem("allPages", totalPages)
	
	// Visit all sections
	for _, section := range param.Sections {
		log.Logger.Info(fmt.Sprintf("Starting to scan section: %d", section))
		i.visitAllThreadListPage(c, section)
	}
}

func (i *ThreadRefresher) setHTMLBodyHandler(c *colly.Collector, normalThreadChan chan sis.NormalThread, urlStatusChan chan sis.ThreadStatus) {
	// First test if we can find any form
	c.OnHTML("form", func(e *colly.HTMLElement) {
		log.Logger.Debug("[THREAD] Found form", zap.String("method", e.Attr("method")), zap.String("action", e.Attr("action")))
	})

	// Test the actual selector
	c.OnHTML("form[method='post'] table tbody tr", func(e *colly.HTMLElement) {
		htmlText := e.Text
		if len(htmlText) > 100 {
			htmlText = htmlText[:100]
		}
		log.Logger.Debug("[THREAD] OnHTML callback triggered", zap.String("html", htmlText))
		var (
			urlName            string
			urlLink            string
			urlType            string
			urlTotalReply      string
			UrlTotalClick      string
			totalReply         int64
			totalClick         int64
			author             string
			firstUploadDateStr string
			thumbsStr          string
			thumbs             int64
			lastPostTimeStr    string
			firstUploadDate    time.Time
			lastPostTime       time.Time
			err                error
		)
		log.Logger.Debug("[THREAD] parse thread html", zap.String("url", e.Request.URL.String()))
		// Try to get thread info with various selectors
		thElement := e.ChildText("th")
		if len(thElement) > 100 {
			thElement = thElement[:100]
		}
		log.Logger.Debug("[THREAD] th content", zap.String("th", thElement))

		for _, class := range []string{"common", "new", "hot", "lock"} {
			urlName = e.ChildText("th[class=" + class + "]>span[id^=thread]>a[href^=thread]")
			urlLink = e.ChildAttr("th[class="+class+"]>span[id^=thread]>a[href^=thread]", "href")
			urlType = e.ChildText("th[class=" + class + "] em a[href^=forumdisplay]")

			// Also try without span
			if urlName == "" {
				urlName = e.ChildText("th[class=" + class + "] a[href^=thread]")
				urlLink = e.ChildAttr("th[class="+class+"] a[href^=thread]", "href")
			}

			if urlName != "" && urlLink != "" {
				log.Logger.Debug("[THREAD] found thread", zap.String("class", class), zap.String("name", urlName), zap.String("type", urlType), zap.String("link", urlLink))
				break
			}
		}

		if urlType == "" {
			log.Logger.Debug("[THREAD] no thread type", zap.String("name", urlName), zap.String("link", urlLink))
			return
		}

		if urlType == "版务" {
			log.Logger.Debug("[THREAD] Skipping admin thread", zap.String("name", urlName))
			return
		}
		// url start with thread-
		if strings.Index(urlLink, "thread-") != 0 {
			log.Logger.Debug("[THREAD] Skipping non-thread link", zap.String("link", urlLink))
			return
		}

		log.Logger.Debug("[THREAD] Processing thread", zap.String("name", urlName), zap.String("type", urlType))

		author = strings.TrimSpace(e.ChildText("td[class=author]>cite>a"))
		firstUploadDateStr = strings.TrimSpace(e.ChildText("td[class=author]>em"))
		lastPostTimeStr = strings.TrimSpace(e.ChildText("td[class=lastpost]>em>a"))
		thumbsStr = strings.ReplaceAll(strings.TrimSpace(e.ChildText("td[class=author]>cite")), author, "")
		thumbsStr = strings.ReplaceAll(thumbsStr, "\"", "")
		urlTotalReply = strings.TrimSpace(e.ChildText("td[class=nums]>strong"))
		UrlTotalClick = strings.TrimSpace(e.ChildText("td[class=nums]>em"))

		firstUploadDate, err = util.ParseTime(firstUploadDateStr, i.location)
		if err != nil {
			log.Logger.Error("[THREAD] parse firstUploadDate failed", zap.String("dateStr", firstUploadDateStr), zap.Error(err))
			return
		}
		lastPostTime, err = util.ParseTime(lastPostTimeStr, i.location)
		if err != nil {
			log.Logger.Error("[THREAD] parse lastPostTime failed", zap.String("dateStr", lastPostTimeStr), zap.Error(err))
			return
		}
		totalReply, _ = strconv.ParseInt(urlTotalReply, 10, 64)
		totalClick, _ = strconv.ParseInt(UrlTotalClick, 10, 64)
		thumbs, _ = strconv.ParseInt(thumbsStr, 10, 64)

		fullLink := _const.SISDomainHTTPS + "forum/" + urlLink
		digest := util.MD5(fullLink)

		// Extract sectionId from the current URL
		// URL format is like: forum/forum-60-1.html
		url := e.Request.URL.String()
		sectionId := i.extractSectionIdFromURL(url)

		// Get section details for ForumType and SectionName
		var forumType, sectionName string
		if sectionDetail, ok := i.forumHelper.GetSectionDetail(sectionId); ok {
			forumType = sectionDetail.ForumType
			sectionName = sectionDetail.SectionName
		}

		threadItem := sis.NormalThread{
			ThreadType:      urlType,
			ForumType:       forumType,
			SectionId:       sectionId,
			SectionName:     sectionName,
			Name:            urlName,
			Link:            urlLink,
			FullLink:        fullLink,
			UrlDigest:       digest,
			TotalReply:      totalReply,
			TotalClick:      totalClick,
			Author:          author,
			Thumbs:          thumbs,
			FirstUploadTime: firstUploadDate,
			LastPostTime:    lastPostTime,
		}
		statusItem := sis.ThreadStatus{
			UrlDigest:   digest,
			ImageStatus: _const.ThreadStatusWaiting,
			PdfStatus:   _const.ThreadStatusWaiting,
		}

		i.progressMgr.MarkSuccess("fetch") // 统计抓取成功数
		log.LogThreadEvent("fetch", threadItem.GetValidName(), "parsed", nil)
		log.Logger.Debug("[THREAD] Sending thread to channel", zap.String("name", threadItem.GetValidName()))
		normalThreadChan <- threadItem
		urlStatusChan <- statusItem
	})
}

func (i *ThreadRefresher) setResponseHandler(c *colly.Collector, urlStatusChan chan sis.ThreadStatus) {
	c.OnResponse(func(r *colly.Response) {
		log.Logger.Debug("[THREAD] Got response",
			zap.String("url", r.Request.URL.String()),
			zap.Int("status", r.StatusCode),
			zap.Int("bodyLen", len(r.Body)),
			zap.String("content-encoding", r.Headers.Get("Content-Encoding")))

		// Check if response contains login page
		bodyStr := string(r.Body)
		if strings.Contains(bodyStr, "没有登录") || strings.Contains(bodyStr, "登录") {
			log.Logger.Warn("[THREAD] Response indicates not logged in")
		}

		// Save first response for debugging
		if strings.Contains(r.Request.URL.String(), "forum-60-1") {
			os.WriteFile("/tmp/sis_response.html", r.Body, 0644)
			log.Logger.Debug("[THREAD] Saved response to /tmp/sis_response.html")
		}

		urlStatusChan <- sis.ThreadStatus{
			UrlDigest:   util.MD5(r.Request.URL.String()),
			ImageStatus: _const.ThreadStatusWaiting,
			PdfStatus:   _const.ThreadStatusWaiting,
		}
	})
}

func (i *ThreadRefresher) setErrorHandler(c *colly.Collector) {
	c.OnError(func(rs *colly.Response, err error) {
		link := rs.Request.URL.String()
		i.urlLock.Lock()
		i.saveErrorLock.Lock()
		if _, ok := i.saveErrorUrl[link]; !ok {
			i.saveErrorUrl[link]++
			log.LogThreadEvent("fetch", link, fmt.Sprintf("retry %d", i.saveErrorUrl[link]), err)
			rs.Request.Retry()
		} else if i.saveErrorUrl[link] < 5 {
			i.saveErrorUrl[link]++
			log.LogThreadEvent("fetch", link, fmt.Sprintf("retry %d", i.saveErrorUrl[link]), err)
			rs.Request.Retry()
		} else {
			log.LogThreadEvent("fetch", link, "failed", err)
		}
		i.saveErrorLock.Unlock()
		i.urlLock.Unlock()
		return
	})
}

func (i *ThreadRefresher) setRequestHeader(c *colly.Collector) {
	c.OnRequest(func(r *colly.Request) {
		for k, v := range i.headers {
			r.Headers.Set(k, v)
		}
	})
}

func (i *ThreadRefresher) setLoginValidator(c *colly.Collector) {
	c.OnHTML("html", func(e *colly.HTMLElement) {
		if strings.Contains(string(e.Response.Body), "没有登录") {
			log.Logger.Panic("[THREAD] Cookie登陆失败")
		}
	})
}

func (i *ThreadRefresher) visitAllThreadListPage(c *colly.Collector, section _const.SectionId) {
	defer i.Done()

	maxIndex := 2000
	
	// Note: Only track global progress to avoid double-counting in globalTotal
	
	log.Logger.Info(fmt.Sprintf("Starting section %d, expecting to visit %d pages", section, maxIndex-1))
	
	urlTemplate := _const.SISDomainHTTPS + "forum/forum-%d-%d.html"
	for j := 1; j < maxIndex; j++ {
		url := fmt.Sprintf(urlTemplate, section, j)
		log.Logger.Info("Visiting URL", zap.String("url", url))
		if err := c.Visit(url); err != nil {
			log.Logger.Error("Failed to visit URL", zap.String("url", url), zap.Error(err))
			i.progressMgr.MarkFailed("allPages")
		} else {
			i.progressMgr.MarkSuccess("allPages")
		}
		c.Wait()
		time.Sleep(time.Millisecond * 200)
	}
	
	log.Logger.Info(fmt.Sprintf("Completed section %d", section))
}

func (i *ThreadRefresher) FetchThreadStatusToDB(ctx context.Context) {

	normalThreadChan := make(chan sis.NormalThread, 10000)
	urlStatusChan := make(chan sis.ThreadStatus, 10000)

	imageParam := i.imageParam

	// Start crawler in a goroutine that will close channels when done
	go func() {
		i.refreshNormalThread(normalThreadChan, urlStatusChan, imageParam)
		close(normalThreadChan)
		close(urlStatusChan)
	}()

	done := make(chan struct{})
	go i.progressMgr.DisplayProgress(done)

	// Wait group for data processing goroutines
	var wg sync.WaitGroup

	// 将所有forum url写入到数据库，或更新动态字段（例如点击数/点赞数/评论数）
	for j := 0; j < int(imageParam.ThreadNum); j++ {
		wg.Add(2)
		go func() {
			defer wg.Done()
			i.updateThreadInfo(normalThreadChan)
		}()
		go func() {
			defer wg.Done()
			i.updateThreadStatus(urlStatusChan)
		}()
	}

	// Wait for all data processing to complete
	log.Logger.Debug("[THREAD] Waiting for all goroutines to complete")
	wg.Wait()
	log.Logger.Debug("[THREAD] All goroutines completed")
	close(done)
}

func (i *ThreadRefresher) updateThreadStatus(urlStatusChan chan sis.ThreadStatus) {
	for item := range urlStatusChan {
		lastInsertId, err := db.UpsertThreadStatus(context.Background(), item)
		if err != nil {
			log.LogThreadEvent("save", item.UrlDigest, "UpsertThreadStatus failed", err)
		} else {
			log.LogThreadEvent("save", item.UrlDigest, fmt.Sprintf("UpsertThreadStatus success, lastInsertId=%d", lastInsertId), nil)
		}
	}
}

func (i *ThreadRefresher) updateThreadInfo(normalThreadChan chan sis.NormalThread) {
	batch := make([]sis.NormalThread, 0, 100) // 中文注释：每批100条
	log.Logger.Debug("[THREAD] updateThreadInfo goroutine started")
	for item := range normalThreadChan {
		log.Logger.Debug("[THREAD] Received thread from channel", zap.String("name", item.GetValidName()))
		if item.GetValidName() == "" || item.ThreadType == "" || item.Link == "" {
			log.Logger.Warn("[THREAD] Skipping invalid thread", zap.String("name", item.GetValidName()), zap.String("type", item.ThreadType), zap.String("link", item.Link))
			continue
		}

		// 中文注释：特殊类别的帖子不处理，不下载
		item.IsForbidden = item.TitleIsForbidden()

		batch = append(batch, item)
		if len(batch) >= 100 {
			i.saveThreadBatch(batch)
			batch = batch[:0]
		}
	}
	// 中文注释：处理剩余未满一批的数据
	if len(batch) > 0 {
		log.Logger.Debug("[THREAD] Saving final batch", zap.Int("count", len(batch)))
		i.saveThreadBatch(batch)
	}
	log.Logger.Debug("[THREAD] updateThreadInfo completed")
}

// saveThreadBatch 封装批量写入并日志、异常处理
func (i *ThreadRefresher) saveThreadBatch(batch []sis.NormalThread) {
	if len(batch) == 0 {
		return
	}
	count, err := db.UpsertNormalThreadBatch(context.Background(), batch)
	if err != nil {
		log.LogThreadEvent("save", "", "UpsertNormalThreadBatch failed", err)
	} else {
		log.LogThreadEvent("save", "", fmt.Sprintf("UpsertNormalThreadBatch success, count=%d", count), nil)
	}
	i.progressMgr.MarkSuccess("refresh") // 统计刷新成功数
}

// extractSectionIdFromURL extracts sectionId from forum URL
// URL format: https://domain/forum/forum-60-1.html
func (i *ThreadRefresher) extractSectionIdFromURL(url string) int {
	re := regexp.MustCompile(`forum/forum-(\d+)-\d+\.html`)
	matches := re.FindStringSubmatch(url)
	if len(matches) >= 2 {
		if sectionId, err := strconv.Atoi(matches[1]); err == nil {
			return sectionId
		}
	}
	log.Logger.Warn("Could not extract sectionId from URL", zap.String("url", url))
	return 0
}
