package progress

import (
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
	"sync"
	"time"
)

type ImageProgress struct {
	Type string
	Name string
	lock sync.Locker

	totalFetchedThreadNum  int
	totalDownloadThreadNum int
	totalDownloadImageNum  int

	ticker *time.Ticker
}

func NewImageProgress() *ImageProgress {
	return &ImageProgress{
		lock:   &sync.Mutex{},
		ticker: time.NewTicker(time.Second * 5),
	}

}
func (i *ImageProgress) IncreaseTotalThreadNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalFetchedThreadNum++
}
func (i *ImageProgress) IncreaseTotalFetchedThreadNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalFetchedThreadNum++
}
func (i *ImageProgress) IncreaseTotalDownloadImageNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalDownloadImageNum++
}

func (i *ImageProgress) Close() {
	i.ticker.Stop()
}

func (i *ImageProgress) DisplayProgress() {
	for {
		select {
		case <-i.ticker.C:
			log.Logger.Info("[IMAGE] Begin Download", zap.String("Type", i.Type), zap.String("Name", i.Name), zap.Int("Total Thread Num", i.totalFetchedThreadNum), zap.Int("Total Download Thread Num", i.totalDownloadThreadNum), zap.Int("Total Download Image Num", i.totalDownloadImageNum))
			i.ticker.Reset(time.Second * 10)
		}
	}
}
