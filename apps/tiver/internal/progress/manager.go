package progress

import (
	"sync"
	"time"

	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
)

// ItemProgress 线程/图片级进度结构体
type ItemProgress struct {
	Total   int
	Success int
	Failed  int
}

// ProgressManager 统一进度管理器
type ProgressManager struct {
	globalTotal   int
	globalSuccess int
	globalFailed  int

	itemProgress map[string]*ItemProgress // 线程/任务名 -> 进度
	lock         sync.Mutex

	ticker *time.Ticker
}

// NewProgressManager 构造函数
func NewProgressManager() *ProgressManager {
	return &ProgressManager{
		itemProgress: make(map[string]*ItemProgress),
		ticker:       time.NewTicker(2 * time.Second),
	}
}

// AddItem 新增一个任务/线程
func (pm *ProgressManager) AddItem(key string, total int) {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	pm.itemProgress[key] = &ItemProgress{Total: total}
	pm.globalTotal += total
}

// MarkSuccess 标记成功
func (pm *ProgressManager) MarkSuccess(key string) {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	if p, ok := pm.itemProgress[key]; ok {
		p.Success++
		pm.globalSuccess++
	}
}

// MarkFailed 标记失败
func (pm *ProgressManager) MarkFailed(key string) {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	if p, ok := pm.itemProgress[key]; ok {
		p.Failed++
		pm.globalFailed++
	}
}

// DisplayProgress 定时输出进度日志
func (pm *ProgressManager) DisplayProgress(done <-chan struct{}) {
	for {
		select {
		case <-pm.ticker.C:
			pm.lock.Lock()
			log.Logger.Info("[PROGRESS] global", zap.Int("total", pm.globalTotal), zap.Int("success", pm.globalSuccess), zap.Int("failed", pm.globalFailed))
			for k, v := range pm.itemProgress {
				log.Logger.Info("[PROGRESS] item", zap.String("key", k), zap.Int("total", v.Total), zap.Int("success", v.Success), zap.Int("failed", v.Failed))
			}
			pm.lock.Unlock()
		case <-done:
			pm.lock.Lock()
			log.Logger.Info("[PROGRESS] global final", zap.Int("total", pm.globalTotal), zap.Int("success", pm.globalSuccess), zap.Int("failed", pm.globalFailed))
			for k, v := range pm.itemProgress {
				log.Logger.Info("[PROGRESS] item final", zap.String("key", k), zap.Int("total", v.Total), zap.Int("success", v.Success), zap.Int("failed", v.Failed))
			}
			pm.lock.Unlock()
			return
		}
	}
}
