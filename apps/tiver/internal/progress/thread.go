package progress

import (
	"github.com/penwyp/hydra/shared/log"
	"go.uber.org/zap"
	"sync"
	"time"
)

type ThreadProgress struct {
	totalVisitPageNum      int
	totalThreadFetchNum    int
	totalThreadRefreshNum  int
	totalThreadDownloadNum int

	startTime time.Time
	lock      sync.Locker

	ticker *time.Ticker
}

func NewThreadProgress() *ThreadProgress {
	return &ThreadProgress{
		ticker:    time.NewTicker(time.Second * 5),
		startTime: time.Now(),
		lock:      &sync.Mutex{},
	}
}

func (i *ThreadProgress) IncreaseDownloadNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalThreadDownloadNum++
}

func (i *ThreadProgress) IncreaseThreadFetchNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalThreadFetchNum++
}

func (i *ThreadProgress) IncreaseVisitPageNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalVisitPageNum++
}

func (i *ThreadProgress) IncreaseRefreshNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.totalThreadRefreshNum++
}

func (i *ThreadProgress) Close() {
	i.ticker.Stop()
}

func (i *ThreadProgress) DisplayProgress(section Section) {
	for {
		select {
		case <-i.ticker.C:
			log.Logger.Info("progress", zap.Int("VisitPageNum", i.totalVisitPageNum), zap.Int("FetchNum", i.totalThreadFetchNum), zap.Int("RefreshNum", i.totalThreadRefreshNum), zap.Int("TimeCost", int(time.Since(i.startTime).Seconds())))
			i.ticker.Reset(time.Second * 10)
		}
	}
}
