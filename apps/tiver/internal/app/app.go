package app

import (
	"github.com/penwyp/hydra/apps/tiver/internal/config"
	"github.com/penwyp/hydra/apps/tiver/internal/db"
	"github.com/penwyp/hydra/apps/tiver/internal/errors"
	"github.com/penwyp/hydra/shared/log"
)

// App holds the application context and shared resources
type App struct {
	Config *config.Config
}

// Initialize sets up the application with logging and database
func Initialize() (*App, error) {
	cfg := config.LoadConfig()

	// Initialize logger
	// Skip if already initialized by command line flags
	if log.Logger == nil {
		log.InitLogger(log.LoggerOption{LogLevel: cfg.App.LogLevel})
	}

	// Initialize database
	if err := db.InitDB(); err != nil {
		appErr := errors.NewAppError(errors.DatabaseError, "database initialization failed", err)
		errors.LogError(appErr)
		return nil, appErr
	}

	return &App{
		Config: cfg,
	}, nil
}

// MustInitialize is like Initialize but panics on error
func MustInitialize() *App {
	app, err := Initialize()
	if err != nil {
		errors.HandleFatalError(err, "application initialization failed")
	}
	return app
}
