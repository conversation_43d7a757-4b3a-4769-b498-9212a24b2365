package server

import (
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/penwyp/hydra/apps/tiver/internal/repository"
)

const (
	CacheKeyThreadTypes  = "thread_types"
	CacheKeyForumTypes   = "forum_types"
	CacheKeySectionTypes = "section_types"
	CacheExpiration      = 5 * time.Minute
	CacheCleanupInterval = 10 * time.Minute
)

type CacheManager struct {
	cache *cache.Cache
	repo  *repository.ThreadRepository
}

func NewCacheManager(repo *repository.ThreadRepository) *CacheManager {
	return &CacheManager{
		cache: cache.New(CacheExpiration, CacheCleanupInterval),
		repo:  repo,
	}
}

func (cm *CacheManager) WarmupCache() error {
	// Warmup thread types
	threadTypes, err := cm.repo.GetThreadTypes()
	if err == nil {
		cm.SetThreadTypes(threadTypes)
	}

	// Warmup forum types
	forumTypes, err := cm.repo.GetForumTypes()
	if err == nil {
		cm.SetForumTypes(forumTypes)
	}

	// Warmup section types
	sectionTypes, err := cm.repo.GetSectionIds("")
	if err == nil {
		cm.SetSectionTypes(sectionTypes)
	}

	return nil
}

func (cm *CacheManager) GetThreadTypes() ([]string, bool) {
	if data, found := cm.cache.Get(CacheKeyThreadTypes); found {
		if threadTypes, ok := data.([]string); ok {
			return threadTypes, true
		}
	}
	return nil, false
}

func (cm *CacheManager) SetThreadTypes(threadTypes []string) {
	cm.cache.Set(CacheKeyThreadTypes, threadTypes, cache.DefaultExpiration)
}

func (cm *CacheManager) GetForumTypes() ([]repository.ForumInfo, bool) {
	if data, found := cm.cache.Get(CacheKeyForumTypes); found {
		if forumTypes, ok := data.([]repository.ForumInfo); ok {
			return forumTypes, true
		}
	}
	return nil, false
}

func (cm *CacheManager) SetForumTypes(forumTypes []repository.ForumInfo) {
	cm.cache.Set(CacheKeyForumTypes, forumTypes, cache.DefaultExpiration)
}

func (cm *CacheManager) GetSectionTypes() ([]repository.SectionInfo, bool) {
	if data, found := cm.cache.Get(CacheKeySectionTypes); found {
		if sectionTypes, ok := data.([]repository.SectionInfo); ok {
			return sectionTypes, true
		}
	}
	return nil, false
}

func (cm *CacheManager) SetSectionTypes(sectionTypes []repository.SectionInfo) {
	cm.cache.Set(CacheKeySectionTypes, sectionTypes, cache.DefaultExpiration)
}

func (cm *CacheManager) GetSectionTypesByForum(forumTypeFilter string) ([]repository.SectionInfo, bool) {
	cacheKey := CacheKeySectionTypes
	if forumTypeFilter != "" {
		cacheKey = CacheKeySectionTypes + "_" + forumTypeFilter
	}
	
	if data, found := cm.cache.Get(cacheKey); found {
		if sectionTypes, ok := data.([]repository.SectionInfo); ok {
			return sectionTypes, true
		}
	}
	return nil, false
}

func (cm *CacheManager) SetSectionTypesByForum(forumTypeFilter string, sectionTypes []repository.SectionInfo) {
	cacheKey := CacheKeySectionTypes
	if forumTypeFilter != "" {
		cacheKey = CacheKeySectionTypes + "_" + forumTypeFilter
	}
	cm.cache.Set(cacheKey, sectionTypes, cache.DefaultExpiration)
}

func (cm *CacheManager) InvalidateAll() {
	cm.cache.Flush()
}