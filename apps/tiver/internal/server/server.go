package server

import (
	"fmt"
	"net"
	"net/http"
	"path/filepath"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/penwyp/hydra/apps/tiver/internal/repository"
)

type Server struct {
	port         int
	webRoot      string
	repo         *repository.ThreadRepository
	router       *gin.Engine
	cacheManager *CacheManager
}

func NewServer(port int, webRoot string) *Server {
	repo := repository.NewThreadRepository()
	cacheManager := NewCacheManager(repo)

	s := &Server{
		port:         port,
		webRoot:      webRoot,
		repo:         repo,
		router:       gin.Default(),
		cacheManager: cacheManager,
	}

	s.setupRoutes()
	return s
}

func (s *Server) setupRoutes() {
	// API routes
	api := s.router.Group("/api")
	{
		api.GET("/threads", s.handleGetThreads)
		api.GET("/thread-types", s.handleGetThreadTypes)
		api.GET("/forum-types", s.handleGetForumTypes)
		api.GET("/section-types", s.handleGetSectionIds)
	}

	// Static files
	s.router.StaticFile("/", filepath.Join(s.webRoot, "index.html"))
	s.router.Static("/css", filepath.Join(s.webRoot, "css"))
	s.router.Static("/js", filepath.Join(s.webRoot, "js"))

	// NoRoute fallback for client-side routing
	s.router.NoRoute(func(c *gin.Context) {
		c.File(filepath.Join(s.webRoot, "index.html"))
	})
}

func (s *Server) Run() error {
	// Start cache warmup in background
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := s.cacheManager.WarmupCache(); err != nil {
			fmt.Printf("Warning: Failed to warmup cache: %v\n", err)
		} else {
			fmt.Println("Cache warmed up successfully")
		}
	}()

	// Wait for cache warmup to complete
	wg.Wait()

	// Log server addresses
	s.logServerAddresses()

	addr := fmt.Sprintf(":%d", s.port)
	return s.router.Run(addr)
}

func (s *Server) logServerAddresses() {
	fmt.Printf("\n")
	fmt.Printf("Server listening on:\n")
	fmt.Printf("  - http://127.0.0.1:%d\n", s.port)
	fmt.Printf("  - http://localhost:%d\n", s.port)
	
	// Get local IP addresses
	addrs, err := net.InterfaceAddrs()
	if err == nil {
		for _, addr := range addrs {
			if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				if ipnet.IP.To4() != nil {
					fmt.Printf("  - http://%s:%d\n", ipnet.IP.String(), s.port)
				}
			}
		}
	}
	fmt.Printf("\n")
}

func (s *Server) handleGetThreads(c *gin.Context) {
	// Parse query parameters
	params := repository.ThreadQueryParams{
		ThreadType:           c.Query("thread_type"),
		ForumType:            c.Query("forum_type"),
		Name:                 c.Query("name"),
		OrderBy:              c.DefaultQuery("order_by", "update_time"),
		Order:                c.DefaultQuery("order", "desc"),
		Page:                 1,
		PageSize:             50,
		FirstUploadThreshold: c.Query("first_upload_after"),
	}

	// Parse section_id if provided
	if sectionIdStr := c.Query("section_id"); sectionIdStr != "" {
		var sectionId int
		if _, err := fmt.Sscanf(sectionIdStr, "%d", &sectionId); err == nil {
			params.SectionId = &sectionId
		}
	}

	// Parse pagination
	var page, pageSize int
	fmt.Sscanf(c.DefaultQuery("page", "1"), "%d", &page)
	fmt.Sscanf(c.DefaultQuery("page_size", "50"), "%d", &pageSize)

	if page > 0 {
		params.Page = page
	}
	if pageSize > 0 && pageSize <= 100 {
		params.PageSize = pageSize
	}

	threads, total, err := s.repo.GetThreads(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"threads":   threads,
		"total":     total,
		"page":      params.Page,
		"page_size": params.PageSize,
	})
}

func (s *Server) handleGetThreadTypes(c *gin.Context) {
	// Try to get from cache first
	if types, found := s.cacheManager.GetThreadTypes(); found {
		c.JSON(http.StatusOK, gin.H{
			"thread_types": types,
		})
		return
	}

	// Cache miss, get from database
	types, err := s.repo.GetThreadTypes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Update cache
	s.cacheManager.SetThreadTypes(types)

	c.JSON(http.StatusOK, gin.H{
		"thread_types": types,
	})
}

func (s *Server) handleGetForumTypes(c *gin.Context) {
	// Try to get from cache first
	if types, found := s.cacheManager.GetForumTypes(); found {
		c.JSON(http.StatusOK, gin.H{
			"forum_types": types,
		})
		return
	}

	// Cache miss, get from database
	types, err := s.repo.GetForumTypes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Update cache
	s.cacheManager.SetForumTypes(types)

	c.JSON(http.StatusOK, gin.H{
		"forum_types": types,
	})
}

func (s *Server) handleGetSectionIds(c *gin.Context) {
	// Get optional forum type filter
	forumTypeFilter := c.Query("forum_type")

	// Try to get from cache first
	if types, found := s.cacheManager.GetSectionTypesByForum(forumTypeFilter); found {
		c.JSON(http.StatusOK, gin.H{
			"section_types": types,
		})
		return
	}

	// Cache miss, get from database
	types, err := s.repo.GetSectionIds(forumTypeFilter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Update cache
	s.cacheManager.SetSectionTypesByForum(forumTypeFilter, types)

	c.JSON(http.StatusOK, gin.H{
		"section_types": types,
	})
}
