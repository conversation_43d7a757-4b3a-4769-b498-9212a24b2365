/**
* @desc
* <AUTHOR>
* @version : image.go, v 0.1 2021/2/8 23:27 pen.wyp Exp $$
 */
package param

import (
	"fmt"
	"time"
)

type DownloadParam struct {
	ImageThreadParseGoroutineNum    uint
	ThreadImageDownloadGoroutineNum uint

	DownloadOrderType   uint
	DownloadThreadTypes []string
	DownloadWatermark   uint
	DataDir             string
	BeginTime           time.Time
	ProxyUrl            string
	ImageDomainFilters  []string

	imageDomainFilterMap map[string]bool
}

// SetDefault 设置DownloadParam的默认值
func (p *DownloadParam) SetDefault() {
	if p.ImageThreadParseGoroutineNum == 0 {
		p.ImageThreadParseGoroutineNum = 5
	}
	if p.ThreadImageDownloadGoroutineNum == 0 {
		p.ThreadImageDownloadGoroutineNum = 5
	}
	if p.DownloadOrderType == 0 {
		p.DownloadOrderType = 1
	}
	if p.DownloadThreadTypes == nil || len(p.DownloadThreadTypes) == 0 {
		p.DownloadThreadTypes = []string{"中文黑白", "中文彩漫"}
	}
	if p.DownloadWatermark == 0 {
		p.DownloadWatermark = 10
	}
	if p.DataDir == "" {
		p.DataDir = "data"
	}
	if p.ProxyUrl == "" {
		p.ProxyUrl = "http://127.0.0.1:6152"
	}
	if p.ImageDomainFilters == nil || len(p.ImageDomainFilters) == 0 {
		p.ImageDomainFilters = []string{"www.kanjiantu.com", "kanjiantu.com", "s7tu.com", "www.skeimg.com", "sxeimg.com", "img301.com", "luoimg.com", "pic.jitudisk.com"}
	}
}

// Validate 校验DownloadParam参数合法性
func (p *DownloadParam) Validate() error {
	if p.ImageThreadParseGoroutineNum == 0 || p.ThreadImageDownloadGoroutineNum == 0 {
		return fmt.Errorf("goroutine num must > 0")
	}
	if p.DataDir == "" {
		return fmt.Errorf("dataDir is empty")
	}
	if len(p.DownloadThreadTypes) == 0 {
		return fmt.Errorf("DownloadThreadTypes is empty")
	}
	return nil
}
