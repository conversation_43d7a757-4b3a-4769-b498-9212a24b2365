package param

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRefreshParam_SetDefault(t *testing.T) {
	var p RefreshParam
	p.SetDefault()
	assert.Equal(t, uint(5), p.ThreadNum)
	assert.Equal(t, []string{"中文黑白", "中文彩漫"}, p.ThreadTypes)
	assert.Equal(t, "http://127.0.0.1:6152", p.ProxyUrl)
}

func TestRefreshParam_Validate(t *testing.T) {
	p := RefreshParam{}
	p.SetDefault()
	err := p.Validate()
	assert.NoError(t, err)

	p.ThreadNum = 0
	err = p.Validate()
	assert.Error(t, err)

	p.ThreadNum = 1
	p.ThreadTypes = nil
	err = p.Validate()
	assert.Error(t, err)
}
