/**
* @desc
* <AUTHOR>
* @version : image.go, v 0.1 2021/2/8 23:27 pen.wyp Exp $$
 */
package param

import (
	"fmt"
	_const "github.com/penwyp/hydra/apps/tiver/internal/const"
)

type RefreshParam struct {
	Sections  []_const.SectionId
	ThreadNum uint
	Proxy     string
	NoProxy   bool
}

// SetDefault 设置RefreshParam的默认值
func (p *RefreshParam) SetDefault() {
	if p.ThreadNum == 0 {
		p.ThreadNum = 5
	}
	if p.Proxy == "" {
		p.Proxy = "http://127.0.0.1:6152"
	}
}

// Validate 校验RefreshParam参数合法性
func (p *RefreshParam) Validate() error {
	if p.ThreadNum <= 0 {
		return fmt.Errorf("ThreadNum must be positive")
	}
	if len(p.Sections) == 0 {
		return fmt.Errorf("Sections must not be empty")
	}
	return nil
}
