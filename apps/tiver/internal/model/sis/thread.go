/**
* @desc
* <AUTHOR>
* @version : url.go, v 0.1 2021/1/22 23:25 pen.wyp Exp $$
 */
package sis

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"path"
	"strings"
	"time"
)

type NormalThread struct {
	ID              int64     `db:"id" json:"id" form:"id"`
	ThreadType      string    `db:"thread_type" json:"thread_type" form:"thread_type"`
	ForumType       string    `db:"forum_type" json:"forum_type" form:"forum_type"`
	SectionId       int       `db:"section_id" json:"section_id" form:"section_id"`
	SectionName     string    `db:"section_name" json:"section_name" form:"section_name"`
	Name            string    `db:"name" json:"name" form:"name"`
	Link            string    `db:"link" json:"link" form:"link"`
	FullLink        string    `db:"full_link" json:"full_link" form:"full_link"`
	UrlDigest       string    `db:"url_digest" json:"url_digest" form:"url_digest"`
	TotalReply      int64     `db:"total_reply" json:"total_reply" form:"total_reply"`
	TotalClick      int64     `db:"total_click" json:"total_click" form:"total_click"`
	Author          string    `db:"author" json:"author" form:"author"`
	Thumbs          int64     `db:"thumbs" json:"thumbs" form:"thumbs"`
	IsForbidden     bool      `db:"is_forbidden" json:"is_forbidden" form:"is_forbidden"`
	FirstUploadTime time.Time `db:"first_upload_time" json:"first_upload_time" form:"first_upload_time"`
	LastPostTime    time.Time `db:"last_post_time" json:"last_post_time" form:"last_post_time"`
	CreateTime      time.Time `db:"create_time" json:"create_time" form:"create_time"`
	UpdateTime      time.Time `db:"update_time" json:"update_time" form:"update_time"`
}

type ThreadStatus struct {
	ID          int64     `db:"id" json:"id" form:"id"`
	UrlDigest   string    `db:"url_digest" json:"url_digest" form:"url_digest"`
	ImageStatus string    `db:"image_status" json:"image_status" form:"image_status"`
	PdfStatus   string    `db:"pdf_status" json:"pdf_status" form:"pdf_status"`
	CreateTime  time.Time `db:"create_time" json:"create_time" form:"create_time"`
	UpdateTIme  time.Time `db:"update_time" json:"update_time" form:"update_time"`
}

func (i NormalThread) GetValidName() string {
	return strings.ReplaceAll(i.Name, "/", "_")
}

func (i NormalThread) GetUrlDigest() string {
	if i.UrlDigest != "" {
		return i.UrlDigest
	}
	id := md5.New()
	_, _ = io.WriteString(id, i.FullLink)
	h := fmt.Sprintf("%x", id.Sum(nil))
	return strings.ToUpper(h)
}

func (i NormalThread) String() string {
	return fmt.Sprintf("<id:%d,threadType:%s,name:%s,link:%s>",
		i.ID, i.ThreadType, i.GetValidName(), i.Link)
}

func (i NormalThread) TitleIsForbidden() bool {
	return strings.Contains(i.GetValidName(), "母") || strings.Contains(i.GetValidName(), "妈")
}

func (i NormalThread) GetDataFullPath(defaultFolder string) string {
	return path.Join(defaultFolder, i.ThreadType, i.GetValidName())
}

func (i NormalThread) GetDataTypePath(defaultFolder string) string {
	return path.Join(defaultFolder, i.ThreadType)
}

func (i NormalThread) GetTempDataTypePath(defaultFolder string) string {
	return path.Join(defaultFolder, "temp", i.ThreadType)
}

func (i NormalThread) GetTempDataFullPath(defaultFolder string) string {
	return path.Join(defaultFolder, "temp", i.ThreadType, i.GetValidName())
}

// MarshalJSON implements custom JSON marshaling to convert time fields to Unix timestamps (seconds)
func (n NormalThread) MarshalJSON() ([]byte, error) {
	type Alias NormalThread
	return json.Marshal(&struct {
		FirstUploadTime int64 `json:"first_upload_time"`
		LastPostTime    int64 `json:"last_post_time"`
		CreateTime      int64 `json:"create_time"`
		UpdateTime      int64 `json:"update_time"`
		*Alias
	}{
		FirstUploadTime: n.FirstUploadTime.Unix(),
		LastPostTime:    n.LastPostTime.Unix(),
		CreateTime:      n.CreateTime.Unix(),
		UpdateTime:      n.UpdateTime.Unix(),
		Alias:           (*Alias)(&n),
	})
}
