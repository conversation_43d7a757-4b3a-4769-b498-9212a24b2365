package structure

type Section struct {
	SectionId   int    `json:"sectionId"`
	SectionName string `json:"sectionName"`
}

type Forum struct {
	ForumType   string    `json:"forumType"`
	SectionList []Section `json:"sectionList"`
}

type ForumHelper struct {
	forums []Forum

	sectionMap     map[int]string
	sectionNameMap map[string]int

	sectionDetailMap map[int]SectionDetail
}

type SectionDetail struct {
	ForumType   string `json:"forumType"`
	SectionId   int    `json:"sectionId"`
	SectionName string `json:"sectionName"`
}

func NewForumHelper() ForumHelper {
	forums := NewDefaultStructure()

	sectionMap := make(map[int]string)
	sectionNameMap := make(map[string]int)
	sectionDetailMap := make(map[int]SectionDetail)
	for _, forum := range forums {
		for _, section := range forum.SectionList {
			sectionMap[section.SectionId] = section.SectionName
			sectionNameMap[section.SectionName] = section.SectionId
			sectionDetailMap[section.SectionId] = SectionDetail{
				ForumType:   forum.ForumType,
				SectionId:   section.SectionId,
				SectionName: section.SectionName,
			}
		}
	}

	return ForumHelper{
		forums:           forums,
		sectionMap:       sectionMap,
		sectionNameMap:   sectionNameMap,
		sectionDetailMap: sectionDetailMap,
	}
}

func (i ForumHelper) HasSectionId(sectionId int) bool {
	_, ok := i.sectionMap[sectionId]
	return ok
}

func (i ForumHelper) HasSectionName(sectionName string) bool {
	_, ok := i.sectionNameMap[sectionName]
	return ok
}

func (i ForumHelper) GetSectionDetail(sectionId int) (SectionDetail, bool) {
	section, ok := i.sectionDetailMap[sectionId]
	return section, ok
}

func (i ForumHelper) GetAllSectionIds() []int {
	var sectionIds []int
	for sectionId := range i.sectionMap {
		sectionIds = append(sectionIds, sectionId)
	}
	return sectionIds
}

func (i ForumHelper) GetAllSectionDetails() []SectionDetail {
	var sections []SectionDetail
	for _, section := range i.sectionDetailMap {
		sections = append(sections, section)
	}
	return sections
}

func NewDefaultStructure() []Forum {
	return []Forum{
		{
			ForumType: "成人BT",
			SectionList: []Section{
				{SectionId: 561, SectionName: "第一会所新片"},
				{SectionId: 519, SectionName: "原创培训"},
				{SectionId: 143, SectionName: "亚无原创"},
				{SectionId: 25, SectionName: "亚无转贴"},
				{SectionId: 230, SectionName: "亚有原创"},
				{SectionId: 58, SectionName: "亚有转贴"},
				{SectionId: 229, SectionName: "欧无原创"},
				{SectionId: 77, SectionName: "欧无转贴"},
				{SectionId: 231, SectionName: "动漫原创"},
				{SectionId: 27, SectionName: "动漫转贴"},
				{SectionId: 406, SectionName: "新手原创"},
				{SectionId: 394, SectionName: "分流宣传"},
				{SectionId: 530, SectionName: "自拍原创"},
			},
		},
		{
			ForumType: "资源共享",
			SectionList: []Section{
				{SectionId: 187, SectionName: "成人网盘"},
				{SectionId: 327, SectionName: "正规影视"},
				{SectionId: 426, SectionName: "成人影视"},
				{SectionId: 500, SectionName: "小说打包"},
				{SectionId: 121, SectionName: "A V 交流"},
				{SectionId: 400, SectionName: "资源悬赏"},
				{SectionId: 110, SectionName: "资源售卖"},
			},
		},
		{
			ForumType: "美图",
			SectionList: []Section{
				{SectionId: 250, SectionName: "原创超市"},
				{SectionId: 277, SectionName: "原创打包"},
				{SectionId: 430, SectionName: "新手原创"},
				{SectionId: 64, SectionName: "东方靓女"},
				{SectionId: 68, SectionName: "西洋靓女"},
				{SectionId: 184, SectionName: "精品套图"},
				{SectionId: 61, SectionName: "星梦奇缘"},
				{SectionId: 249, SectionName: "高跟丝袜"},
				{SectionId: 432, SectionName: "丝魅鉴赏"},
				{SectionId: 242, SectionName: "熟女乱伦"},
				{SectionId: 60, SectionName: "卡通贴图"},
				{SectionId: 62, SectionName: "网友自拍"},
				{SectionId: 495, SectionName: "若兰居"},
				{SectionId: 411, SectionName: "自拍VIP"},
				{SectionId: 252, SectionName: "唯美图文"},
				{SectionId: 186, SectionName: "东方唯美"},
				{SectionId: 253, SectionName: "西方唯美"},
				{SectionId: 254, SectionName: "景致唯美"},
				{SectionId: 193, SectionName: "生活百态"},
				{SectionId: 63, SectionName: " medicines拾趣"},
				{SectionId: 524, SectionName: "恐怖殿堂"},
				{SectionId: 427, SectionName: "魅惑瞬间"},
				{SectionId: 183, SectionName: "网红前沿"},
				{SectionId: 419, SectionName: "偷拍分享"},
			},
		},
		{
			ForumType: "文学",
			SectionList: []Section{
				{SectionId: 480, SectionName: "作者会宾室"},
				{SectionId: 322, SectionName: "文学作者"},
				{SectionId: 383, SectionName: "原创人生"},
				{SectionId: 391, SectionName: "评论推荐"},
				{SectionId: 423, SectionName: "文学交流"},
				{SectionId: 390, SectionName: "征文活动"},
				{SectionId: 359, SectionName: "旧文展览"},
				{SectionId: 279, SectionName: "收集藏书馆-人妻意淫区"},
				{SectionId: 83, SectionName: "收集藏书馆-乱伦迷情区"},
				{SectionId: 96, SectionName: "收集藏书馆-武侠玄幻区"},
				{SectionId: 334, SectionName: "收集藏书馆-长篇收藏区"},
				{SectionId: 385, SectionName: "电子书馆-原创分享区"},
				{SectionId: 368, SectionName: "电子书馆-转帖分享区"},
				{SectionId: 382, SectionName: "五味书斋-聆风轩"},
				{SectionId: 381, SectionName: "五味书斋-雅韵斋"},
				{SectionId: 360, SectionName: "五味书斋-典藏阁"},
			},
		},
		{
			ForumType: "信息",
			SectionList: []Section{
				{SectionId: 467, SectionName: "北京信息"},
				{SectionId: 150, SectionName: "良家情感"},
				{SectionId: 148, SectionName: "东北华北"},
				{SectionId: 153, SectionName: "西北西南"},
				{SectionId: 149, SectionName: "华中华东"},
				{SectionId: 151, SectionName: "华南港澳台"},
				{SectionId: 449, SectionName: "海外信息"},
				{SectionId: 74, SectionName: "性技学习"},
				{SectionId: 357, SectionName: "男女性健康"},
				{SectionId: 338, SectionName: "经验交流"},
				{SectionId: 416, SectionName: "楼凤信息"},
				{SectionId: 522, SectionName: "毕业区"},
				{SectionId: 472, SectionName: "新贴审核"},
				{SectionId: 532, SectionName: "在水一方"},
				{SectionId: 538, SectionName: "征文大赛现场"},
				{SectionId: 543, SectionName: "今夜不设防"},
			},
		},
		{
			ForumType: "在线视频",
			SectionList: []Section{
				{SectionId: 563, SectionName: "观看短视频"},
				{SectionId: 438, SectionName: "新帖发布"},
				{SectionId: 349, SectionName: "明星三级"},
				{SectionId: 350, SectionName: "成人视频"},
				{SectionId: 213, SectionName: "居家自拍"},
				{SectionId: 200, SectionName: "成人卡通"},
			},
		},
	}
}
