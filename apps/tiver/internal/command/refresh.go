package command

import (
	"context"
	"fmt"
	"os"
	"time"

	pkg "github.com/penwyp/hydra/apps/tiver/internal"
	"github.com/penwyp/hydra/apps/tiver/internal/app"
	_const "github.com/penwyp/hydra/apps/tiver/internal/const"
	"github.com/penwyp/hydra/apps/tiver/internal/errors"
	"github.com/penwyp/hydra/apps/tiver/internal/model/param"
	"github.com/penwyp/hydra/apps/tiver/internal/model/structure"
	"github.com/penwyp/hydra/shared/json"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	sectionIdInt   int
	logLevel       string
	threadNum      uint
	proxy          string
	noProxy        bool
	timeoutSeconds int
)

func NewRefreshCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "refresh",
		Short: "refresh threads of s",
		Long:  ``,
		Run:   doRefreshCommand,
	}

	cmd.PersistentFlags().UintVarP(&threadNum, "threadNum", "n", 3, "thread num")
	cmd.PersistentFlags().StringVarP(&proxy, "proxy", "p", "http://127.0.0.1:6152", "proxy url")
	cmd.PersistentFlags().BoolVar(&noProxy, "no-proxy", false, "disable proxy for requests")
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().IntVarP(&sectionIdInt, "sectionId", "s", 0, "specific sectionId to refresh, 0 means all sections (60/334/359/368/385/...)")
	cmd.PersistentFlags().IntVar(&timeoutSeconds, "timeout", 3600, "timeout in seconds, exit directly when timeout")
	return cmd
}

func doRefreshCommand(cmd *cobra.Command, args []string) {
	// Set log level before initializing the app
	if logLevel != "" {
		log.InitLogger(log.LoggerOption{LogLevel: logLevel})
	}

	application := app.MustInitialize()

	// Validate sectionId
	forumHelper := structure.NewForumHelper()
	var sectionIds []int
	if sectionIdInt == 0 {
		// Scan all sections
		sectionIds = forumHelper.GetAllSectionIds()
		log.Logger.Info(fmt.Sprintf("scanning all %d sections", len(sectionIds)))
	} else {
		// Validate single sectionId
		if !forumHelper.HasSectionId(sectionIdInt) {
			appErr := errors.NewAppError(errors.ValidationError, fmt.Sprintf("invalid sectionId: %d", sectionIdInt), nil)
			errors.HandleFatalError(appErr, "invalid section ID provided")
		}
		sectionIds = []int{sectionIdInt}
		log.Logger.Info(fmt.Sprintf("scanning single section: %d", sectionIdInt))
	}

	// Use proxy from config if not provided, unless disabled
	var proxyToUse string
	if !noProxy {
		proxyToUse = proxy
		if proxyToUse == "" {
			proxyToUse = application.Config.HTTP.Proxy
		}
	}

	// Convert sectionIds to SectionId type
	var sections []_const.SectionId
	for _, id := range sectionIds {
		sections = append(sections, _const.SectionId(id))
	}

	refreshParam := &param.RefreshParam{
		ThreadNum: threadNum,
		Proxy:     proxyToUse,
		Sections:  sections,
		NoProxy:   noProxy,
	}
	refreshParam.SetDefault()
	if err := refreshParam.Validate(); err != nil {
		appErr := errors.NewAppError(errors.ValidationError, "refresh parameter validation failed", err)
		errors.HandleFatalError(appErr, "invalid refresh parameters")
	}

	refreshParamBytes, _ := json.Marshal(&refreshParam)
	log.Logger.Info("refresh run param", zap.String("value", string(refreshParamBytes)))

	if noProxy {
		log.Logger.Info("proxy disabled by --no-proxy flag")
	}

	// Use common location and headers
	location := time.UTC
	headers := pkg.GetDefaultHeaders()

	threadRefresher := pkg.NewThreadRefresher(refreshParam, location, headers)
	threadRefresher.AddOne()

	// Create context with timeout
	ctx, cancel := context.WithTimeout(cmd.Context(), time.Duration(timeoutSeconds)*time.Second)
	defer cancel()

	// Create a channel to signal completion
	done := make(chan struct{})

	// Run the refresh in a goroutine
	go func() {
		defer close(done)
		threadRefresher.FetchThreadStatusToDB(ctx)
		threadRefresher.Wait()
	}()

	// Wait for either completion or timeout
	select {
	case <-done:
		log.Logger.Info("refresh done") // 中文注释：统一的流程结束日志
	case <-ctx.Done():
		log.Logger.Info("refresh timeout, exiting directly", zap.Int("timeoutSeconds", timeoutSeconds))
		os.Exit(0)
	}
}
