package command

import (
	"time"

	pkg "github.com/penwyp/hydra/apps/tiver/internal"
	"github.com/penwyp/hydra/apps/tiver/internal/app"
	"github.com/penwyp/hydra/apps/tiver/internal/errors"
	"github.com/penwyp/hydra/apps/tiver/internal/model/param"
	"github.com/penwyp/hydra/shared/json"
	"github.com/penwyp/hydra/shared/log"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	imageThreadParseGoroutineNum    uint
	threadImageDownloadGoroutineNum uint
	forumDownloadOrderType          uint
	forumDownloadWaterMark          uint
	beginTime                       string
	dataDir                         string
	imageDomainFilter               []string
	threadTypes                     []string
	proxyUrl                        string
)

var filterDomain = []string{"www.kanjiantu.com", "kanjiantu.com", "s7tu.com", "www.skeimg.com", "sxeimg.com", "img301.com", "luoimg.com", "pic.jitudisk.com"}

func NewDownloadCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "download",
		Short: "download thread images of s",
		Long:  ``,
		Run:   doDownloadCommand,
	}

	cmd.PersistentFlags().StringSliceVarP(&threadTypes, "threadTypes", "t", []string{"中文黑白", "中文彩漫"}, "thread num")
	cmd.PersistentFlags().StringVarP(&proxyUrl, "proxyUrl", "p", "http://127.0.0.1:6152", "proxy url")
	cmd.PersistentFlags().StringVarP(&logLevel, "logLevel", "l", "info", "info/debug")
	cmd.PersistentFlags().UintVarP(&imageThreadParseGoroutineNum, "imageThreadParseGoroutineNum", "i", 5, "image thread parse goroutine num")
	cmd.PersistentFlags().UintVarP(&threadImageDownloadGoroutineNum, "threadImageDownloadGoroutineNum", "g", 5, "thread image download goroutine num")
	cmd.PersistentFlags().UintVarP(&forumDownloadOrderType, "forumDownloadOrderType", "o", 1, "1:thumbs,2:total_click,3:total_reply")
	cmd.PersistentFlags().UintVarP(&forumDownloadWaterMark, "forumDownloadWaterMark", "w", 10, "download water mark, for thumbs/total_click/total_reply")
	cmd.PersistentFlags().StringVarP(&beginTime, "beginTime", "b", "2020-06-30 00:00:01", "thread first upload time")
	cmd.PersistentFlags().StringVarP(&dataDir, "dataDir", "d", "data", "data dir")
	cmd.PersistentFlags().StringSliceVarP(&imageDomainFilter, "imageDomainFilter", "f", filterDomain, "image domain filter")

	return cmd
}

func doDownloadCommand(cmd *cobra.Command, args []string) {
	application := app.MustInitialize()

	// Use default data directory from config if not provided
	dataDirToUse := dataDir
	if dataDirToUse == "" {
		dataDirToUse = application.Config.App.DataDir
	}

	downloadParam := &param.DownloadParam{
		ImageThreadParseGoroutineNum:    imageThreadParseGoroutineNum,
		ThreadImageDownloadGoroutineNum: threadImageDownloadGoroutineNum,
		DownloadOrderType:               forumDownloadOrderType,
		DownloadThreadTypes:             threadTypes,
		DownloadWatermark:               forumDownloadWaterMark,
		DataDir:                         dataDirToUse,
		BeginTime:                       time.Time{},
		ProxyUrl:                        proxyUrl,
		ImageDomainFilters:              imageDomainFilter,
	}

	// 设置默认值
	downloadParam.SetDefault()
	// 校验参数
	if err := downloadParam.Validate(); err != nil {
		appErr := errors.NewAppError(errors.ValidationError, "download parameter validation failed", err)
		errors.HandleFatalError(appErr, "invalid download parameters")
	}

	downloadParamBytes, _ := json.Marshal(&downloadParam)
	log.Logger.Info("download run param", zap.String("value", string(downloadParamBytes)))

	// Use headers from common config
	headers := pkg.GetDefaultHeaders()

	imageDownloader := pkg.NewThreadImageDownloader(headers)
	imageDownloader.ApplyParam(downloadParam)
	imageDownloader.FetchThreadToDownloadImage(cmd.Context())
	log.Logger.Info("refresh done")
}
