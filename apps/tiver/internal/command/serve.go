package command

import (
	"fmt"

	"github.com/penwyp/hydra/apps/tiver/internal/server"
	"github.com/spf13/cobra"
)

var (
	serverPort int
	webRoot    string
)

func NewServeCommand() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "serve",
		Short: "Start web server to browse threads",
		Long:  `Start a web server that provides a web interface for browsing and filtering thread data from the database.`,
		Run:   doServeCommand,
	}

	cmd.PersistentFlags().IntVarP(&serverPort, "port", "p", 8030, "server port")
	cmd.PersistentFlags().StringVarP(&webRoot, "web-root", "w", "apps/tiver-web", "path to web static files")
	return cmd
}

func doServeCommand(cmd *cobra.Command, args []string) {
	fmt.Printf("Starting server on port %d...\n", serverPort)
	fmt.Printf("Serving static files from %s\n", webRoot)

	srv := server.NewServer(serverPort, webRoot)
	if err := srv.Run(); err != nil {
		fmt.Printf("Failed to start server: %v\n", err)
	}
}
