package config

import (
	"os"
	"strconv"
	"strings"
)

// Config holds all configuration for the Tiver application
type Config struct {
	Database DatabaseConfig
	HTTP     HTTPConfig
	App      AppConfig
}

// DatabaseConfig holds database connection configuration
type DatabaseConfig struct {
	DSN      string
	Host     string
	Port     int
	Username string
	Password string
	Database string
	TLSName  string
}

// HTTPConfig holds HTTP client configuration
type HTTPConfig struct {
	UserAgent string
	Timeout   int
	Proxy     string
	Cookie    string
}

// AppConfig holds application-specific configuration
type AppConfig struct {
	LogLevel string
	DataDir  string
	Audit    AuditConfig
}

// AuditConfig holds audit logging configuration
type AuditConfig struct {
	SQLEnabled bool   // Enable SQL query auditing
	LogPath    string // Path to audit log file (optional, defaults to ~/.tiver/audit/tiver-web-sql.log)
}

// LoadConfig loads configuration from environment variables with fallback defaults
func LoadConfig() *Config {
	config := &Config{
		Database: DatabaseConfig{
			DSN:      getEnvOrDefault("TIVER_DB_DSN", "4We1Pjvy7NZzhk4.root:zkM9kgtpcBv4V5ke@tcp(gateway01.ap-southeast-1.prod.alicloud.tidbcloud.com:4000)/tiver?tls=tidb&parseTime=true&loc=UTC"),
			Host:     getEnvOrDefault("TIVER_DB_HOST", "gateway01.ap-southeast-1.prod.alicloud.tidbcloud.com"),
			Port:     getEnvIntOrDefault("TIVER_DB_PORT", 4000),
			Username: getEnvOrDefault("TIVER_DB_USERNAME", "4We1Pjvy7NZzhk4.root"),
			Password: getEnvOrDefault("TIVER_DB_PASSWORD", "zkM9kgtpcBv4V5ke"),
			Database: getEnvOrDefault("TIVER_DB_NAME", "tiver"),
			TLSName:  getEnvOrDefault("TIVER_DB_TLS_NAME", "tidb"),
		},
		HTTP: HTTPConfig{
			UserAgent: getEnvOrDefault("TIVER_HTTP_USER_AGENT", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"),
			Timeout:   getEnvIntOrDefault("TIVER_HTTP_TIMEOUT", 30),
			Proxy:     getEnvOrDefault("TIVER_HTTP_PROXY", ""),
			Cookie:    getEnvOrDefault("TIVER_HTTP_COOKIE", "cdb2_cookietime=315360000; cdb2_smile=1D1; cdb2_auth=dDCUrRjCncjp3%2FXjtIu6k4ACerNXaWM1PJ05RVewTl9HS57v1bD1hHMyuKaSlg7t%2FyITKocdhwvz46Xy; cdb2_readapmid=509; cdb2_sid=BNEvi8; cdb2_uvStat=1755968386; cf_clearance=_XUhf9BdI9W.nruZAFTMEZ7A3BMGgvJtLftlCUg.kmA-1755968387-1.2.1.1-U_BfZnk2.NnCI9bqS53IwzwD.yx.M7AvYY7_Uxbet9o7LEoU2oFwWi1YTxPwoj5m5W7B5jsZ3Mdh6V32j48ejqGqLktn2TSgmUUbsiYIweZFyjMZAaz.IAm6d2XKCDMpa8S39hGDoRECGkzWcCD7lO2eyekDnF1v3odHJInqOWFLvx6sF0ITHwx.1LcJewiN9chtftd0A_JrVWLL0dbAYPG0gKJehlC0IowRdruHhw4uLF00vnY8P6HQacL6Gg4d; cdb2_fuvs=151.60; cdb2_oldtopics=D11807224D12013731D11774555D11611502D12083883D12167670D11908280D12001461D12167617D12071100D12060300D11759429D11838212D12246182D12128980D11935815D11719873D11764286D12127363D12138538D12157737D"),
		},
		App: AppConfig{
			LogLevel: getEnvOrDefault("TIVER_LOG_LEVEL", "info"),
			DataDir:  getEnvOrDefault("TIVER_DATA_DIR", "./data"),
			Audit: AuditConfig{
				SQLEnabled: getEnvBoolOrDefault("TIVER_AUDIT_SQL_ENABLED", true),
				LogPath:    getEnvOrDefault("TIVER_AUDIT_LOG_PATH", ""),
			},
		},
	}

	return config
}

// getEnvOrDefault returns the value of the environment variable if it exists, otherwise returns the default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvIntOrDefault returns the integer value of the environment variable if it exists and is valid, otherwise returns the default value
func getEnvIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBoolOrDefault returns the boolean value of the environment variable if it exists and is valid, otherwise returns the default value
func getEnvBoolOrDefault(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		switch strings.ToLower(value) {
		case "true", "1", "yes", "on":
			return true
		case "false", "0", "no", "off":
			return false
		}
	}
	return defaultValue
}
