-- Verify optimized index performance after dropping redundant indexes
-- Date: 2025-08-15

-- Current optimized index set (8 total):
-- PRIMARY KEY (`id`) CLUSTERED
-- UNIQUE KEY `uk_url_digest` (`url_digest`)
-- <PERSON><PERSON>Y `idx_section_upload_click` (`section_id`,`first_upload_time`,`total_click`)
-- <PERSON><PERSON>Y `idx_forum_section` (`forum_type`,`section_id`)
-- KEY `idx_update_time` (`update_time`)
-- KEY `idx_section_upload_update` (`section_id`,`first_upload_time`,`update_time`)
-- KEY `idx_section_upload_reply` (`section_id`,`first_upload_time`,`total_reply`)
-- KEY `idx_section_upload_thumbs` (`section_id`,`first_upload_time`,`thumbs`)

-- Test 1: Verify section+time queries use appropriate composite indexes
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY total_click DESC LIMIT 50;

EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY total_reply DESC LIMIT 50;

EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY update_time DESC LIMIT 50;

-- Test 2: Verify aggregation queries
EXPLAIN SELECT forum_type, COUNT(*) as count 
FROM sis_normal_thread 
WHERE forum_type IS NOT NULL AND forum_type != '' 
GROUP BY forum_type;

-- Test 3: Measure actual query performance
SELECT 'Test: section_id + first_upload_time + ORDER BY total_click' as test_name;
SELECT COUNT(*) FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15';

SELECT 'Test: Forum type aggregation' as test_name;
SELECT forum_type, COUNT(*) as count 
FROM sis_normal_thread 
WHERE forum_type IS NOT NULL AND forum_type != '' 
GROUP BY forum_type;

-- Summary: Dropped indexes that were removed
-- 1. idx_upload_click - Was redundant with idx_section_upload_click
-- 2. idx_author - No queries used author field
-- 3. idx_last_post_time - No queries used this field
-- 4. idx_create_time - No queries used this field  
-- 5. idx_section_upload_time - Completely redundant with other composite indexes