-- Analysis of sis_normal_thread indexes for Tiver Web Application
-- Total rows: 22,300
-- Date: 2025-08-15

-- Current indexes:
-- PRIMARY KEY (`id`) /*T![clustered_index] CLUSTERED */
-- UNIQUE KEY `uk_url_digest` (`url_digest`)
-- <PERSON><PERSON>Y `idx_section_upload_click` (`section_id`,`first_upload_time`,`total_click`)
-- KEY `idx_forum_section` (`forum_type`,`section_id`)
-- KEY `idx_upload_click` (`first_upload_time`,`total_click`)
-- KEY `idx_author` (`author`)
-- KEY `idx_last_post_time` (`last_post_time`)
-- KEY `idx_create_time` (`create_time`)
-- KEY `idx_update_time` (`update_time`)
-- KEY `idx_section_upload_update` (`section_id`,`first_upload_time`,`update_time`)
-- KEY `idx_section_upload_reply` (`section_id`,`first_upload_time`,`total_reply`)
-- <PERSON><PERSON>Y `idx_section_upload_thumbs` (`section_id`,`first_upload_time`,`thumbs`)
-- KEY `idx_section_upload_time` (`section_id`,`first_upload_time`)

-- Common query patterns from audit log analysis:
-- 1. Filtering by section_id and first_upload_time, ordering by various fields (50-100ms avg)
-- 2. Group by queries for forum_type and section_id (60ms avg)
-- 3. Get distinct thread_type values (60ms avg)

-- Test 1: ORDER BY total_click DESC
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY total_click DESC LIMIT 50;

-- Test 2: ORDER BY update_time DESC
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY update_time DESC LIMIT 50;

-- Test 3: ORDER BY total_reply DESC
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY total_reply DESC LIMIT 50;

-- Test 4: ORDER BY thumbs DESC
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY thumbs DESC LIMIT 50;

-- Test 5: ORDER BY first_upload_time DESC
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY first_upload_time DESC LIMIT 50;

-- Test 6: ORDER BY first_upload_time ASC
EXPLAIN SELECT * FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15' 
ORDER BY first_upload_time ASC LIMIT 50;

-- Test 7: COUNT query
EXPLAIN SELECT COUNT(*) FROM sis_normal_thread 
WHERE section_id = 60 AND first_upload_time >= '2025-02-15';

-- Test 8: Forum type aggregation
EXPLAIN SELECT forum_type, COUNT(*) as count 
FROM sis_normal_thread 
WHERE forum_type IS NOT NULL AND forum_type != '' 
GROUP BY forum_type;

-- Test 9: Section aggregation
EXPLAIN SELECT section_id, COUNT(*) as count 
FROM sis_normal_thread 
WHERE section_id IS NOT NULL 
GROUP BY section_id;

-- ========================================
-- INDEX REDUNDANCY ANALYSIS
-- ========================================

-- REDUNDANT INDEXES (Can be safely dropped):
-- 1. idx_upload_click (`first_upload_time`,`total_click`)
--    REASON: Redundant - idx_section_upload_click covers first_upload_time prefix
--    USAGE: Queries with first_upload_time can use idx_section_upload_click
--
-- 2. idx_author (`author`)
--    REASON: No queries in audit log filter by author
--    USAGE: Zero usage based on query patterns
--
-- 3. idx_last_post_time (`last_post_time`)
--    REASON: No queries in audit log use this field
--    USAGE: Zero usage based on query patterns
--
-- 4. idx_create_time (`create_time`)
--    REASON: No queries in audit log order by or filter on create_time
--    USAGE: Zero usage based on query patterns
--
-- 5. idx_section_upload_time (`section_id`,`first_upload_time`)
--    REASON: Completely redundant - multiple other indexes have same prefix
--    COVERED BY: idx_section_upload_click, idx_section_upload_update, 
--                idx_section_upload_reply, idx_section_upload_thumbs

-- INDEXES TO KEEP:
-- 1. idx_section_upload_click - Used for section+time filtering with click ordering
-- 2. idx_section_upload_reply - Used for section+time filtering with reply ordering  
-- 3. idx_section_upload_update - Used for section+time filtering with update_time ordering
-- 4. idx_section_upload_thumbs - Used for section+time filtering with thumbs ordering
-- 5. idx_forum_section - Used for forum_type grouping and filtering
-- 6. idx_update_time - Used for default ordering by update_time

-- ========================================
-- RECOMMENDED ACTIONS
-- ========================================

-- Step 1: Drop redundant indexes to improve write performance and reduce storage
ALTER TABLE sis_normal_thread DROP INDEX idx_upload_click;
ALTER TABLE sis_normal_thread DROP INDEX idx_author;
ALTER TABLE sis_normal_thread DROP INDEX idx_last_post_time;
ALTER TABLE sis_normal_thread DROP INDEX idx_create_time;
ALTER TABLE sis_normal_thread DROP INDEX idx_section_upload_time;

-- Expected benefits:
-- 1. Reduced index maintenance overhead during INSERT/UPDATE operations
-- 2. Less storage space used (5 fewer indexes on 22,300 rows)
-- 3. Simplified query optimizer decisions
-- 4. No impact on query performance (all queries still covered by remaining indexes)