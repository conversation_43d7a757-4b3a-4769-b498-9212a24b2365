package main

import (
	"fmt"
	"os"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/samber/lo"
)

const K = 1000
const W = 10000

const housingProvidentFundMaxValue = 41190.0

type Offer struct {
	Name                 string
	MonthSalary          float64
	YearEndAwardMonthNum float64
	Options              float64

	MealAllowance    float64
	HousingAllowance float64

	HousingProvidentFundPercentage float64
}

func R(v float64) string {
	return PackageMoneyToHumanReadable(v)
}

func PackageMoneyToHumanReadable(v float64) string {
	return fmt.Sprintf("%.2f万", v/10000)
}

func (i Offer) TotalPackage() float64 {
	salary := i.MonthSalary*12 + i.MonthSalary*i.YearEndAwardMonthNum + i.Options
	allowance := i.MealAllowance*12 + i.HousingAllowance*12
	return salary + allowance
}

// TaxBracket represents a single tax bracket
type TaxBracket struct {
	rate      float64
	deduction float64
}

// taxBrackets defines the tax brackets according to the given tax table
var taxBrackets = []TaxBracket{
	{rate: 0.03, deduction: 0},
	{rate: 0.10, deduction: 2520},
	{rate: 0.20, deduction: 16920},
	{rate: 0.25, deduction: 31920},
	{rate: 0.30, deduction: 52920},
	{rate: 0.35, deduction: 85920},
	{rate: 0.45, deduction: 181920},
}

// CalculateTax calculates the personal income tax based on the annual taxable income
func CalculateTax(annualIncome float64) float64 {
	taxableIncome := annualIncome - 5000*12 // deduct the tax threshold
	if taxableIncome <= 0 {
		return 0
	}

	var tax float64
	switch {
	case taxableIncome <= 36000:
		tax = taxableIncome*taxBrackets[0].rate - taxBrackets[0].deduction
	case taxableIncome <= 144000:
		tax = taxableIncome*taxBrackets[1].rate - taxBrackets[1].deduction
	case taxableIncome <= 300000:
		tax = taxableIncome*taxBrackets[2].rate - taxBrackets[2].deduction
	case taxableIncome <= 420000:
		tax = taxableIncome*taxBrackets[3].rate - taxBrackets[3].deduction
	case taxableIncome <= 660000:
		tax = taxableIncome*taxBrackets[4].rate - taxBrackets[4].deduction
	case taxableIncome <= 960000:
		tax = taxableIncome*taxBrackets[5].rate - taxBrackets[5].deduction
	default:
		tax = taxableIncome*taxBrackets[6].rate - taxBrackets[6].deduction
	}

	return tax
}

func (i Offer) TotalTaxedPackage() float64 {
	totalPackageVal := i.TotalPackage()

	taxVal := CalculateTax(totalPackageVal)
	return totalPackageVal - taxVal
}

func (i Offer) TotalHousingProvidentFund() float64 {
	return lo.Min([]float64{i.MonthSalary, housingProvidentFundMaxValue}) * i.HousingProvidentFundPercentage / 100 * 2 * 12
}

type Offers []Offer

var (
	offer1 = Offer{
		Name:                           "38K+10W",
		MonthSalary:                    38 * K,
		YearEndAwardMonthNum:           3,
		Options:                        10 * W,
		MealAllowance:                  300,
		HousingProvidentFundPercentage: 5,
	}
	offer2 = Offer{
		Name:                           "40K+6W",
		MonthSalary:                    40 * K,
		YearEndAwardMonthNum:           3,
		Options:                        6 * W,
		MealAllowance:                  300,
		HousingProvidentFundPercentage: 5,
	}
	offer3 = Offer{
		Name:                           "44K+0W",
		MonthSalary:                    44 * K,
		YearEndAwardMonthNum:           3,
		Options:                        0 * W,
		MealAllowance:                  300,
		HousingProvidentFundPercentage: 5,
	}
	offer4 = Offer{
		Name:                           "50K+0W",
		MonthSalary:                    50 * K,
		YearEndAwardMonthNum:           3,
		Options:                        0 * W,
		MealAllowance:                  300,
		HousingProvidentFundPercentage: 5,
	}
	offer5 = Offer{
		Name:                           "48K+10W",
		MonthSalary:                    48 * K,
		YearEndAwardMonthNum:           3,
		Options:                        10 * W,
		MealAllowance:                  300,
		HousingProvidentFundPercentage: 5,
	}
	offer6 = Offer{
		Name:                           "48K+8W",
		MonthSalary:                    48 * K,
		YearEndAwardMonthNum:           3,
		Options:                        8 * W,
		MealAllowance:                  300,
		HousingProvidentFundPercentage: 5,
	}
	byteDanceOffer = Offer{
		Name:                           "字节",
		MonthSalary:                    37 * K,
		YearEndAwardMonthNum:           3.5,
		Options:                        0 * W,
		HousingAllowance:               1000,
		HousingProvidentFundPercentage: 12,
	}

	offers = Offers{offer1, offer2, offer3, offer4, offer5, offer6, byteDanceOffer}
)

func main() {
	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.AppendHeader(table.Row{"薪资方案", "月薪", "期权", "餐补", "房补", "总包", "税后", "公积金", "税后+公积金"})

	for _, o := range offers {
		t.AppendRows([]table.Row{{
			o.Name,
			R(o.MonthSalary),
			R(o.Options),
			R(o.MealAllowance * 12),
			R(o.HousingAllowance * 12),
			R(o.TotalPackage()),
			R(o.TotalTaxedPackage()),
			R(o.TotalHousingProvidentFund()),
			R(o.TotalTaxedPackage() + o.TotalHousingProvidentFund()),
		}})
	}
	t.Render()
}
