<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TIVER - WEB</title>
    <!-- iOS specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="TIVER">
    <meta name="format-detection" content="telephone=no">
    <meta name="HandheldFriendly" content="true">
    <!-- Favicon support for all browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16.png">
    <link rel="icon" type="image/png" sizes="48x48" href="/favicon-48.png">
    <link rel="icon" type="image/png" sizes="64x64" href="/favicon-64.png">
    <link rel="icon" type="image/png" sizes="128x128" href="/favicon-128.png">
    <link rel="icon" type="image/png" sizes="256x256" href="/favicon-256.png">
    <link rel="shortcut icon" href="/favicon-32.png">
    <link rel="apple-touch-icon" sizes="128x128" href="/favicon-128.png">
    <link rel="apple-touch-icon" sizes="256x256" href="/favicon-256.png">
    <link rel="mask-icon" href="/index.svg" color="#9AF4EF">
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    <!-- iOS Splash Screens -->
    <link rel="apple-touch-startup-image" href="/favicon-256.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-sky-100 min-h-screen">
    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[10002]">
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/30 w-full max-w-md m-4 p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">登录 TIVER</h2>
            <form id="loginForm" class="space-y-4" method="post" action="javascript:void(0)">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <input type="text" id="username" name="username" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请输入用户名">
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <input type="password" id="password" name="password" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请输入密码">
                </div>
                <div id="loginError" class="hidden text-red-600 text-sm text-center"></div>
                <button type="submit" 
                        class="w-full px-6 py-3 bg-gradient-to-r from-blue-400 to-sky-400 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-200">
                    登录
                </button>
            </form>
        </div>
    </div>

    <div class="px-2 sm:px-4 py-4 sm:py-8">

        <!-- Header with logout button -->
        <div class="flex justify-between items-center mb-4 sm:mb-6">
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-800">TIVER WEB</h1>
            <button id="logoutBtn" class="px-3 sm:px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm sm:text-base">
                退出登录
            </button>
        </div>

        <!-- Filters -->
        <div class="filter-card bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-xl border border-white/20 p-4 sm:p-8 mb-4 sm:mb-8">
            <div class="flex flex-col sm:flex-row sm:flex-wrap gap-3 sm:gap-4 sm:items-end">
                <!-- Forum Type Filter -->
                <div class="w-full sm:flex-shrink-0 sm:w-56">
                    <label for="forumType" class="block text-sm font-medium text-gray-700 mb-2">版块</label>
                    <div class="modal-select-container relative">
                        <input type="text" id="forumTypeSearch" placeholder="点击选择版块..." readonly
                               class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 cursor-pointer bg-white">
                        <button type="button" id="clearForumType" class="clear-btn absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors" title="清空选择">
                            ✕
                        </button>
                        <select id="forumType" class="hidden">
                            <option value="">所有版块</option>
                        </select>
                    </div>
                </div>

                <!-- Section Type Filter -->
                <div class="w-full sm:flex-shrink-0 sm:w-56">
                    <label for="sectionType" class="block text-sm font-medium text-gray-700 mb-2">分区</label>
                    <div class="modal-select-container relative">
                        <input type="text" id="sectionTypeSearch" placeholder="点击选择分区..." readonly
                               class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 cursor-pointer bg-white">
                        <button type="button" id="clearSectionType" class="clear-btn absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors" title="清空选择">
                            ✕
                        </button>
                        <select id="sectionType" class="hidden">
                            <option value="">所有分区</option>
                        </select>
                    </div>
                </div>

                <!-- Name Search -->
                <div class="w-full sm:flex-1 sm:min-w-48 sm:max-w-80">
                    <label for="nameSearch" class="block text-sm font-medium text-gray-700 mb-2">名称搜索</label>
                    <div class="relative">
                        <input type="text" id="nameSearch" placeholder="按名称搜索..." 
                               class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <button type="button" id="clearNameSearch" class="clear-btn absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors" title="清空搜索">
                            ✕
                        </button>
                    </div>
                </div>

                <!-- Create Time Threshold -->
                <div class="w-full sm:flex-shrink-0 sm:w-80">
                    <label for="createTimeThreshold" class="block text-sm font-medium text-gray-700 mb-2">上传时间</label>
                    <div class="flex flex-col sm:flex-row gap-2 sm:items-center sm:h-10">
                        <input type="date" id="createTimeThreshold" 
                               class="w-full sm:flex-shrink-0 sm:w-36 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <div class="flex gap-1 flex-wrap">
                            <button type="button" class="time-shortcut px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors" data-months="3">3月</button>
                            <button type="button" class="time-shortcut px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors" data-months="6">半年</button>
                            <button type="button" class="time-shortcut px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors" data-months="12">1年</button>
                            <button type="button" class="time-shortcut px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors" data-months="24">2年</button>
                            <button type="button" class="time-shortcut px-2 py-1 text-xs bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors" data-months="36">3年</button>
                        </div>
                    </div>
                </div>

                <!-- Search Button -->
                <div class="w-full sm:flex-shrink-0">
                    <label class="hidden sm:block text-sm font-medium text-transparent mb-2">搜索</label>
                    <button id="searchBtn" class="search-btn w-full sm:w-auto px-6 py-2 bg-gradient-to-r from-blue-400 to-sky-400 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-200">
                        🔍 搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- Pagination and Results Info -->
        <div class="mb-4 flex flex-col sm:flex-row justify-center sm:justify-end items-center gap-3 sm:space-x-3">
            <div id="pagination" class="flex items-center space-x-2">
                <!-- Pagination buttons will be inserted here -->
            </div>
            <select id="pageSize" class="px-3 py-2 border border-gray-300 rounded-lg text-sm h-10 bg-white min-w-48">
                <option value="25">25条/页</option>
                <option value="50" selected>50条/页</option>
                <option value="100">100条/页</option>
            </select>
        </div>

        <!-- Table -->
        <div class="modern-table bg-white/90 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-2xl border border-white/30">
            <div class="overflow-x-auto">
                <table class="w-full min-w-[800px] divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-blue-400 to-sky-400 text-white">
                        <tr>
                            <th class="px-2 py-4 text-left text-sm font-bold text-white uppercase tracking-wider w-20 h-12 hidden sm:table-cell">📁 版块</th>
                            <th class="px-2 py-4 text-left text-sm font-bold text-white uppercase tracking-wider w-20 h-12 hidden sm:table-cell">📂 分区</th>
                            <th class="px-2 py-4 text-left text-sm font-bold text-white uppercase tracking-wider w-16 h-12">🏷️ 类型</th>
                            <th class="px-4 py-4 text-left text-sm font-bold text-white uppercase tracking-wider min-w-0 flex-1 h-12">📝 名称</th>
                            <th class="px-2 py-4 text-center text-sm font-bold text-white uppercase tracking-wider w-16 h-12">🔍 操作</th>
                            <th class="px-1 py-4 text-center text-sm font-bold text-white uppercase tracking-wider sortable-header cursor-pointer hover:bg-white/20 transition-colors w-16 h-12" data-sort="total_reply">
                                💬 回复
                            </th>
                            <th class="px-1 py-4 text-center text-sm font-bold text-white uppercase tracking-wider sortable-header cursor-pointer hover:bg-white/20 transition-colors w-16 h-12" data-sort="total_click">
                                👁️ 点击
                            </th>
                            <th class="px-1 py-4 text-center text-sm font-bold text-white uppercase tracking-wider sortable-header cursor-pointer hover:bg-white/20 transition-colors w-16 h-12" data-sort="thumbs">
                                👍 点赞
                            </th>
                            <th class="px-2 py-4 text-left text-sm font-bold text-white uppercase tracking-wider sortable-header cursor-pointer hover:bg-white/20 transition-colors w-24 h-12" data-sort="first_upload_time">
                                📅 上传
                            </th>
                            <th class="px-2 py-4 text-left text-sm font-bold text-white uppercase tracking-wider sortable-header cursor-pointer hover:bg-white/20 transition-colors w-24 h-12" data-sort="update_time">
                                🔄 更新
                            </th>
                        </tr>
                    </thead>
                    <tbody id="tableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Table rows will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loading" class="hidden fixed inset-0 bg-blue-900/10 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="loading-card bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-2xl border border-white/30">
                <div class="colorful-spinner mx-auto mb-4"></div>
                <p class="text-xl text-blue-500 font-semibold text-center">✨ 正在加载...</p>
            </div>
        </div>

        <!-- Modal Select Dialog -->
        <div id="modalSelectDialog" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[10001]">
            <div class="modal-dialog bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/30 w-full max-w-4xl max-h-[80vh] m-4">
                <div class="modal-header p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 id="modalTitle" class="text-xl font-semibold text-gray-800">选择选项</h3>
                        <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="mt-4">
                        <input type="text" id="modalSearchInput" placeholder="搜索选项..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <div class="modal-body p-6 overflow-y-auto max-h-96">
                    <div id="modalOptionsGrid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        <!-- Options will be populated here -->
                    </div>
                </div>
                <div class="modal-footer p-6 border-t border-gray-200 flex justify-end gap-3">
                    <button id="modalCancelBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                    <button id="modalConfirmBtn" class="px-4 py-2 bg-gradient-to-r from-blue-400 to-sky-400 text-white rounded-lg hover:from-blue-500 hover:to-sky-500 transition-colors">
                        确认选择
                    </button>
                </div>
            </div>
        </div>

    </div>

    <script src="/js/app.js"></script>
</body>
</html>
