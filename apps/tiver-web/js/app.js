// Global state
let currentPage = 1;
let totalPages = 1;
let totalRecords = 0;
let currentSortColumn = 'total_click';
let currentSortOrder = 'desc';

// Searchable select data
let forumTypeOptions = [];
let sectionTypeOptions = [];

// Authentication state
const AUTH_KEY = 'tiver_auth_token';
const VALID_USERNAME = 'nas';
const VALID_PASSWORD = 'acce-s6se/nas';

// Check authentication
function checkAuth() {
    const token = localStorage.getItem(AUTH_KEY);
    return token === btoa(`${VALID_USERNAME}:${VALID_PASSWORD}`);
}

// Handle login
function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');
    
    if (username === VALID_USERNAME && password === VALID_PASSWORD) {
        // Store auth token
        localStorage.setItem(AUTH_KEY, btoa(`${username}:${password}`));
        
        // Hide login modal
        document.getElementById('loginModal').classList.add('hidden');
        
        // Clear form
        document.getElementById('loginForm').reset();
        errorDiv.classList.add('hidden');
        
        // Initialize app
        initializeApp();
    } else {
        errorDiv.textContent = '用户名或密码错误';
        errorDiv.classList.remove('hidden');
    }
}

// Handle logout
function handleLogout() {
    // Remove auth token
    localStorage.removeItem(AUTH_KEY);
    
    // Reload page to show login modal
    window.location.reload();
}

// Initialize app after authentication
function initializeApp() {
    loadForumTypes();
    loadSectionTypes();
    setupEventListeners();
    setupSearchableSelects();
    setupModalEventListeners();
    setupKeyboardNavigation();
    setupClearButtons();
    setupTimeShortcuts();
    
    // Set default date to 6 months ago
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    document.getElementById('createTimeThreshold').value = sixMonthsAgo.toISOString().split('T')[0];
    
    // Setup logout button
    document.getElementById('logoutBtn').addEventListener('click', handleLogout);
    
    // Initialize sort indicators for default sorting
    updateSortIndicators();
}

// Detect iOS device
function isIOS() {
    return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
}

// Detect if device is mobile
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           window.matchMedia('(max-width: 768px)').matches;
}

// Handle iOS viewport height changes (for Safari's dynamic toolbar)
function updateViewportHeight() {
    if (isIOS()) {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }
}

// Add touch event support
function addTouchSupport(element, callback) {
    let touchStartY = 0;
    let touchEndY = 0;
    
    element.addEventListener('touchstart', function(e) {
        touchStartY = e.touches[0].clientY;
    }, { passive: true });
    
    element.addEventListener('touchend', function(e) {
        touchEndY = e.changedTouches[0].clientY;
        
        // Only trigger if it's a tap (not a swipe)
        if (Math.abs(touchEndY - touchStartY) < 10) {
            callback(e);
        }
    }, { passive: true });
    
    // Keep click event for non-touch devices
    element.addEventListener('click', callback);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Update viewport height for iOS
    updateViewportHeight();
    window.addEventListener('resize', updateViewportHeight);
    window.addEventListener('orientationchange', updateViewportHeight);
    
    // Check if authenticated
    if (checkAuth()) {
        // User is logged in, hide modal and initialize app
        document.getElementById('loginModal').classList.add('hidden');
        initializeApp();
    } else {
        // User not logged in, show login modal
        document.getElementById('loginModal').classList.remove('hidden');
        
        // Setup login form handler
        document.getElementById('loginForm').addEventListener('submit', handleLogin);
    }
});

// Setup event listeners
function setupEventListeners() {
    // Prevent iOS bounce scrolling on the body
    if (isIOS()) {
        document.body.addEventListener('touchmove', function(e) {
            if (e.target.closest('.overflow-y-auto, .overflow-x-auto, .modal-body, table')) {
                return; // Allow scrolling in these elements
            }
            if (e.scale !== 1) {
                e.preventDefault(); // Prevent pinch zoom
            }
        }, { passive: false });
    }
    
    // Use touch-friendly event for search button
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        addTouchSupport(searchBtn, function() {
            currentPage = 1;
            loadThreads();
        });
    }

    // Search on Enter key in name search
    document.getElementById('nameSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            currentPage = 1;
            loadThreads();
        }
    });

    // Page size change
    document.getElementById('pageSize').addEventListener('change', function() {
        currentPage = 1;
        loadThreads();
    });

    // Auto-search when filters change
    document.getElementById('forumType').addEventListener('change', function() {
        const selectedForum = this.value;
        
        // Reset section selection and reload sections based on forum
        document.getElementById('sectionType').value = '';
        loadSectionTypes(selectedForum);
        
        currentPage = 1;
        loadThreads();
    });

    document.getElementById('sectionType').addEventListener('change', function() {
        currentPage = 1;
        loadThreads();
    });

    // First upload threshold change
    document.getElementById('createTimeThreshold').addEventListener('change', function() {
        currentPage = 1;
        loadThreads();
    });

    // Setup sortable column headers with touch support
    document.querySelectorAll('.sortable-header').forEach(header => {
        addTouchSupport(header, function() {
            const sortColumn = header.getAttribute('data-sort');
            
            // If clicking the same column, cycle through: desc -> asc -> none -> desc
            if (sortColumn === currentSortColumn) {
                if (currentSortOrder === 'desc') {
                    currentSortOrder = 'asc';
                } else if (currentSortOrder === 'asc') {
                    // Reset to no sorting
                    currentSortColumn = '';
                    currentSortOrder = 'desc';
                } else {
                    currentSortOrder = 'desc';
                }
            } else {
                // New column, default to descending
                currentSortColumn = sortColumn;
                currentSortOrder = 'desc';
            }
            
            // Update visual indicators
            updateSortIndicators();
            
            // Reset to first page and reload
            currentPage = 1;
            loadThreads();
        });
    });
}

// Load forum types for dropdown
async function loadForumTypes() {
    try {
        const response = await fetch('/api/forum-types');
        const data = await response.json();
        
        const select = document.getElementById('forumType');
        select.innerHTML = '<option value="">所有版块</option>';
        
        if (data.forum_types && data.forum_types.length > 0) {
            forumTypeOptions = [{ value: '', text: '所有版块' }, ...data.forum_types.map(forum => ({
                value: forum.name,
                text: `${forum.name} (${forum.count})`
            }))];
            data.forum_types.forEach(forum => {
                const option = document.createElement('option');
                option.value = forum.name;
                option.textContent = `${forum.name} (${forum.count})`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading forum types:', error);
    }
}

// Load section types for dropdown
async function loadSectionTypes(forumTypeFilter = '') {
    try {
        let url = '/api/section-types';
        if (forumTypeFilter) {
            url += `?forum_type=${encodeURIComponent(forumTypeFilter)}`;
        }
        
        const response = await fetch(url);
        const data = await response.json();
        
        const select = document.getElementById('sectionType');
        select.innerHTML = '<option value="">所有分区</option>';
        
        if (data.section_types && data.section_types.length > 0) {
            sectionTypeOptions = [{ value: '', text: '所有分区', forum_type: '' }, ...data.section_types.map(section => ({
                value: section.value,
                text: `${section.label} (${section.count})`,
                forum_type: section.forum_type || ''
            }))];
            data.section_types.forEach(section => {
                const option = document.createElement('option');
                option.value = section.value;
                option.textContent = `${section.label} (${section.count})`;
                select.appendChild(option);
            });
            
            // Set default to "卡通贴图" if available
            const cartoonSection = data.section_types.find(section => section.label.includes('卡通贴图'));
            if (cartoonSection) {
                select.value = cartoonSection.value;
                document.getElementById('sectionTypeSearch').value = `${cartoonSection.label} (${cartoonSection.count})`;
                // Trigger search with default selection
                currentPage = 1;
                loadThreads();
            }
        }
    } catch (error) {
        console.error('Error loading section types:', error);
    }
}


// Load threads with filters
async function loadThreads() {
    showLoading(true);
    
    const params = new URLSearchParams();
    
    // Add filters
    const forumType = document.getElementById('forumType').value;
    if (forumType) params.append('forum_type', forumType);
    
    const sectionType = document.getElementById('sectionType').value;
    if (sectionType) params.append('section_id', sectionType);
    
    
    const nameSearch = document.getElementById('nameSearch').value;
    if (nameSearch) params.append('name', nameSearch);
    
    // Use current sort state (from column clicks or default)
    if (currentSortColumn) {
        params.append('order_by', currentSortColumn);
        params.append('order', currentSortOrder);
    }
    
    // Add first upload threshold if set
    const createTimeThreshold = document.getElementById('createTimeThreshold').value;
    if (createTimeThreshold) {
        params.append('first_upload_after', createTimeThreshold);
    }
    
    const pageSize = document.getElementById('pageSize').value;
    params.append('page_size', pageSize);
    params.append('page', currentPage);
    
    try {
        const response = await fetch('/api/threads?' + params.toString());
        const data = await response.json();
        
        if (data.error) {
            showError(data.error);
            return;
        }
        
        displayThreads(data.threads || []);
        updateResultInfo(data.total || 0, data.page || 1, data.page_size || 50);
        updatePagination(data.total || 0, data.page || 1, data.page_size || 50);
        updateSortIndicators();
        
    } catch (error) {
        console.error('Error loading threads:', error);
        showError('加载主题失败');
    } finally {
        showLoading(false);
    }
}

// Display threads in table
function displayThreads(threads) {
    const tbody = document.getElementById('tableBody');
    
    // No longer need hardcoded section mapping - using data from backend
    
    if (!threads || threads.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="px-6 py-4 text-center text-gray-500">
                    没有找到主题
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = threads.map(thread => {
        const sectionLabel = thread.section_name || (thread.section_id ? `Section ${thread.section_id}` : '-');
        const forumLabel = thread.forum_type || '-';
        
        return `
        <tr class="table-row hover:bg-gradient-to-r hover:from-blue-50 hover:to-sky-50 transition-all duration-300">
            <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="inline-block px-2 py-1 text-xs font-normal rounded-full bg-gradient-to-r from-blue-200 to-sky-300 text-gray-800 shadow-sm">
                    ${escapeHtml(forumLabel)}
                </span>
            </td>
            <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="inline-block px-2 py-1 text-xs font-normal rounded-full bg-gradient-to-r from-emerald-200 to-teal-300 text-gray-800 shadow-sm">
                    ${escapeHtml(sectionLabel)}
                </span>
            </td>
            <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs font-normal rounded-full bg-gradient-to-r from-pink-200 to-rose-300 text-gray-800 shadow-sm">
                    ${escapeHtml(thread.thread_type)}
                </span>
            </td>
            <td class="px-4 py-4 text-sm text-gray-900 min-w-0">
                <a href="${escapeHtml(thread.full_link)}" target="_blank" class="text-blue-600 hover:text-sky-700 hover:underline cursor-pointer font-medium transition-colors duration-200 block truncate" title="${escapeHtml(thread.name)}">
                    ${escapeHtml(thread.name)}
                </a>
            </td>
            <td class="px-2 py-4 whitespace-nowrap text-center text-sm">
                <button onclick="searchOnGoogle('${escapeHtml(thread.name).replace(/'/g, "\\'")}')" 
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-gradient-to-r from-blue-400 to-sky-400 rounded-md hover:from-blue-500 hover:to-sky-500 transition-all duration-200 shadow-sm hover:shadow-md"
                        title="在 Google 搜索">
                    🔍
                </button>
            </td>
            <td class="px-1 py-4 whitespace-nowrap text-center text-sm font-semibold text-gray-700">${thread.total_reply.toLocaleString()}</td>
            <td class="px-1 py-4 whitespace-nowrap text-center text-sm font-semibold text-gray-700">${thread.total_click.toLocaleString()}</td>
            <td class="px-1 py-4 whitespace-nowrap text-center text-sm font-semibold text-gray-700">${thread.thumbs.toLocaleString()}</td>
            <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDateOnly(thread.first_upload_time)}
            </td>
            <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDate(thread.update_time)}
            </td>
        </tr>
    `}).join('');
}

// Update result info
function updateResultInfo(total, page, pageSize) {
    const start = (page - 1) * pageSize + 1;
    const end = Math.min(page * pageSize, total);
    
    // Update page size selector with result info
    const pageSizeSelect = document.getElementById('pageSize');
    const currentValue = pageSizeSelect.value;
    
    if (total > 0) {
        const resultText = `显示 ${start}-${end}，共 ${total.toLocaleString()} 条`;
        pageSizeSelect.innerHTML = `
            <option value="25" ${currentValue === '25' ? 'selected' : ''}>${resultText} (25条/页)</option>
            <option value="50" ${currentValue === '50' ? 'selected' : ''}>${resultText} (50条/页)</option>
            <option value="100" ${currentValue === '100' ? 'selected' : ''}>${resultText} (100条/页)</option>
        `;
    } else {
        pageSizeSelect.innerHTML = `
            <option value="25" ${currentValue === '25' ? 'selected' : ''}>没有找到结果 (25条/页)</option>
            <option value="50" ${currentValue === '50' ? 'selected' : ''}>没有找到结果 (50条/页)</option>
            <option value="100" ${currentValue === '100' ? 'selected' : ''}>没有找到结果 (100条/页)</option>
        `;
    }
    
    totalRecords = total;
}

// Update pagination
function updatePagination(total, page, pageSize) {
    totalPages = Math.ceil(total / pageSize);
    currentPage = page;
    
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // Previous button
    if (currentPage > 1) {
        html += `<button onclick="goToPage(${currentPage - 1})" class="px-3 py-2 h-10 bg-gradient-to-r from-blue-500 to-sky-500 text-white border border-transparent rounded-lg hover:from-blue-600 hover:to-sky-600 transition-all duration-200 shadow-md hover:shadow-lg text-sm font-medium">上一页</button>`;
    }
    
    // Page numbers
    const maxButtons = 7;
    let startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxButtons - 1);
    
    if (endPage - startPage < maxButtons - 1) {
        startPage = Math.max(1, endPage - maxButtons + 1);
    }
    
    if (startPage > 1) {
        html += `<button onclick="goToPage(1)" class="px-3 py-2 h-10 bg-white/90 backdrop-blur-sm border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 text-blue-600 font-medium text-sm">1</button>`;
        if (startPage > 2) {
            html += `<span class="px-2 text-blue-400 font-medium text-sm h-10 flex items-center">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            html += `<button class="px-3 py-2 h-10 bg-gradient-to-r from-blue-600 to-sky-500 text-white rounded-lg shadow-lg font-medium text-sm">${i}</button>`;
        } else {
            html += `<button onclick="goToPage(${i})" class="px-3 py-2 h-10 bg-white/90 backdrop-blur-sm border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 text-blue-600 font-medium text-sm">${i}</button>`;
        }
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<span class="px-2 text-blue-400 font-medium text-sm h-10 flex items-center">...</span>`;
        }
        html += `<button onclick="goToPage(${totalPages})" class="px-3 py-2 h-10 bg-white/90 backdrop-blur-sm border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 text-blue-600 font-medium text-sm">${totalPages}</button>`;
    }
    
    // Next button
    if (currentPage < totalPages) {
        html += `<button onclick="goToPage(${currentPage + 1})" class="px-3 py-2 h-10 bg-gradient-to-r from-blue-500 to-sky-500 text-white border border-transparent rounded-lg hover:from-blue-600 hover:to-sky-600 transition-all duration-200 shadow-md hover:shadow-lg text-sm font-medium">下一页</button>`;
    }
    
    pagination.innerHTML = html;
}

// Go to specific page
function goToPage(page) {
    currentPage = page;
    loadThreads();
    
    // Scroll to top on mobile after page change
    if (isMobile()) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// Show/hide loading indicator
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (show) {
        loading.classList.remove('hidden');
        // Prevent scrolling while loading on mobile
        if (isMobile()) {
            document.body.style.overflow = 'hidden';
        }
    } else {
        loading.classList.add('hidden');
        // Re-enable scrolling
        if (isMobile()) {
            document.body.style.overflow = '';
        }
    }
}

// Show error message
function showError(message) {
    const tbody = document.getElementById('tableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="10" class="px-6 py-4 text-center text-red-600">
                错误: ${escapeHtml(message)}
            </td>
        </tr>
    `;
}

// Format date to YYYY-MM-DD HH:mm:ss format from Unix timestamp (seconds)
function formatDate(timestamp) {
    if (!timestamp) return '';
    
    // Convert timestamp (seconds) to Date object
    const date = new Date(timestamp * 1000);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// Format date to YYYY-MM-DD format (date only) from Unix timestamp (seconds)
function formatDateOnly(timestamp) {
    if (!timestamp) return '';
    
    // Convert timestamp (seconds) to Date object
    const date = new Date(timestamp * 1000);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.toString().replace(/[&<>"']/g, m => map[m]);
}

// Search on Google with site filter
function searchOnGoogle(title) {
    // Extract clean title by removing content within brackets
    let cleanTitle = title;
    
    // Remove content within various bracket types and the brackets themselves
    // Includes: [], {}, (), （）, 【】, 〔〕, ［］, ｛｝, ＜＞, 《》, 「」, 『』
    cleanTitle = cleanTitle.replace(/\[[^\]]*\]/g, ''); // []
    cleanTitle = cleanTitle.replace(/\{[^}]*\}/g, '');  // {}
    cleanTitle = cleanTitle.replace(/\([^)]*\)/g, '');  // ()
    cleanTitle = cleanTitle.replace(/（[^）]*）/g, ''); // （）
    cleanTitle = cleanTitle.replace(/【[^】]*】/g, ''); // 【】
    cleanTitle = cleanTitle.replace(/〔[^〕]*〕/g, ''); // 〔〕
    cleanTitle = cleanTitle.replace(/［[^］]*］/g, ''); // ［］
    cleanTitle = cleanTitle.replace(/｛[^｝]*｝/g, ''); // ｛｝
    cleanTitle = cleanTitle.replace(/＜[^＞]*＞/g, ''); // ＜＞
    cleanTitle = cleanTitle.replace(/《[^》]*》/g, ''); // 《》
    cleanTitle = cleanTitle.replace(/「[^」]*」/g, ''); // 「」
    cleanTitle = cleanTitle.replace(/『[^』]*』/g, ''); // 『』
    
    // Trim whitespace
    cleanTitle = cleanTitle.trim();
    
    // If cleaned title is empty, use original title
    if (!cleanTitle) {
        cleanTitle = title;
    }
    
    const searchQuery = encodeURIComponent(`${cleanTitle} site:wnacg.com`);
    const googleSearchUrl = `https://www.google.com/search?q=${searchQuery}`;
    window.open(googleSearchUrl, '_blank');
}

// Update sort indicators on column headers
function updateSortIndicators() {
    // Clear all indicators
    document.querySelectorAll('.sortable-header').forEach(header => {
        header.classList.remove('text-blue-600', 'font-semibold');
    });
    
    // Set active indicator
    const activeHeader = document.querySelector(`[data-sort="${currentSortColumn}"]`);
    if (activeHeader) {
        activeHeader.classList.add('text-blue-600', 'font-semibold');
    }
}

// Setup modal selects
function setupSearchableSelects() {
    setupModalSelect('forumType', '版块');
    setupModalSelect('sectionType', '分区');
}

// Setup a single modal select
function setupModalSelect(selectType, title) {
    const searchInput = document.getElementById(`${selectType}Search`);
    
    // Handle input click to open modal
    searchInput.addEventListener('click', function() {
        openModalSelect(selectType, title);
    });
}

// Modal select variables
let currentModalType = '';
let currentModalTitle = '';
let currentModalOptions = [];
let selectedModalValue = '';

// Open modal select dialog
function openModalSelect(selectType, title) {
    currentModalType = selectType;
    currentModalTitle = title;
    
    // Get options based on select type
    if (selectType === 'forumType') {
        currentModalOptions = forumTypeOptions || [];
    } else if (selectType === 'sectionType') {
        currentModalOptions = sectionTypeOptions || [];
    }
    
    // Get current selected value
    selectedModalValue = document.getElementById(selectType).value;
    
    // Get current selected text for header display
    const currentOption = currentModalOptions.find(option => option.value == selectedModalValue);
    const currentText = currentOption ? currentOption.text : '';
    
    // Set modal title with current selection
    const titleElement = document.getElementById('modalTitle');
    if (currentText && currentText !== '所有版块' && currentText !== '所有分区') {
        titleElement.innerHTML = `选择${title} <span class="text-sm font-normal text-blue-600 ml-2">(当前: ${escapeHtml(currentText)})</span>`;
    } else {
        titleElement.textContent = `选择${title}`;
    }
    
    // Clear search input
    document.getElementById('modalSearchInput').value = '';
    
    // Populate options
    updateModalOptions();
    
    // Show modal
    document.getElementById('modalSelectDialog').classList.remove('hidden');
    
    // Focus search input
    setTimeout(() => {
        document.getElementById('modalSearchInput').focus();
    }, 100);
}

// Update modal options display
function updateModalOptions() {
    const searchTerm = document.getElementById('modalSearchInput').value.toLowerCase();
    const optionsGrid = document.getElementById('modalOptionsGrid');
    
    // Filter options based on search term
    const filteredOptions = currentModalOptions.filter(option => 
        option.text.toLowerCase().includes(searchTerm)
    );
    
    if (currentModalType === 'sectionType') {
        // Group sections by forum type
        const groupedSections = {};
        filteredOptions.forEach(option => {
            const forumType = option.forum_type;
            if (forumType && forumType !== '') { // Only include items with valid forum_type
                if (!groupedSections[forumType]) {
                    groupedSections[forumType] = [];
                }
                groupedSections[forumType].push(option);
            }
        });
        
        // Custom sort order for forums
        const forumOrder = ['美图', '信息', '文学', '成人BT', '资源共享', '在线视频'];
        const sortedForumTypes = Object.keys(groupedSections).sort((a, b) => {
            const indexA = forumOrder.indexOf(a);
            const indexB = forumOrder.indexOf(b);
            
            // If both are in the priority list, sort by priority
            if (indexA !== -1 && indexB !== -1) {
                return indexA - indexB;
            }
            // If only A is in priority list, A comes first
            if (indexA !== -1) return -1;
            // If only B is in priority list, B comes first
            if (indexB !== -1) return 1;
            // Otherwise, alphabetical sort
            return a.localeCompare(b);
        });
        
        // Generate grouped HTML
        let html = '';
        sortedForumTypes.forEach(forumType => {
            
            html += `<div class="col-span-full mb-4">
                <h4 class="text-lg font-semibold text-gray-700 mb-3 border-b border-gray-200 pb-2">${escapeHtml(forumType)}</h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    ${groupedSections[forumType].map(option => `
                        <div class="modal-option-card ${option.value == selectedModalValue ? 'selected' : ''}" 
                             data-value="${option.value}">
                            ${escapeHtml(option.text)}
                        </div>
                    `).join('')}
                </div>
            </div>`;
        });
        
        // Add "所有分区" option at the beginning if it matches search
        const allOption = filteredOptions.find(option => option.value === '');
        if (allOption) {
            html = `<div class="col-span-full mb-4">
                <div class="modal-option-card ${allOption.value == selectedModalValue ? 'selected' : ''}" 
                     data-value="${allOption.value}">
                    ${escapeHtml(allOption.text)}
                </div>
            </div>` + html;
        }
        
        optionsGrid.innerHTML = html;
    } else {
        // Regular grid for forum types
        optionsGrid.innerHTML = filteredOptions.map(option => `
            <div class="modal-option-card ${option.value == selectedModalValue ? 'selected' : ''}" 
                 data-value="${option.value}">
                ${escapeHtml(option.text)}
            </div>
        `).join('');
    }
    
    // Add click handlers to option cards
    optionsGrid.querySelectorAll('.modal-option-card').forEach(card => {
        card.addEventListener('click', function() {
            // Remove previous selection
            optionsGrid.querySelectorAll('.modal-option-card').forEach(c => c.classList.remove('selected'));
            
            // Add selection to clicked card
            this.classList.add('selected');
            selectedModalValue = this.getAttribute('data-value');
            
            // Auto-confirm selection (direct search)
            confirmModalSelection();
        });
    });
}

// Close modal select dialog
function closeModalSelect() {
    document.getElementById('modalSelectDialog').classList.add('hidden');
    currentModalType = '';
    currentModalTitle = '';
    currentModalOptions = [];
    selectedModalValue = '';
}

// Confirm modal selection
function confirmModalSelection() {
    if (!currentModalType) return;
    
    // Find selected option - use == instead of === to handle string/number comparison
    const selectedOption = currentModalOptions.find(option => option.value == selectedModalValue);
    const displayText = selectedOption ? selectedOption.text : '';
    
    // Update hidden select
    document.getElementById(currentModalType).value = selectedModalValue;
    
    // Update search input display
    const searchInput = document.getElementById(`${currentModalType}Search`);
    searchInput.value = displayText;
    
    // Trigger change event
    if (currentModalType === 'forumType') {
        // Reset section selection and reload sections based on forum
        document.getElementById('sectionType').value = '';
        document.getElementById('sectionTypeSearch').value = '';
        loadSectionTypes(selectedModalValue);
    }
    
    // Reload threads
    currentPage = 1;
    loadThreads();
    
    // Close modal
    closeModalSelect();
}

// Setup modal event listeners
function setupModalEventListeners() {
    // Close modal button
    document.getElementById('closeModalBtn').addEventListener('click', closeModalSelect);
    
    // Cancel button
    document.getElementById('modalCancelBtn').addEventListener('click', closeModalSelect);
    
    // Confirm button
    document.getElementById('modalConfirmBtn').addEventListener('click', confirmModalSelection);
    
    // Modal search input
    document.getElementById('modalSearchInput').addEventListener('input', updateModalOptions);
    
    // Close modal when clicking background
    document.getElementById('modalSelectDialog').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModalSelect();
        }
    });
    
    // Handle ESC key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !document.getElementById('modalSelectDialog').classList.contains('hidden')) {
            closeModalSelect();
        }
    });
}

// Setup keyboard navigation
function setupKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Only handle keyboard navigation if no input is focused
        if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'SELECT') {
            return;
        }
        
        // Ignore keyboard shortcuts when modifier keys are pressed
        if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) {
            return;
        }
        
        switch(e.key) {
            case 'ArrowLeft':
            case 'a':
            case 'A':
                e.preventDefault();
                if (currentPage > 1) {
                    goToPage(currentPage - 1);
                }
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                e.preventDefault();
                if (currentPage < totalPages) {
                    goToPage(currentPage + 1);
                }
                break;
            case '1':
                e.preventDefault();
                document.querySelector('[data-sort="total_reply"]').click();
                break;
            case '2':
                e.preventDefault();
                document.querySelector('[data-sort="total_click"]').click();
                break;
            case '3':
                e.preventDefault();
                document.querySelector('[data-sort="thumbs"]').click();
                break;
            case '4':
                e.preventDefault();
                document.querySelector('[data-sort="first_upload_time"]').click();
                break;
            case '5':
                e.preventDefault();
                document.querySelector('[data-sort="update_time"]').click();
                break;
        }
    });
}

// Setup clear buttons with touch support
function setupClearButtons() {
    // Add visual feedback for touch
    const addTouchFeedback = (button) => {
        if (!button) return;
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        }, { passive: true });
        
        button.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        }, { passive: true });
    };
    
    // Clear forum type
    const clearForumBtn = document.getElementById('clearForumType');
    addTouchFeedback(clearForumBtn);
    if (clearForumBtn) {
        clearForumBtn.addEventListener('click', function() {
        document.getElementById('forumTypeSearch').value = '';
        document.getElementById('forumType').value = '';
        document.getElementById('forumTypeDropdown').classList.add('hidden');
        
        // Reset section selection and reload sections
        document.getElementById('sectionType').value = '';
        document.getElementById('sectionTypeSearch').value = '';
        loadSectionTypes('');
        
        currentPage = 1;
        loadThreads();
        });
    }

    // Clear section type
    const clearSectionBtn = document.getElementById('clearSectionType');
    addTouchFeedback(clearSectionBtn);
    if (clearSectionBtn) {
        clearSectionBtn.addEventListener('click', function() {
        document.getElementById('sectionTypeSearch').value = '';
        document.getElementById('sectionType').value = '';
        document.getElementById('sectionTypeDropdown').classList.add('hidden');
        
        currentPage = 1;
        loadThreads();
        });
    }

    // Clear name search
    const clearNameBtn = document.getElementById('clearNameSearch');
    addTouchFeedback(clearNameBtn);
    if (clearNameBtn) {
        clearNameBtn.addEventListener('click', function() {
        document.getElementById('nameSearch').value = '';
        
        currentPage = 1;
        loadThreads();
        });
    }
}

// Setup time shortcuts with touch support
function setupTimeShortcuts() {
    document.querySelectorAll('.time-shortcut').forEach(button => {
        button.addEventListener('click', function() {
            const months = parseInt(this.getAttribute('data-months'));
            const date = new Date();
            date.setMonth(date.getMonth() - months);
            
            document.getElementById('createTimeThreshold').value = date.toISOString().split('T')[0];
            
            // Highlight selected button
            document.querySelectorAll('.time-shortcut').forEach(btn => {
                btn.classList.remove('bg-blue-200', 'font-semibold');
                btn.classList.add('bg-blue-100');
            });
            this.classList.remove('bg-blue-100');
            this.classList.add('bg-blue-200', 'font-semibold');
            
            currentPage = 1;
            loadThreads();
        });
    });
}
