/* Custom styles for Tiver Web - Modern Colorful Design */

/* iOS Safe Area Support */
:root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}

/* Prevent iOS text size adjustment */
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

/* iOS Touch Optimization */
* {
    -webkit-tap-highlight-color: rgba(96, 165, 250, 0.2);
    -webkit-touch-callout: none;
}

/* Smooth scrolling for iOS */
.overflow-y-auto,
.overflow-x-auto,
.overflow-auto {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Prevent iOS input zoom */
input[type="text"],
input[type="password"],
input[type="date"],
select,
textarea {
    font-size: 16px !important; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* iOS safe area padding */
body {
    padding-top: var(--safe-area-inset-top);
    padding-left: var(--safe-area-inset-left);
    padding-right: var(--safe-area-inset-right);
    padding-bottom: var(--safe-area-inset-bottom);
}

/* Modern Gradient Header */
.header-gradient {
    background: linear-gradient(135deg, 
        rgba(147, 51, 234, 0.1) 0%, 
        rgba(59, 130, 246, 0.1) 35%, 
        rgba(16, 185, 129, 0.1) 70%, 
        rgba(236, 72, 153, 0.1) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.header-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Modern Filter Card */
.filter-card {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: visible;
    z-index: 10000;
}

/* Enhanced Search Button */
.search-btn {
    background: linear-gradient(135deg, #60a5fa 0%, #38bdf8 100%);
    position: relative;
    overflow: hidden;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.search-btn:hover::before {
    left: 100%;
}

/* Modern Table Styling */
.modern-table {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(255, 255, 255, 0.85) 100%);
    backdrop-filter: blur(15px);
    position: relative;
    z-index: 1;
}

/* Enhanced Table Rows */
tbody tr {
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.5);
}

tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

tbody tr:hover {
    background: linear-gradient(135deg, 
        rgba(96, 165, 250, 0.08) 0%, 
        rgba(56, 189, 248, 0.08) 50%, 
        rgba(125, 211, 252, 0.08) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Colorful Loading Spinner */
.colorful-spinner {
    width: 60px;
    height: 60px;
    border: 6px solid transparent;
    border-top: 6px solid #60a5fa;
    border-right: 6px solid #38bdf8;
    border-bottom: 6px solid #7dd3fc;
    border-left: 6px solid #93c5fd;
    border-radius: 50%;
    animation: rainbow-spin 1.5s linear infinite;
}

@keyframes rainbow-spin {
    0% { transform: rotate(0deg); }
    25% { border-top-color: #38bdf8; border-right-color: #7dd3fc; border-bottom-color: #93c5fd; border-left-color: #60a5fa; }
    50% { border-top-color: #7dd3fc; border-right-color: #93c5fd; border-bottom-color: #60a5fa; border-left-color: #38bdf8; }
    75% { border-top-color: #93c5fd; border-right-color: #60a5fa; border-bottom-color: #38bdf8; border-left-color: #7dd3fc; }
    100% { transform: rotate(360deg); }
}

/* Modern Scrollbar Styles */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #93c5fd 0%, #7dd3fc 100%);
    border-radius: 6px;
    border: 2px solid #f8fafc;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #60a5fa 0%, #38bdf8 100%);
}

::-webkit-scrollbar-corner {
    background: #f8fafc;
}

/* Enhanced Input Styles */
input[type="text"], input[type="date"], select {
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
}

input[type="text"]:focus, input[type="date"]:focus, select:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

/* Enhanced Labels */
label {
    color: #3b82f6;
    font-weight: 600;
}

/* Touch-friendly button sizes */
button, .btn, [role="button"] {
    min-height: 44px; /* Apple HIG recommendation */
    min-width: 44px;
    touch-action: manipulation; /* Prevent double-tap zoom */
}

/* Clear Button Styles */
.clear-btn {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
    z-index: 10;
    opacity: 0.7;
}

.clear-btn:hover {
    opacity: 1;
    background-color: rgba(239, 68, 68, 0.1);
}

/* Loading animation */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Sortable headers */
.sortable-header {
    user-select: none;
    transition: background-color 0.2s ease;
    position: relative;
}

.sortable-header:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.sortable-header.text-blue-600 {
    color: #1f2937 !important;
    background-color: rgba(255, 255, 255, 0.3) !important;
}

.sort-indicator {
    font-size: 14px;
    display: inline-block;
    min-width: 12px;
    color: #fbbf24;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    line-height: 1;
}

/* Modal Select Styles */
.modal-select-container {
    position: relative;
}

/* Modal Dialog Styles */
.modal-dialog {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-option-card {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%, 
        rgba(248, 250, 252, 0.9) 100%);
    text-align: center;
    font-size: 14px;
    line-height: 1.4;
}

.modal-option-card:hover {
    border-color: #60a5fa;
    background: linear-gradient(135deg, 
        rgba(96, 165, 250, 0.1) 0%, 
        rgba(56, 189, 248, 0.1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
}

.modal-option-card.selected {
    border-color: #60a5fa;
    border-width: 3px;
    background: linear-gradient(135deg, 
        rgba(96, 165, 250, 0.15) 0%, 
        rgba(56, 189, 248, 0.15) 100%);
    color: #3b82f6;
    font-weight: 700;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 20px rgba(96, 165, 250, 0.3);
    position: relative;
}

.modal-option-card.selected::before {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 6px;
    color: #60a5fa;
    font-weight: bold;
    font-size: 16px;
}

.modal-option-card.hidden {
    display: none;
}

/* Mobile-specific styles for iOS */
@supports (-webkit-touch-callout: none) {
    /* iOS only styles */
    select {
        background-image: url('data:image/svg+xml;utf8,<svg fill="%23333" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
        background-repeat: no-repeat;
        background-position: right 8px center;
        background-size: 20px;
        padding-right: 30px;
    }
    
    /* Fixed positioning adjustments for iOS */
    .fixed {
        position: fixed;
        transform: translateZ(0); /* Hardware acceleration */
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Header adjustments */
    h1 {
        font-size: 1.75rem !important;
    }
    
    /* Filter card mobile layout */
    .filter-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .filter-card .flex {
        flex-direction: column;
    }
    
    .filter-card .flex > div {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 0.75rem;
    }
    
    /* Table responsive scroll */
    .modern-table {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    table {
        font-size: 12px;
        min-width: 800px; /* Force horizontal scroll on small screens */
    }
    
    th, td {
        padding: 6px 8px !important;
        white-space: nowrap;
    }
    
    /* Hide less important columns on mobile */
    th:nth-child(1), td:nth-child(1), /* Forum */
    th:nth-child(2), td:nth-child(2) { /* Section */
        display: none;
    }
    
    /* Touch-friendly buttons */
    .search-btn {
        font-size: 1rem;
        padding: 0.875rem 1.5rem;
        width: 100%;
        min-height: 48px;
    }
    
    /* Pagination mobile optimization */
    #pagination {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }
    
    #pagination button {
        min-width: 44px;
        min-height: 44px;
        padding: 0.625rem 0.875rem;
        font-size: 0.875rem;
    }
    
    /* Page size selector mobile */
    #pageSize {
        width: auto;
        min-width: 120px;
        min-height: 44px;
    }
    
    /* Modal optimizations */
    .modal-dialog {
        margin: 0.5rem;
        max-height: 90vh;
    }
    
    .modal-body {
        max-height: 60vh;
    }
    
    #modalOptionsGrid {
        grid-template-columns: 1fr 1fr;
    }
    
    .modal-option-card {
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Login modal mobile */
    #loginModal .max-w-md {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    /* Time shortcuts mobile layout */
    .time-shortcut {
        min-width: 44px;
        min-height: 36px;
        padding: 0.5rem;
    }
}

/* Enhanced Focus States */
input:focus, select:focus, button:focus {
    outline: none;
    ring: 4px;
    ring-color: rgba(96, 165, 250, 0.3);
}

/* Gradient Text Animation */
@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.header-gradient h1 {
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
}

/* Card Hover Effects */
.filter-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.1);
}

/* Table Row Animations */
.table-row {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-row:hover {
    transform: scale(1.01);
}

/* Login Modal Styles */
#loginModal input[type="text"],
#loginModal input[type="password"] {
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    background: rgba(255, 255, 255, 0.95);
}

#loginModal input[type="text"]:focus,
#loginModal input[type="password"]:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.1);
    background: rgba(255, 255, 255, 1);
}

#loginModal button[type="submit"] {
    background: linear-gradient(135deg, #60a5fa 0%, #38bdf8 100%);
    position: relative;
    overflow: hidden;
}

#loginModal button[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

#loginModal button[type="submit"]:hover::before {
    left: 100%;
}