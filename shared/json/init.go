package json

import (
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"unsafe"
)

// MarshalString marshal
func MarshalString(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return byteSliceToString(bytes), nil
}

// Marshal marshal x
func Marshal(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

// Unmarshal unmarshal x
func Unmarshal(data []byte, v interface{}) error {
	if err := json.Unmarshal(data, v); err != nil {
		return err
	}
	if err := innerValidator.ValidateStruct(v); err != nil {
		return err
	}
	return nil
}

// UnmarshalString unmarshal x
func UnmarshalString(str string, v interface{}) error {
	if err := json.Unmarshal(stringToByteSlice(str), v); err != nil {
		return err
	}
	if err := innerValidator.ValidateStruct(v); err != nil {
		errs := err.(validator.ValidationErrors) // nolint: errcheck
		return fmt.Errorf("%v", errs.Translate(trans))
	}
	return nil
}

func byteSliceToString(bytes []byte) string {
	return *(*string)(unsafe.Pointer(&bytes))
}

func stringToByteSlice(s string) []byte {
	tmp1 := (*[2]uintptr)(unsafe.Pointer(&s))
	tmp2 := [3]uintptr{tmp1[0], tmp1[1], tmp1[1]}
	return *(*[]byte)(unsafe.Pointer(&tmp2))
}
