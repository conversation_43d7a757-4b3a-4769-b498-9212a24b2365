package json

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMarshal(t *testing.T) {
	var x struct {
		A string
		B int
	}
	x.B = 1
	x.A = "2"

	str, err := MarshalString(&x)
	assert.Nil(t, err)
	assert.Equal(t, "{\"A\":\"2\",\"B\":1}", str)
}
func TestUnmarshal(t *testing.T) {
	type xx struct {
		A string
		B int
	}
	x1 := xx{A: "2", B: 1}
	x2 := xx{}

	obj := "{\"A\":\"2\",\"B\":1}"

	err := UnmarshalString(obj, &x2)
	assert.Nil(t, err)
	assert.Equal(t, x1, x2)

}

func TestUnmarshalValidator(t *testing.T) {
	type xx struct {
		A string `json:"a" binding:""`
		B int    `json:"b" binding:"min=1"`
	}
	x1 := xx{A: "2", B: -2}
	x2 := xx{}
	x3 := xx{}

	obj := "{\"A\":\"2\",\"B\":-2}"

	err := UnmarshalString(obj, &x2)
	assert.Equal(t, "map[xx.B:B最小只能为1]", err.Error())
	assert.Equal(t, x1, x2)

	obj = "{\"A\":\"2\",\"B\":2}"
	err = UnmarshalString(obj, &x3)
	assert.Nil(t, err)
	x1.B = 2
	assert.Equal(t, x1, x3)
}
