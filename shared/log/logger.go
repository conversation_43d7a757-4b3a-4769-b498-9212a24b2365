package log

import (
	"fmt"
	"os"
	"time"

	"github.com/slack-go/slack"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var Logger *zap.Logger
var WebhookLogger *zap.Logger

func WebhookSugarLogger() *zap.SugaredLogger {
	return WebhookLogger.Sugar()
}

type LoggerOption struct {
	LogLevel    string
	Writers     []zapcore.WriteSyncer
	EncodeLevel zapcore.LevelEncoder
}

func (i LoggerOption) GetLogLevel() string {
	return i.LogLevel
}

func (i LoggerOption) GetWriters() []zapcore.WriteSyncer {
	return i.Writers
}

func (i LoggerOption) GetEncodeLevel() zapcore.LevelEncoder {
	return i.EncodeLevel
}

func InitLogger(option LoggerOption) {
	Logger, _ = zap.NewProduction()

	encodeLevel := zapcore.CapitalColorLevelEncoder
	if option.GetEncodeLevel() != nil {
		encodeLevel = option.GetEncodeLevel()
	}

	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		CallerKey:      "line",
		NameKey:        "hydra",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    encodeLevel,                    // 小写编码器
		EncodeTime:     TimeEncoder,                    // 自定义时间格式
		EncodeDuration: zapcore.SecondsDurationEncoder, //
		EncodeCaller:   zapcore.ShortCallerEncoder,     // 相对路径编码器
		EncodeName:     zapcore.FullNameEncoder,
	}

	level, err := zapcore.ParseLevel(option.GetLogLevel())
	if err != nil {
		fmt.Println("日志级别解析失败，默认设置为info")
		level = zapcore.InfoLevel
	}

	encoder := zapcore.NewConsoleEncoder(encoderConfig)
	cores := make([]zapcore.Core, 0)
	atomicLevel := zap.NewAtomicLevel()
	atomicLevel.SetLevel(level)

	ws := getWriteSyncer()
	ws = append(ws, option.GetWriters()...)

	cores = append(cores,
		zapcore.NewCore(
			encoder,                            // 编码器配置
			zapcore.NewMultiWriteSyncer(ws...), // 打印到控制台或文件或其他平台
			atomicLevel,                        // 日志级别
		),
	)

	core := zapcore.NewTee(cores...)
	// 开启开发模式，堆栈跟踪
	caller := zap.AddCaller()
	// 开启文件及行号
	development := zap.Development()
	// 设置初始化字段
	filed := zap.Fields()
	// 构造日志
	Logger = zap.New(core, caller, development, filed)
}

func InitWebhookLogger(logLevel string, writers ...zapcore.WriteSyncer) {
	WebhookLogger, _ = zap.NewProduction()

	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		CallerKey:      "line",
		NameKey:        "webhook",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalColorLevelEncoder, // 小写编码器
		EncodeTime:     TimeEncoder,                      // 自定义时间格式
		EncodeDuration: zapcore.SecondsDurationEncoder,   //
		EncodeCaller:   zapcore.ShortCallerEncoder,       // 相对路径编码器
		EncodeName:     zapcore.FullNameEncoder,
	}

	level, err := zapcore.ParseLevel(logLevel)
	if err != nil {
		fmt.Println("日志级别解析失败，默认设置为info")
		level = zapcore.InfoLevel
	}

	encoder := zapcore.NewConsoleEncoder(encoderConfig)
	cores := make([]zapcore.Core, 0)
	atomicLevel := zap.NewAtomicLevel()
	atomicLevel.SetLevel(level)

	ws := getWriteSyncer()
	if len(writers) > 0 {
		ws = append(ws, writers...)
	}

	cores = append(cores,
		zapcore.NewCore(
			encoder,                            // 编码器配置
			zapcore.NewMultiWriteSyncer(ws...), // 打印到控制台或文件或其他平台
			atomicLevel,                        // 日志级别
		),
	)

	core := zapcore.NewTee(cores...)
	// 开启开发模式，堆栈跟踪
	caller := zap.AddCaller()
	// 开启文件及行号
	development := zap.Development()
	// 设置初始化字段
	filed := zap.Fields()
	// 构造日志
	WebhookLogger = zap.New(core, caller, development, filed)
}

// TimeEncoder 序列化时间
func TimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
}

func getWriteSyncer() []zapcore.WriteSyncer {
	var items []zapcore.WriteSyncer
	//hook := lumberjack.Logger{
	//	Filename:   "hydra.log", // 日志文件路径
	//	MaxSize:    100,         // 每个日志文件保存的最大尺寸 单位：M
	//	MaxBackups: 100,         // 日志文件最多保存多少个备份
	//	MaxAge:     100,         // 文件最多保存多少天
	//	Compress:   true,        // 是否压缩
	//}
	//items = append(items, zapcore.AddSync(&hook))
	items = append(items, zapcore.AddSync(os.Stdout))
	return items
}

type SlackWriter struct {
	ChannelId string
	Client    *socketmode.Client
}

func (hook *SlackWriter) Write(p []byte) (n int, err error) {
	_, _, err = hook.Client.PostMessage(hook.ChannelId, slack.MsgOptionText(string(p), false))
	return len(p), err
}
