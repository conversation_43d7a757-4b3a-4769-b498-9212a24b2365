package log

import (
	"go.uber.org/zap"
)

// LogThreadEvent 统一输出线程相关事件日志
// eventType: 事件类型（如fetch/refresh/save等）
// thread: 线程名或ID
// status: 状态描述
// err: 错误信息（可为nil）
func LogThreadEvent(eventType, thread, status string, err error) {
	fields := []zap.Field{
		zap.String("event", eventType),
		zap.String("thread", thread),
		zap.String("status", status),
	}
	if err != nil {
		fields = append(fields, zap.Error(err))
		Logger.Error("[THREAD EVENT]", fields...)
	} else {
		Logger.Debug("[THREAD EVENT]", fields...)
	}
}

// LogImageEvent 统一输出图片相关事件日志
// eventType: 事件类型（如download/success/fail等）
// thread: 线程名或ID
// imageIndex: 图片序号
// status: 状态描述
// err: 错误信息（可为nil）
func LogImageEvent(eventType, thread string, imageIndex int, status string, err error) {
	fields := []zap.Field{
		zap.String("event", eventType),
		zap.String("thread", thread),
		zap.Int("imageIndex", imageIndex),
		zap.String("status", status),
	}
	if err != nil {
		fields = append(fields, zap.Error(err))
		Logger.Error("[IMAGE EVENT]", fields...)
	} else {
		Logger.Info("[IMAGE EVENT]", fields...)
	}
}
