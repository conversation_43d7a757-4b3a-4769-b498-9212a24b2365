/*
Offical Style for Discuz!(R)
URL: http://www.discuz.net
(C) 2001-2007 Comsenz Inc.
<style type="text/css">
*/

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Common Style ~~~~ */

* { word-wrap: break-word; }

body {
	margin: 0px;
	padding:0;
	width: 100%;
	height: 100%;
	scrollbar-base-color: #E0E8D0;
	scrollbar-arrow-color: #BDC8A8;
	font: 12px Tahoma, Verdana, sans-serif;
	background: #F0F3EA;
	color: #000000;

}

html, html>body {
	width: 100%;
	height: 100%;
}

#wrapper {
	width: 100%;
	height: auto;
	margin: 0 auto;
	border-top: 1px solid #BDC8A8;
	border-bottom: 1px solid #BDC8A8;
	background: #f0f3ea;
}


body, td, input, textarea, select, button { color: #000000; font: 12px/1.6em <PERSON>, <PERSON><PERSON><PERSON>, sans-serif; }
body, ul, dl, dd, p, h1, h2, h3, h4, h5, h6, form, fieldset { margin: 0; padding: 0; }
h1, h2, h3, h4, h5, h6 { font-size: 1em; }
#menu li, .popupmenu_popup li, #announcement li, .portalbox li, .tabs li, .postmessage fieldset li, .side li, .formbox li, .notice li { list-style: none; }
a { color: #4B6D0F; text-decoration: none; }
#menu2 { height: 20px;   }
	#menu2 ul { float: right; padding: 0px 10px 0;  }
		#menu2 li { float: left; padding: 0px 5px 0px; display:inline;}
		
	
	a:hover { text-decoration: underline; }
	a img { border: none; }
em, cite, strong, th { font-style: normal; font-weight: normal; }
table { empty-cells: show; border-collapse: collapse; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Layout & Block Style ~~~~ */

.wrap { width: 100%; text-align: left; margin: 0 auto; }
.notice { font-size: 12px; border: 1px solid #BDC8A8; background: #A2BC75 url(../../images/green001/notice.gif) no-repeat 1em 0.7em; padding: 0.5em 1em 0.3em 3em; margin-bottom: 0.5em; color: #2B2B2B; }
.message { margin: 3em 10em 5em !important; }
	.message h1 { line-height: 26px; border: 1px solid; background: #E0E8D0; background-repeat: repeat-x; background-position: 0 0; border-color: #F0F3EA #F0F3EA #BDC8A8 #F0F3EA; padding-left: 1em; margin-bottom: 1em; }
	.message p { margin: 1.5em 1em; }
	.message a { color: ; }
/*
#header { width: 100%; overflow: hidden; background: url(../../images/green001/headbg001.png) repeat-x #A2C672;} 
*/
#header { width: 100%; overflow: hidden; background: url(../../images/green001/headbg001.png) repeat-x #64822a;} 
/* #header { width: 100%; overflow: hidden; background: #054C02;}*/
	#header h2 { float: left; }
#footer { border-top: 1px solid #BDC8A8; background: #E0E8D0; color: #000000; padding: 12px 0; }
	#footlinks { float: right; margin-top: -3px; text-align: right; }
	#footer img { float: left; margin: 0 10px 0 0; }
	#copyright, #debuginfo { font: 11px/1.5em Arial, Tahoma, sans-serif; }
		#copyright strong, #copyright em { font-weight: bold; }
			#copyright strong a { color: #0954A6; }
			#copyright em { color: #FF9D25; }
			#debuginfo { color: #666666; }
	.scrolltop { cursor: pointer; }
#menu { height: 31px; border: 1px solid #BDC8A8; background: #728B47; background-repeat: repeat-x; }
	#menu ul { float: right; padding: 4px 10px 0; border-right: 1px solid #F0F3EA; }
		#menu li { float: left; }
			#menu li a { text-decoration: none; float: left; color: #FFFFFF; padding: 4px 8px 3px; background: url(../../images/green001/menu_itemline.gif) no-repeat 0 6px; }
				#menu li.hover, #menu li.current { background-color: #A2BC75; border: 1px solid; border-color: #BDC8A8 #BDC8A8 #F0F3EA; }
					#menu li.current { font-weight: bold; }
					#menu li.hover a { padding: 3px 7px; background-image: none; }
					#menu li.current a { padding: 4px 7px 3px; background-image: none; }
			#menu cite a { font-weight: bold; background-image: none; }
	.frameswitch { float: left; height: 30px; line-height: 30px; padding-left: 10px; border-left: 1px solid #F0F3EA; }
		#menu a.frameoff, #menu a.frameon { float: left; border: none; padding-left: 16px; margin-left: 0; background: no-repeat 0 50%; }
			#menu a.frameoff { background-image: url(../../images/green001/frame_off.gif); }
			#menu a.frameon { background-image: url(../../images/green001/frame_on.gif); }
#foruminfo { width: 100%; overflow: hidden; margin: 10px 0; color: #000000; }
	#userinfo, #foruminfo #nav { float: left; padding-left: 5px; }
	#forumstats, #headsearch { float: right; text-align: right; padding-right: 5px; }
	#foruminfo p { margin: 0; }
		#foruminfo a{ color: ; }
		#foruminfo em { color: #000000; }
		#foruminfo cite { font-weight: bold; }
			#foruminfo strong a { font-weight: bold; color: #000000; }
	#nav { margin: 10px 5px; }
		#foruminfo #nav { margin: 0; }
		#userinfo #nav { float: none; padding: 0; }
			#nav a { font-weight: bold; color: ; }
#announcement { border-top: 1px dashed #BDC8A8; line-height: 36px; height: 36px; overflow: hidden; }
	#announcement div { border: 1px solid #F0F3EA; padding: 0 10px; line-height: 35px !important; height: 36px; overflow-y: hidden;}
		#announcement li { float: left; margin-right: 20px; padding-left: 10px; background: url(../../images/green001/arrow_right.gif) no-repeat 0 50%; white-space: nowrap; }
			#announcement li em { font-size: 0.83em; margin-left: 5px; color: #000000; }
.portalbox { width: 100%; background: #BDC8A8; margin-bottom: 0.5em; border-collapse: separate; }
	.portalbox td { padding: 10px; vertical-align: top; background: #E0E8D0; background-repeat: repeat-x; background-position: 0 0; background-repeat: repeat-x; background-position: 0 0; border: 1px solid #F0F3EA; }
		.portalbox h3 { margin: 0 0 5px; font-size: 1em; white-space: nowrap; }
		.portalbox strong { font-weight: bold; margin-top: 4px;}
		.portalbox em { color: #666666; }
			.portalbox em a { color: #666666; }
			.portalbox cite a { color: ; }
		#supeitems li { float: left; height: 1.6em; overflow: hidden; }
		#hottags a { white-space: nowrap; margin-right: 0.5em; }
		#hottags h3 { clear:both; }
.headactions { float: right; line-height: 1em; padding: 10px 10px 0 0; }
	.headactions img { vertical-align: middle; cursor: pointer; padding: 0 5px; }
		.mainbox .headactions { color: #f8f9f7; }
		.mainbox .headactions a, .mainbox .headactions span, .mainbox .headactions strong { background: url(../../images/green001/headactions_line.gif) no-repeat 100% 50%; padding-right: 10px; margin-right: 8px; color: #f8f9f7; }
			.mainbox .headactions strong { font-weight: bold; background-image: url(../../images/green001/arrow_left.gif); }
.pages_btns { width: 100%; padding: 0 0 8px; margin-top: 3px;  overflow: hidden; }
	.postbtn, .replybtn { float: right; }
		.postbtn { margin-left: 10px; cursor: pointer; }
	.pages_btns .pages em { line-height: 26px; }
.pages, .threadflow { float: left; border: 1px solid #BDC8A8; background: ; height: 24px; line-height: 26px; color: #666666; overflow: hidden; }
	.pages a, .pages strong, .pages em, .pages kbd, #multipage .pages em { float: left; padding: 0 8px; line-height:26px; }
		.pages a:hover { background-color: #F0F3EA; }
		.pages strong { font-weight: bold; color: #2B2B2B; background: #BDC8A8; }
			.pages a.prev, .pages a.next { line-height: 24px; font-family: Verdana, Arial, Helvetica, sans-serif; }
				.pages a.next { padding: 0 15px; }
		.pages kbd { border-left: 1px solid #BDC8A8; margin: 0; }
			* html .pages kbd { padding: 1px 8px; }
			.pages kbd input { border: 1px solid #BDC8A8; margin-top: 3px !important; * > margin-top: 1px  !important; margin: 1px 4px 0 3px; padding: 0 2px; height: 17px; }
				.pages kbd>input { margin-bottom: 2px; }
	.threadflow { margin-right: 5px; padding: 0 5px; }
.tabs { padding-bottom: 26px; margin-bottom: 15px; background: #E0E8D0; border-top: 1px solid #F0F3EA; border-bottom: 1px solid #BDC8A8; }
	.tabs li { float: left; line-height: 25px; border-right: 1px solid #BDC8A8; }
		.tabs li.current { background: #F0F3EA; height: 27px; font-weight: bold; }
			.tabs li a { float: left; padding: 0 10px; }
	.headertabs { background: #F0F3EA none; margin-bottom: 0; }
		.headertabs li.current { background-color: #A2BC75; }
	.sendpm a { color: #2B2B2B; background: url(../../images/green001/buddy_sendpm.gif) no-repeat 15px 50%; padding: 0 20px 0 35px !important; }
#headfilter { border: solid #BDC8A8; border-width: 1px 1px 0; }
	#headfilter .tabs { border-bottom-color: #F0F3EA; margin-bottom: 0; }
#footfilter { padding: 10px; he\ight: 44px; height: 24px; line-height: 24px; background: ; border-color: #BDC8A8; font-family: Simsun, "Times New Roman"; }
	#footfilter form { float: right; }
		#footfilter * { vertical-align: middle; }
/* .legend { border: 1px solid #BDC8A8; background: #E0E8D0; padding: 10px; margin: 10px auto; width: 500px; text-align: center; line-height: 35px; } */
.legend { border: 1px solid #BDC8A8; background: #E0E8D0; padding: 10px; margin: 10px auto; display: table; width: auto; text-align: center; line-height: 35px; }
	.legend label { padding: 0 20px; }
	.legend img { vertical-align: middle; margin: 0 10px 6px 0; }
.avatarlist { overflow: hidden; padding: 5px 0; }
	* html .avatarlist { height: 1%; }
	.avatarlist dl { float: left; width: 70px; border: 1px solid #BDC8A8; padding: 5px; margin-right: 5px; text-align: center; }
		.avatarlist dt { width: 70px; height: 70px; }
		.avatarlist dl img { width: 64px; height: 64px; }
	.avatarlist dd { height: 22px; line-height: 22px; overflow: hidden; }
.taglist { width: 100%; padding: 10px 0; overflow: hidden; }
	.taglist li { float: left; display: inline; width: 10em; height: 24px; overflow: hidden; margin: 0 10px; }
		.taglist li em { font-size: 10px; color: #666666; }
.attriblist * { color: #000000; }
	.attriblist dt { float: left; margin-right: 10px; }
	.attriblist .name { font-weight: bold; }
	.attriblist dd a { color: ;}
#forumlinks {}
	#forumlinks td { padding: 5px 5px 5px 55px; background: url(../../images/green001/forumlink.gif) no-repeat 18px 50%; color: #666666; }
	#forumlinks .forumlink_logo { float: right; }
#online {}
	#online h4 { font-weight: normal; color: #000000; }
		#online h4 strong { font-weight: bold; }
	#online dl { padding: 5px 5px 5px 55px; }
		#onlinelist { background: url(../../images/green001/online.gif) no-repeat 10px 10px; border-top: 1px solid #BDC8A8; }
		#online dt { padding: 5px; }
			#online dt img { margin-bottom: -3px; }
		#online dd { border-top: 1px solid #BDC8A8; }
	#bdayslist { padding: 10px 0 10px 55px; border-top: 1px solid #BDC8A8; background: url(../../images/green001/bdays_cake.gif) no-repeat 10px 5px; }
.userlist { overflow: hidden; padding: 5px 5px 0; }
	* html .userlist { height: 1%; }
	.userlist li { float: left; width:128px; height: 20px; overflow: hidden; }
		.userlist li img { vertical-align: middle; }
	#onlinelist .userlist li { height: auto; margin:4px auto ;}
#recommendlist { }
	#recommendlist li { float: left; white-space: nowrap; width: 24.9%; overflow: hidden; text-indent: 12px; background: url(../../images/green001/arrow_right.gif) no-repeat 2px 7px; }
	#recommendlist.rules li { width: 49%; }
.recommendrules { padding: 0px;}
#seccode { cursor: pointer; }
.autosave { behavior: url(#default#userdata); }
#menu a.notabs { background: none; }
.headactions a.notabs { background: none; margin-right: 0px; padding-right: 0px; }
.absmiddle { vertical-align: middle; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Box Style ~~~~ */

/*Main Box*/
.mainbox { background: #F0F3EA; border: 1px solid #BDC8A8; padding: 4; margin-bottom: 0.5em; }
	.mainbox h1, .mainbox h3, .mainbox h6 { line-height: 31px; padding-left: 1em; background: #728B47; background-repeat: repeat-x; background-position: 0 0; color: #f8f9f7; border-bottom: 1px solid #4B6D0F; }
		.mainbox h1 a, .mainbox h3 a { color: #f8f9f7; }
	.mainbox table { width: 100%; }
		.forumlist table, .threadlist table { border-collapse: separate; border-bottom: 1px solid #BDC8A8; border-top: 1px solid #BDC8A8; } 
		.mainbox thead th, .mainbox thead td { background: #A2BC75; padding: 2px 5px; line-height: 22px; color: #000000; }
			.mainbox thead.category th, .mainbox thead.category td { background: #A2BC75; }
			/* .mainbox thead.separation th, .mainbox thead.separation td { border-top: 1px solid #BDC8A8 } */
		.mainbox tbody th, .mainbox tbody td { border-top: 1px solid #BDC8A8; padding: 5px; }
			.mainbox tbody cite, .mainbox tbody em { line-height: 1.3em; }
				.forumlist tbody strong, .threadlist tbody strong , .formbox tbody strong  { color: #2B2B2B; }
	/*Tabel Footer: Button Operation*/
	.footoperation { background: #A2BC75; padding: 5px; border-top: 1px solid #BDC8A8; }
		.threadlist .footoperation { padding-left: 61px; }
		.footoperation * { vertical-align: middle; }
		.footoperation label { margin-right: 1em; cursor: pointer; }
		.footoperation button { line-height: 1em; display: inline; width: 0; overflow: visible; padding: 3px 5px 2px; border: 1px solid #BDC8A8; background: #F0F3EA; color: ; cursor: pointer; margin-left: 2px; }
			.footoperation>button { width: auto; }
	/*Forum & Thread List*/
	/* .forumlist tbody th, .forumlist tbody td, .threadlist tbody th, .threadlist tbody td { color: #000000; padding: 1px 5px; border-bottom: 1px solid #F0F3EA; background-color: #E0E8D0; } */
	.forumlist tbody th, .forumlist tbody td, .threadlist tbody th, .threadlist tbody td { color: #000000; padding: 1px 5px; background-color: #E0E8D0; }
		.forumlist tbody th { height: 40px; }
		.forumlist th, .threadlist th { text-align: left; }
				.forumlist th { padding-left: 55px !important; }
					.forumlist h2 em { color: ; }
				.forumlist tbody th { background-image: url(../../images/green001/forum.gif); background-repeat: no-repeat; background-position: 13px 50%; }
					.forumlist tbody th.new { background-image: url(../../images/green001/forum_new.gif); }
					.moderators a { color: ; }
					.moderators a strong { font-weight: bold; color: ; }
				.threadlist th label { float: right; }
				.threadpages { background: url(../../images/green001/multipage.gif) no-repeat 0 100%; font-size: 11px; margin-left: 5px; white-space: nowrap; }
					.threadpages a { padding-left: 8px; }
						.threadpages a:hover { text-decoration: underline; }
				.threadlist th a.new { color: #2B2B2B; text-transform: uppercase; font-size: 9px; white-space: nowrap; }
				.threadlist th input { float: left; margin-right: 5px; }
				.threadlist th em, .threadlist th em a { color: #666666; }
				.threadlist th img.icon { float: left; margin-left: -22px; }
				.threadlist th img.attach, .threadlist th img.icon { margin-bottom: -3px; }
				.threadlist .target { float: left; display: block; width: 20px; height: 20px; margin-left: -28px; text-indent: -9999px; overflow: hidden;  }
					* html .threadlist .target  { margin-left: -14px; margin-right: 5px; }
	.forumlist tbody tr:hover th, .forumlist tbody tr:hover td, .threadlist tbody tr:hover th, .threadlist tbody tr:hover td { background-color: #E0E8D0; }
		.forumlist td.lastpost { width: 260px; }
		.threadlist td.folder { text-align: center; width: 30px; }
		.threadlist td.icon { text-align: center; padding: 3px 0; width: 16px; }
		.threadlist td.author { width: 120px; }
		.threadlist td.lastpost { text-align: right; width: 120px; padding-right: 15px; }
			.threadlist td.lastpost cite a { color: #666666; }
		.forumlist cite, .threadlist cite { display: block; }
		.threadlist td.author em, .threadlist td.lastpost em { font-size: 11px; }
		label.highlight { width: 40px; margin-right: 30px; }
		label.highlight, label.highlight input { float: left; }
		label.highlight em { float: right; width: 16px; height:16px;  }
		.quickmanage label.highlight { width: 40px; margin: 3px 18px 3px 0; }

		#updatecircles th { background-image: none; }
			#updatecircles .circlelogo { float: left; margin-left: -40px; margin-top: 10px;  max-height: 32px; max-width: 32px; width: expression(this.width > 32 && this.height < this.width ? 32: true); height: expression(this.height > 32 ? 32: true); }

	/*Viewthread*/
	.viewthread { padding-bottom: 1px; padding-top: 1px; background: #A2BC75; }
		.viewthread table, #pmprompt table, #forumlinks, #pmlist, #specialpost, #newpost, #editpost { table-layout: fixed; }
		.viewthread ins, .mainbox ins { display: block; text-align: center; text-decoration: none; margin-bottom: 1px; background: ; border-bottom: 1px solid #BDC8A8; line-height: 26px; }
			ins.logininfo { background: #A2BC75; padding: 2px 5px; line-height: 22px; color: #000000; text-align: left; border: none; }
			.viewthread ins, .viewthread ins a { color: #2B2B2B; }
			.viewthread td.postcontent, .viewthread td.postauthor { vertical-align: top; padding: 0 1px; border: none; overflow: hidden; background: #E0E8D0; }
			.postinfo { color: #000000; border-bottom: 1px dashed #BDC8A8; padding: 0 5px; line-height: 26px; height: 26px; overflow: hidden; }
				.postinfo strong, .postinfo em { float: right; line-height: 26px !important; cursor: pointer; padding: 0 3px; color: ; }
					.postinfo strong { margin-left: 5px; color: #2B2B2B; font-weight: bold; }
						* html .postinfo strong { margin-top: -2px; }
						.postinfo strong sup { font-weight: normal; margin-left: 1px; color: #666666; }
				.postinfo a { color: ; }
				.postinfo label { color:#2B2B2B; cursor: pointer; }
			.postmessage { padding: 10px; overflow-x: hidden; }
				.postmessage *, .pmmessage *, .register *  { line-height: normal; }
			.defaultpost {  height: auto !important; height:280px; min-height:280px !important; }
				.postmessage h2 {font-size: 1.17em; margin-bottom: 0.5em; }
				.t_msgfont, .t_msgfont td { font-size: 12px; line-height: 1.6em; }
				.t_smallfont, .t_smallfont td { font-size: 12px; line-height: 1.6em; }
				.t_bigfont, .t_bigfont td { font-size: 16px; line-height: 1.6em; }
					.t_msgfont *, .t_smallfont *, .t_bigfont * { line-height: normal; }
					.t_msgfont a, .t_smallfont a, .t_bigfont a { color: ; }
				.postratings { float: right; line-height: 4em; overflow: hidden; }
			.signatures { overflow: hidden; height: expression(signature(this)); max-height: 240px; background: url(../../images/green001/sigline.gif) no-repeat 0 0; margin: 10px; padding-top: 20px; color: #000000; line-height: 1.6em; }
				.signatures * { line-height: normal; }
				.signatures strong { font-weight: bold; }
			.postactions { border-top: 1px dashed #BDC8A8; background: ; line-height: 30px; height: 30px; padding: 0 10px; }
				.postactions strong { cursor: pointer; }
				.postactions input { float: right; margin: 5px 0 0 5px; }
				.postactions p { float: right; }
			.postmessage .box { border-width: 0; margin: 5px 0; }
			.postmessage .typeoption { width: 500px; }
			 	.typeoption tbody th { width: 100px; }
				.typeoption tbody td, .typeoption tbody th { border-top: 0px; border-bottom: 1px dashed #BDC8A8; }
				.postmessage .box tbody th, .postmessage .box tbody td { border-top-color: #BDC8A8; }
		.postmessage fieldset { font-size: 12px; width: 500px; padding: 10px; border: 1px solid #BDC8A8; margin-top: 2em; }
			.postmessage fieldset li { color: #666666; line-height: 1.6em; }
			.postmessage fieldset li cite, .postmessage fieldset li em { margin: auto 10px; }
		.t_msgfont li, .t_bigfont li, .t_smallfont li, .faq li { margin-left: 2em; }
		.postattach { width: 500px; margin: 10px 0; }
		.postattachlist { width: 500px; font-size: 12px; margin-top: 2em; }
		.t_attach { border: 1px solid #BDC8A8; background: #F0F3EA; font-size: 12px; padding: 5px; }
			.t_attach em { color: #666666; }
		.t_attachlist { border-bottom: 1px dashed #BDC8A8; padding: 5px 0; }
			.t_attachlist dt { font-weight: bold; }
				.t_attachlist dt img { margin-bottom: -4px; }
			.t_attachlist dd { padding-left: 20px; color: #666666; }
		.t_attachinsert { margin: 1em 0; font-size: 12px; }
			.t_attachinsert p img { margin-bottom: -4px; }
		.t_table { border: 1px solid #BDC8A8; empty-cells: show; border-collapse: collapse; }
			.t_table td { padding: 4px; border: 1px solid #BDC8A8; overflow: hidden; }
		/* Discuz! Code */
		/*CODE & Quote*/
		.blockcode, .quote { font-size: 12px; margin: 10px 20px; border: solid #BDC8A8; border-width: 4px 1px 1px; background: #E0E8D0; background-repeat: repeat-x; background-position: 0 0; overflow: hidden; }
			.blockcode h5, .quote h5 { border: 1px solid; border-color: #F0F3EA #F0F3EA #BDC8A8 #F0F3EA; line-height: 26px; padding-left: 5px; color: #000000; }
				.blockcode code, .quote blockquote { margin: 1em 1em 1em 3em; line-height: 1.6em; }
					.blockcode code { font: 14px/1.4em "Courier New", Courier, monospace; display: block; padding: 5px; }
					.blockcode .headactions { color: #000000; font-size: 12px; cursor: pointer; padding-top: 5px; }
		p.posttags { margin: 2em 0em 0.5em 0em; }
			p.posttags a, .footoperation span.posttags a { color: #F00; font-weight: bold; }
					p.posttags .postkeywords a, { color: #2B2B2B; }
		.postmessage strong { font-weight: bold; }
		.postmessage em { color:#000000; }
		.postmessage span.t_tag { cursor: pointer; border-bottom: 1px solid #F00; white-space: nowrap; }
		.mainbox td.postauthor { width: 180px; background: #F0F3EA; padding: 5px; overflow: hidden; }
			.postauthor cite { font-weight: bold; display: block; border-bottom: 1px dashed #BDC8A8; height: 21px; overflow: hidden; margin-bottom: 5px; }
				.postauthor cite label a { float: right; padding: 3px; }
			div.avatar { margin: 5px; text-align: center; width: 160px; overflow: hidden }
			.postauthor dt { float: left; margin-right: 0.5em; color: #000000; }
			.postauthor dd, .postauthor dt { height: 1.6em; line-height: 1.6em; }
			.postauthor dd { overflow: hidden; }
			.postauthor p { margin: 0 10px; }
				.postauthor p.customstatus { color: #000000 }
				.postauthor p em, .postauthor dt em { color: #2B2B2B; }
			.postauthor ul { margin: 5px 10px; line-height: 1.6em; overflow: hidden; }
				.postauthor li { text-indent: 22px; width: 49.5%; height: 1.6em; overflow: hidden; float: left; background-position: 0 50%; background-repeat: no-repeat; }
					.postauthor li.pm { background-image: url(../../images/green001/buddy_sendpm.gif); }
					.postauthor li.buddy { background-image: url(../../images/green001/user_add.gif); }
					.postauthor li.space { background-image: url(../../images/green001/forumlink.gif); }
					.postauthor li.online { background-image: url(../../images/green001/user_online.gif); color: #2B2B2B; }
					.postauthor li.offline { color: #000000; background-image: url(../../images/green001/user_offline.gif); }
					.postauthor li.magic { background-image: url(../../images/green001/magic.gif);}
			.postauthor dl.profile, .postauthor div.bio { margin: 5px 10px; padding-top: 5px; }
/*Common Box*/
.box { background: #F0F3EA; border: 1px solid #BDC8A8; padding: 4; margin-bottom: 0.5em; }
	.box h4 { background: #E0E8D0; background-repeat: repeat-x; background-position: 0 0; line-height: 30px; padding: 0 10px; }
	.box table { width: 100%; }
		/* .box td { border-top: 1px solid #BDC8A8; } */
		.box .box li { list-style: none;}
	.postattachlist h4, .tradethumblist h4, .pollpanel h4, .activitythread h4, .typeoption h4 { border-top: 1px solid #BDC8A8; }
	#pmprompt { border-color: #BDC8A8; }
		#pmprompt h4 { background: #A2BC75; border-top: none; }
		/* #pmprompt th, #pmprompt td { border-top-color: #BDC8A8; } */
/*List*/
td.user { width: 120px; }
td.nums { width: 80px; text-align: center; }
td.time { width: 120px; }
td.selector { width: 20px; text-align: center; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Spacial Threads ~~~~ */

.specialthread h1 { background: #F0F3EA; font-size: 1.5em; color: #000000; padding: 10px 5px; border-bottom: 1px solid #BDC8A8; }
	.specialthread h1 a { color: ; }
.specialthread .postcontent label { float: right; display: inline; margin: 12px 12px 0; border: 1px solid #BDC8A8; padding: 3px 5px; background: #A2BC75 no-repeat 3px 50%; }
	.specialthread .postcontent label strong { color: #2B2B2B; }
	.specialthread .postcontent label a { color: ; }
.specialthread .postauthor { width: 180px; }
.specialpostcontainer { padding: 0 1px; }
.specialpost { border-bottom: 4px solid #E0E8D0; text-align: right; }
	.specialpost .postinfo h2  { float: left; font-weight: normal; padding-left: 8px; font-size: 11px; }
	.specialpost .postinfo h2 em {}
		.specialpost .postinfo { border-color: #E0E8D0; height: 1.8em; }
		.specialpost strong { border: none; }
		.specialpost .postinfo h2 a { font-size: 12px; }
	.specialpost .postmessage { text-align: left; min-height: 30px; border-bottom: 1px solid #BDC8A8; }
		* html .specialpost .postmessage { height: 30px; overflow: visible; }
		p.imicons { margin: 8px auto; width: 160px;}
/* [ Poll ] */
.pollpanel { margin: 1em 0; border-width: 1px 0 0; }
	.pollpanel h4 span { float: left;line-height:30px;}
	.pollpanel h4 a { float: right;line-height:30px; font-weight:normal; }
	.pollpanel tbody td { vertical-align: middle; }
		.pollpanel tbody td a { color:  }
		.optionbar { float: left; margin-right: 0.5em; border: 1px solid #BDC8A8; background: #728B47; background-repeat: repeat-x; background-position: 0 100%; height: 12px; }
			.optionbar div { float: left; border: 1px solid #F0F3EA; height: 10px; overflow: hidden; }
/* [ Reward ] */
.rewardthread .postcontent label { background-image: url(../../images/green001/rewardsmallend.gif); padding-left: 25px; }
	.rewardthread .postcontent label.unsolved { background-image: url(../../images/green001/rewardsmall.gif); float:right; }
	#bestpost { padding-top: 10px; margin-top: 10px; border-top: 1px solid #BDC8A8; }
/* [ Activity ] */
.activitythread .box th { width: 7em; }
	#activityjoin label { float: none; border: none; background: transparent; padding: 0; margin: 0; }
/* [ Trade ] */
.tradethread .postmessage { min-height: 160px; }
	* html .tradethread .postmessage { height: 360px !important; }
	.tradethread .postauthor dt, .tradethread .postauthor dd { height: 20px; overflow: hidden; }
*>.tradeinfo { overflow: hidden; }
	* html .tradeinfo { height: 1%; }
	.tradeinfo h1 { background: #E0E8D0; font-size: 1.5em; color: #000000; padding: 10px 5px; border-bottom: 1px solid #BDC8A8; margin-bottom: 1em; }
	.tradethumb, .tradeattribute { float: left; }
		.tradethumb { width: 260px; text-align: center;}
		.tradeattribute { padding-left: 1em; }
		.tradeattribute img { vertical-align: middle; }
			.tradeattribute dl { overflow: hidden; padding-bottom:2em !important;  }
				.tradeattribute dt { float: left; width: 5em; padding: 0.5em; line-height: 2em; clear: left; }
				.tradeattribute dd { border-bottom: 1px dotted #BDC8A8; padding: 0.5em 1.5em; line-height: 2em; }
					.tradeattribute em, .tradeattribute del { color: #666666; }
					.tradeattribute strong { font-size: 1.6em; font-weight: bold; color: #F00; }
	.sellerinfo { float: right; display: inline; margin-right: 1em; width: 180px; }
		.sellerinfo h4 { border-bottom: 1px dotted #BDC8A8; }
		.sellerinfo dl { margin: 1em; }
	.tradeinfo .postinfo { clear: both; }
	.tradeinfo .postmessage { min-height: 100px; }
		* html .tradethread .postmessage { height: 100px; }

	*>.tradethumblist { overflow: hidden; }
		* html .tradethumblist { height: 1%; }
		.tradethumblist dl { float: left; text-align: center; padding: 10px; width: 170px; height: 220px; w\idth: 150px; he\ight: 200px; }
			.tradethumblist dd.thumblist { height: 100px; overflow: hidden; }
				.tradethumblist dd img { vertical-align: middle; cursor: pointer; }
			.tradethumblist dl p { height: 1.6em; overflow: hidden; }
			.tradethumblist p.tradename { height: 45px; line-height: 18px; margin-top: 5px; }
			.tradethumblist del { color: #666666; }
			.tradethumblist strong { font-weight: bold; color: #F00; }
	 #ajaxtradelist .price { text-align: right; }
	 	#ajaxtradelist strong { font-weight: bold; color: #F00; }
	 	#ajaxtradelist .popupmenu_popup { white-space: nowrap;overflow: visible; }
	 	#ajaxtradelist .popupmenu_popup a { color: ; }
/* [ Debate ] */
.debatethread .postmessage { min-height: inherit; height: auto; }
.debatethread .box { margin: 0;}
.debatethread .debatepoints { border-width: 1px 0 0; padding-bottom: 0; border-top: none; margin-bottom: 10px; }
	.debatepoints tbody td { border: 0px; width: 50%; border-bottom: none; vertical-align: top; }
		.debatepoints .message td.stand1 { border:1px solid #BDC8A8; background: #A2BC75; border-bottom: none; }
		.debatepoints .message td.stand2 { border:1px solid #BDC8A8; background: #E0E8D0; border-bottom: none; }
		.debatepoints .button td.stand1 { border:1px solid #BDC8A8; background: #A2BC75; border-top: none; }
		.debatepoints .button td.stand2 { border:1px solid #BDC8A8; background: #E0E8D0; border-top: none; }
	.debatepoints h2 { padding-left: 40px; line-height: 2em; background-position: 5px 1px; background-repeat: no-repeat; }
		.stand1 h2 { background-image: url(../../images/green001/debate_stand_1.gif); }
		.stand2 h2 { background-image: url(../../images/green001/debate_stand_2.gif); }
		.poststand0, .poststand1, .poststand2 { font-size: 1.17em; text-align: center; display: block; float: left; border: 1px solid #BDC8A8; background: ; color: #666666; width: 40px; height: 22px; line-height: 22px; margin-right: 12px; }
		.poststand1 { border: 1px solid #BDC8A8; background: #A2BC75; color: #2B2B2B; }
		.poststand2 { border: 1px solid #BDC8A8; background: #E0E8D0; color: ; }
	.debatepoints p { padding: 0 10px; overflow: hidden; }
	.debatepoints a { margin: 0 auto; display: block; width: 80px; text-align: center; border: 1px solid; padding: 0.3em 1em; }
		.debatepoints #affirmbutton { border-color: #BDC8A8; background: #A2BC75; color: #2B2B2B; }
		.debatepoints #negabutton { border-color: #BDC8A8; background: #E0E8D0; color: ; }
.debatethread .optionbar div { float: none; }

.payinfo dt { float: left; width: 10em; padding: 0.5em; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Control Panel ~~~~ */
.container { width: 100%; overflow: hidden; }
	.content { float: right; width: 80%; }
		.content .mainbox { padding-bottom: 5px; }
		.content .footoperation, .content .mainbox thead th, .content .mainbox thead td,
		.content .mainbox tbody th, .content .mainbox tbody td { border-top: none; border-bottom: 1px solid #BDC8A8; }
	.side { float: left; width: 18%;}
		.side div { border: 1px solid #BDC8A8;background: #E0E8D0; background-repeat: repeat-x; background-position: 0 0; margin-bottom: 0.5em; }
			.side h2 { padding-left: 10px; line-height: 2.4em; font-size: 1.17em; border: 1px solid; border-color: #F0F3EA #F0F3EA #BDC8A8 #F0F3EA;}
			.side ul { padding: 1px; }
				.side li{ text-indent: 26px; line-height: 2.4em; }
					.side h3 { font-weight: normal; background:url(../../images/green001/arrow_right.gif) no-repeat 14px 46%; }
						.side_on h3 { font-weight: bold; border: solid #BDC8A8; border-width: 1px 0; background: #E0E8D0 url(../../images/green001/arrow_down.gif) no-repeat 14px 46%; }
					.side li ul { border-bottom: 1px solid #BDC8A8; }
						.side li li { padding-left: 1em;}
				.side li.current { font-weight: bold; }
					.side li.current a { color: #000000; }
			.side li.first h3 { border-top: none; }
			.side li.last ul { border-bottom: none; }
	#memberinfo { }
		#memberinfo .memberinfo_avatar { text-align: center; width: 170px; font-weight: bold; }
			#memberinfo li label { color: #000000; margin-right: 0.5em; }
			#memberinfo .memberinfo_forum label { float: left; width: 7em; text-align: right; }
	.mysearch { float: left; display: block; margin-top: -10px; margin-left: 10px;}
		/* Message Tabs*/
		.msgtabs { border-bottom: 1px solid #BDC8A8; padding-bottom: 23px; padding-right: 5px; margin-top: 0.8em;}
			.msgtabs strong { float: right; padding: 0 12px; border: 1px solid #BDC8A8; border-bottom: 1px solid #A2BC75;  margin-right: 5px; text-decoration: none; height: 22px; line-height: 22px; font-weight: bold; background: #A2BC75; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ DropMenu ~~~~ */

.dropmenu { padding-right: 15px !important; background-image: url(../../images/green001/arrow_down.gif) !important; background-position: 100% 50% !important; background-repeat: no-repeat !important; cursor: pointer; }
.popupmenu_popup { text-align: left; line-height: 1.4em; padding: 10px; overflow: hidden; border: 1px solid #BDC8A8; background: #E0E8D0; background-repeat: repeat-x; background-position: 0 1px;  }
.headermenu_popup { width: 170px; }
	.headermenu_popup li { float: left; width: 7em; line-height: 24px; height: 24px; overflow: hidden; border-bottom: 1px solid #BDC8A8; }
.newspecialmenu { width: 100px; }
	.newspecialmenu li { background: url(../../images/green001/folder_s.gif) no-repeat 3px 50%; float: left; }
		.newspecialmenu li.poll { background-image: url(../../images/green001/pollsmall.gif); }
		.newspecialmenu li.trade { background-image: url(../../images/green001/tradesmall.gif); }
		.newspecialmenu li.reward { background-image: url(../../images/green001/rewardsmall.gif); }
		.newspecialmenu li.activity { background-image: url(../../images/green001/activitysmall.gif); }
		.newspecialmenu li.debate { background-image: url(../../images/green001/debatesmall.gif); }
		.newspecialmenu li.video { background-image: url(../../images/green001/videosmall.gif); }
		.newspecialmenu a { float: left; width: 75px; border-bottom: 1px solid #BDC8A8; padding: 5px 0 5px 25px; }
			.newspecialmenu a:hover { text-decoration: none; color: ; border-bottom-color: #BDC8A8; }
#forumlist_menu { padding: 10px 30px 10px 20px; }
	#forumlist_menu dl { padding: 5px 0; }
		#forumlist_menu dt a { font-weight: bold; color: #000000; }
		#forumlist_menu dd { padding-left: 1em; }
			#forumlist_menu li.sub { padding-left: 1em; }
			#forumlist_menu li.current a { font-weight: bold; }
			#forumlist_menu li a { font-weight: normal; color: ; }
.userinfopanel { border: 1px solid #BDC8A8; width: 140px; background: #E0E8D0; background-repeat: repeat-x; background-position: 0 0; padding: 10px; }
	.imicons { text-align: center; border: 1px solid #BDC8A8; background: #F0F3EA; padding: 4px 1px; }
		.imicons img { vertical-align: middle; }
	.userinfopanel p { text-align: left; margin: 0; }
		.userinfopanel p a { color: ; }
	.userinfopanel dl { border-bottom: 1px solid #BDC8A8; margin: 5px 0; padding: 5px 0; }
	.postauthor cite a { float: left; padding: 5px; border: solid #E0E8D0; border-width: 1px 1px 0; height: 10px; overflow: hidden; }
		.postauthor cite a.hover { border-color: #BDC8A8; background-color: #F0F3EA; }
	.popupmenu_popup .postauthor { width: 180px; }
		.popupmenu_popup .postauthor a { color: ; }
/*Popup Calendar*/
#calendar { border: 1px solid #BDC8A8; background: #E0E8D0; margin-bottom: 0.8em;}
	#calendar td { padding: 2px; font-weight: bold;}
	#calendar_week td { height: 2em; line-height: 2em; border-bottom: 1px solid #BDC8A8;}
	#hourminute td {padding: 4px 2px; border-top: 1px solid #BDC8A8;}
		.calendar_expire, .calendar_expire a:link, .calendar_expire a:visited {	color: #000000; font-weight: normal; }
		.calendar_default, .calendar_default a:link, .calendar_default a:visited { color: ;}
		.calendar_checked, .calendar_checked a:link, .calendar_checked a:visited { color: #2B2B2B; font-weight: bold;}
		td.calendar_checked, span.calendar_checked{ background: #BDC8A8;}
		.calendar_today, .calendar_today a:link, .calendar_today a:visited { color: #000000; font-weight: bold; }
	#calendar_header td{ width: 30px; height: 20px; border-bottom: 1px solid #BDC8A8; font-weight: normal; }
	#calendar_year { display: none;	line-height: 130%; background: #E0E8D0; position: absolute; z-index: 10; }
		#calendar_year .col { float: left; background: #E0E8D0; margin-left: 1px; border: 1px solid #BDC8A8; padding: 4px; }
	#calendar_month { display: none; background: #E0E8D0; line-height: 130%; border: 1px solid #DDD; padding: 4px; position: absolute; z-index: 11; }
#styleswitcher_menu { overflow: visible; }
	#styleswitcher_menu, #styleswitcher_menu ul li, #styleswitcher_menu ul li.current a, #styleswitcher_menu ul li a { white-space: nowrap; }
	#styleswitcher_menu ul li.current { font-weight: bold; }
		#styleswitcher_menu ul li.current a { color: #000000; }
#styleswitcher_menu {}
	#styleswitcher_menu li.current { font-weight: bold; }
.tagthread { width: 360px; }
	.tagthread .close { float: right; padding-top: 5px; }
	.tagthread h4 { line-height: 26px; border-bottom: 1px solid #BDC8A8; }
	.tagthread ul { padding: 5px; }
		.tagthread li { line-height: 1.8em; }
	.tagthread li.more { text-align: right; background: url(../../images/green001/arrow_right.gif) no-repeat 100% 50%; padding-right: 10px; }
.headactions .popupmenu_popup a, .headactions .popupmenu_popup strong { color: #000000; background: none; white-space: nowrap; }
	.headactions .popupmenu_popup { overflow: visible; }

*+html #my_menu, *+html #memcp_menu, *+html #stats_menu, *+html #plugin_menu { margin-left: 1px; }
* html #my_menu, * html #memcp_menu, * html #stats_menu, * html #plugin_menu { margin-left: 1px; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Form Style ~~~~ */

fieldset { border: none; }
input, textarea { border-width: 1px; background: #E0E8D0; border-color: #BDC8A8; padding: 2px; }
	input[type="radio"], input[type="checkbox"] { border: none; background: none; }
	.radio, .checkbox{ border: none; background: none; }
	.invitecodelist input { border: none; font-family: "Courier New", Courier, monospace; font-size: 13px; cursor: pointer; }
button { border: 1px solid; border-color: #BDC8A8 #666666 #666666 #BDC8A8; background: #A2BC75; height: 2em; line-height: 2em; cursor: pointer; }
#postsubmit, button.submit { margin-right: 1em; border: 1px solid; border-color: #FFFDEE #FDB939 #FDB939 #FFFDEE; background: #FFF8C5; color: #2B2B2B; padding: 0 10px; width:162px;}
button.insmsg { margin: 1em 0; border: 1px solid #BDC8A8; background: #A2BC75; color: #2B2B2B; }
.formbox th { width: 180px; text-align: left; }
	.formbox th, .formbox td { padding: 5px; }
	.formbox th, .formbox td { border-bottom: 1px solid #BDC8A8; }
	.formbox table a { color: ; }
.formbox label { cursor: pointer; }
.lighttxt, .formbox *.tips { color: #666666; }
.formbox th ul { padding: 5px 0; margin: 5px 0; }
	#threadtypes table td, #threadtypes table th { border-top: 1px solid #BDC8A8; border-bottom: 0;}
/*Login Form*/
#loginform * { vertical-align: middle; }
	#loginform button { line-height: 21px; height: 21px; padding: 0 4px; margin-left: 3px; }
/*PostForm & Editor*/
.editor_cell { vertical-align: top; }
#editor { border: solid; border-color: #BDC8A8; border-width: 1px 1px 0; background: ; }
	#editor td { border: none; padding: 2px; }
.editortoolbar table { width: auto; }
.editortoolbar a, .editortoolbar .a { display: block; padding: 1px; border: 1px solid ; cursor: pointer; }
	.editortoolbar a.hover, .editortoolbar a:hover, .editortoolbar .a1 { background-color: #E0E8D0; border: 1px solid #BDC8A8; text-decoration: none; }
.editor_switcher_bar {  position: relative; }
	.editor_switcher_bar a { float: right; padding: 0 3px; margin-right: 5px; }
	.editor_switcher_bar button { border: 1px solid; border-color: #BDC8A8 #BDC8A8 #F0F3EA #BDC8A8; font-weight: bold; height: 30px; he\ight: 28px; line-height: 28px; background: #F0F3EA; margin: 0 2px; position: relative; top: 6px; cursor: pointer; }
		*+html .editor_switcher_bar button { top: 4px; }
		* html .editor_switcher_bar button { top: 4px; }
		.editor_switcher_bar .editor_switcher { border-bottom-color: #BDC8A8; font-weight: normal; }
.editor_text { border: 1px solid; border-color: #BDC8A8 #BDC8A8 #BDC8A8 #BDC8A8; }
	.editor_text textarea { border: none; width: 99%; font: 12px/1.6em "Courier New", Courier, monospace; }
.editor_button { background: ; border: solid #BDC8A8; border-width: 0 1px 0; margin-bottom: 0.5em; }
	.editor_button button { background: transparent; border-width: 0 0 0 1px; color: ; }
.editor_attach {  border: 1px solid #BDC8A8; }
#wysiwyg { font: 12px/1.6em Tahoma, Verdana, sans-serif !important; }
	#wysiwyg * { line-height: normal; }
	#wysiwyg a { text-decoration:underline; color:  !important; color: ; }
	#wysiwyg li { margin-left: 2em; }
	#wysiwyg strong, #wysiwyg b { font-weight: bold; }
	#wysiwyg em, #wysiwyg i { font-style: italic; }
.fontname_menu { width: 97px; }
.fontsize_menu { width: 27px; line-height: normal; }
#posteditor_popup_table_menu { width: 220px; }
.fontname_menu li, .fontsize_menu li { cursor: pointer; }
.editor_colornormal, .editor_colorhover { border: none !important; padding: 2px !important; }
	.editor_colornormal div { width: 10px; height: 10px; overflow: hidden; cursor: pointer; border: 1px solid #F0F3EA; }
		.editor_colorhover div { width: 10px; height: 10px; overflow: hidden; cursor: pointer; border: 1px solid #000000; }
/*QuickPost*/
	#quickpost { overflow: hidden; padding-bottom: 0; }
		* html #quickpost { height: 1%; overflow: visible; }
		#quickpost h5 { margin: 0.5em 1em; }
		.postoptions, .postform, .smilies { float: left; }
		.postoptions, .smilies { width: 20%; }
			.postoptions p { margin: 2px 0.7em; }
		.postform { width: 59%; padding-bottom: 10px; }
			.postform p label { vertical-align: top; font-weight: bold; }
			.postform h5 input { width: 60%; }
			.postform p, .postform div { margin: 0 1em; }
			.postform h4 * { vertical-align: middle; }
				.postform h4 input { width: 60%; }
			.postform textarea { width: 90%; height: 160px; }
			.postform .btns { margin-top: 0.5em; line-height: 30px; color: #666666; }
				.postform .btns button { vertical-align: middle; }
				.postform .btns a { color: ; }
					.postform button { border: none; background: transparent; color: ; padding: 0; cursor: pointer; }
					.postform #postsubmit { float: left; display: inline; margin-left: 2.3em; }
					.btns em { color: #666666; }
		#smilieslist { border: 1px solid #BDC8A8; overflow: hidden; text-align: center; }
			#quickpost #smilieslist { margin: 6px 1em 0 ; }
			#quickpost h4 { border-bottom: 1px solid #BDC8A8;}
			#smilieslist td { border: none; padding: 8px 0; cursor: pointer; }
				#smilieslist td:hover { background: #BDC8A8; }
			#smilieslist .pages { float: none; border-width: 1px 0 0; }
			#smilieslist h4 { color: ; padding: 5px; line-height: 20px; background: ; border-bottom: 1px solid #BDC8A8; text-align: left; }
				#smilieslist .popupmenu_popup { overflow: visible; padding: 5px 10px; white-space: nowrap; }
					#smilieslist .popupmenu_popup a { color: ; }
/*Ajax Form*/
.ajaxform {}
	.ajaxform th, .ajaxform td { border-bottom: 1px solid #BDC8A8; padding: 5px; }
		.ajaxform thead th { font-weight: bold; }
	.ajaxform a { color: ; }
.btns th, .btns td { border: none !important; }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Advertisments ~~~~ */

/* #ad_headerbanner { float: right; margin: 15px 0 3px; margin-right: 5px;} */
/* one #ad_headerbanner { float: right; margin: 35px 0 3px; margin-right: 5px;} */
#ad_headerbanner { float: right; margin: 2px 0 3px; margin-right: 5px;}
.ad_text { border: 1px solid #BDC8A8; margin-bottom: 1px; padding: 6px; background: #E0E8D0; background-repeat: repeat-x; }
.ad_text table { width: 100%; border-collapse: collapse; }
	.ad_text td { background-repeat: repeat-x; background-position: 0 0; padding: 2px 10px; }.ad_textlink1 { float: left; white-space: nowrap; }
.ad_textlink2 { margin: 10px; }
.ad_textlink1,.ad_textlink2 { padding-left: 25px; background: url(../../images/green001/ad_icon.gif) no-repeat 0 50%; }
.ad_pip { clear: right; float: right; display: inline; margin: 10px 10px 10px; }
.ad_topicrelated { clear: both; float: right; display: inline; margin: 0 10px 10px; padding: 10px 10px 10px 30px; border: 1px solid #78A73D; background: #CAEEC0; }
.ad_column { text-align: center; margin-bottom: 0.5em; }
.ad_footerbanner { text-align: center; clear: both; margin: 5px }

/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Archiver ~~~~ */
.archiver .wrap { margin: 20px auto 10px; width: 760px; padding: 10px; border: 1px solid #BDC8A8; }
	.archiver_banner { text-align: center; padding: 5px; margin-top: 40px;}
	.archiver h1, .archiver h2 { font-size: 1.17em; padding: 0 5px; }
.archiver_forumlist, .archiver_threadlist { padding: 1em; font-size: 1.17em; line-height: 1.6em; }
	.archiver_forumlist ul { padding-left: 2em; }
	.archiver_threadlist li { list-style: none; padding-left: 10px; background: url(../../images/green001/arrow_right.gif) no-repeat 0 46%; }
		.archiver_threadlist li em { color: #666666; font-size: 0.83em; }
.archiver_post {  border-top: 1px solid #BDC8A8; }
	.archiver_post cite { padding-left: 10px; font-weight: bold; }
	.archiver_post p { line-height: 3em; height: 3em; margin-bottom: 0.5em; background: #E0E8D0; }
	.archiver_postbody { overflow:hidden;  font-size: 1.17em; padding: 0 10px 10px; border-bottom: 1px solid #BDC8A8; }
.archiver_pages, .archiver_fullversion { padding: 10px; }
	.archiver_pages strong, .archiver_fullversion strong, .archiver_fullversion strong a { font-weight: bold; color: #2B2B2B; }
#ajaxwaitid { position: absolute; display: none; z-index: 100; width: 100px; height: 1.6em; top: 0px; right: 0px; line-height: 1.6em; overflow: hidden; background: #dd0000; color: #ffffff;}
.postform .special, #postform .special { font-weight: bold; color: ;}
#newpost em { color: #666666 }

/* THANKS DIGG*/
.comment_digg {
    background: url("../../images/thanks.gif") no-repeat scroll 0 0 transparent;
    color: #FFFFFF;
    float: left;
    font-size: 11px;
    height: 30px;
    padding-top: 28px;
    text-align: center;
    width: 53px;
}
.comment_diggb {
    background: url("../../images/thanksb.gif") no-repeat scroll 0 0 transparent;
    float: left;
    width: 22px;
    margin-top: 4px;
}


/* FOOTER */
#footercontainer {
	width: 100%;
	font-family: Arial, Tahoma, sans-serif;
	text-align : center;
	background: #FFF;
}
#footer {
	width: 90%;
	padding: 16px 0;
	margin: 0 auto;
	min-width: 760px; 
	width: expression(document.body.clientWidth < 760? "760px": "90%" );
}

.footerline {
	position: relative;
	float: right;
	top: -28px !important;
	top: -26px;
	font-size: 9px; 
}

#footer td, #footer a {
	color: #FFF;
	text-decoration: none;
	padding: 0 5px;
}
#footer a:hover {
	color: #D2F0BC
}

.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 1px dotted #4B6D0F;
}

.tooltip .tooltiptext {
    width: 150px;
    background: #728B47;
    color: #fff;
    text-align: center;
    border-radius: 10px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    top: -5px;
    right: 110%;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent #728B47;
}

.t_msgfont img {
	max-width: 100%;
}

.topRedirectTipsFloat{
	top: 400px;
	width: 100%;
	position: fixed;
	background: #ffc107ed;
	height: 159px;
	padding-top: 100px;
	padding-left: 540px;
	border-bottom: 1px solid #fdb939;
}

.topRedirectTips{
	top: 0px;
	height: 0px;
}

.textThreadTopDiv {
	border: 1px solid #bdc8a8;
    margin: 10px;
    padding: 5px;
}

.textThreadTop {
	list-style-type: none;
}




/*
Default Style for Discuz!(R)
URL: http://www.discuz.net
(C) 2001-2007 Comsenz Inc.
<style type="text/css">
*/


