{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(make test:*)", "Bash(make fmt)", "<PERSON><PERSON>(make:*)", "Bash(rg:*)", "Bash(zen)", "mcp__zen__listmodels", "mcp__zen__chat", "Bash(rm:*)", "Bash(./bin/tiver:*)", "<PERSON><PERSON>(pkill:*)", "Bash(bin/tiver:*)", "Bash(/Users/<USER>/go/src/github.com/penwyp/hydra/bin/tiver refresh --help)", "<PERSON><PERSON>(mysql:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_navigate", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_network_requests"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["playwright"]}