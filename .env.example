# Tiver Application Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
# Complete DSN (recommended - overrides individual DB settings)
TIVER_DB_DSN=user:password@tcp(host:port)/database?tls=tidb&parseTime=true&loc=Asia%2FShanghai

# Individual Database Settings (used if DSN is not provided)
# TIVER_DB_HOST=gateway01.ap-southeast-1.prod.aws.tidbcloud.com
# TIVER_DB_PORT=4000
# TIVER_DB_USERNAME=your_username
# TIVER_DB_PASSWORD=your_password
# TIVER_DB_NAME=tiver
# TIVER_DB_TLS_NAME=tidb

# HTTP Configuration
TIVER_HTTP_USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
TIVER_HTTP_TIMEOUT=30
TIVER_HTTP_PROXY=
TIVER_HTTP_COOKIE=your_session_cookie_here

# Application Configuration
TIVER_LOG_LEVEL=info
TIVER_DATA_DIR=./data

# Example Development Configuration
# TIVER_LOG_LEVEL=debug
# TIVER_DATA_DIR=/tmp/tiver-dev

# Example Production Configuration
# TIVER_LOG_LEVEL=warn
# TIVER_DATA_DIR=/var/lib/tiver