<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片链接解析器</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        #link-list {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .image-row {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .image-row img {
            width: 100px;
            height: 100px;
            object-fit: contain;
            margin-right: 15px;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        .image-row img:hover {
            opacity: 0.8;
        }
        .image-row .url-text {
            flex: 1;
            word-break: break-all;
            align-self: center;
            font-size: 0.95rem;
        }
        #copy-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        @media (max-width: 576px) {
            .image-row {
                flex-wrap: wrap;
            }
            .image-row img {
                margin-bottom: 10px;
            }
            .image-row .url-text {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">图片链接解析器</h1>

        <!-- Upload Section -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">上传HTML文件</h5>
                <div class="mb-3">
                    <label for="file-input" class="form-label">选择一个 .html 或 .htm 文件</label>
                    <input type="file" class="form-control" id="file-input" accept=".html,.htm">
                </div>
            </div>
        </div>

        <!-- Result Section -->
        <div id="result-section" class="card mb-4" style="display: none;">
            <div class="card-body">
                <h5 class="card-title">解析结果</h5>
                <div id="link-list" class="border"></div>
                <button id="download-btn" class="btn btn-primary mt-3" disabled>下载链接</button>
            </div>
        </div>

        <!-- Image List Section -->
        <div id="image-list-section" class="card mb-4" style="display: none;">
            <div class="card-body">
                <h5 class="card-title">图片预览</h5>
                <div id="image-list"></div>
            </div>
        </div>

        <!-- Error Message -->
        <div id="error-message" class="alert alert-danger" role="alert" style="display: none;"></div>

        <!-- Copy Toast -->
        <div id="copy-toast" class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">链接已复制！</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS (for toast) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script>
        // List of common image extensions
        const imageExtensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'
        ];

        // Function to check if a URL is an image
        function isImageUrl(url) {
            return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
        }

        // Function to collect all image links from meta tags and other elements
        function collectImageLinks(doc) {
            const links = new Set(); // Use Set to handle duplicates

            // 1. Check meta tags with name starting with 'twitter:image'
            const metaTags = doc.querySelectorAll('meta[name^="twitter:image"]');
            metaTags.forEach(tag => {
                const content = tag.getAttribute('content');
                if (content && isImageUrl(content) && 
                    (content.startsWith('https://i.miji.bid/') || content.startsWith('https://i.mij.rip/'))) {
                    links.add(content);
                }
            });

            // 2. Check img tags (for robustness)
            const imgTags = doc.querySelectorAll('img');
            imgTags.forEach(img => {
                const src = img.getAttribute('src');
                if (src && isImageUrl(src) && 
                    (src.startsWith('https://i.miji.bid/') || src.startsWith('https://i.mij.rip/'))) {
                    links.add(src);
                }
            });

            // 3. Check a tags (for robustness)
            const aTags = doc.querySelectorAll('a');
            aTags.forEach(a => {
                const href = a.getAttribute('href');
                if (href && isImageUrl(href) && 
                    (href.startsWith('https://i.miji.bid/') || href.startsWith('https://i.mij.rip/'))) {
                    links.add(href);
                }
            });

            return Array.from(links);
        }

        // Function to rename miji.bid to mij.rip
        function renameLinks(links) {
            return links.map(link => {
                if (link.startsWith('https://i.miji.bid/')) {
                    return link.replace('https://i.miji.bid/', 'https://i.mij.rip/');
                }
                return link;
            });
        }

        // Function to create and download a text file
        function downloadLinks(links, filename) {
            const textContent = links.join('\n');
            const blob = new Blob([textContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}_images.txt`; // Use uploaded filename + _images.txt
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Function to copy link and show toast
        function copyLink(link) {
            navigator.clipboard.writeText(link).then(() => {
                const toastElement = document.getElementById('copy-toast');
                const toast = new bootstrap.Toast(toastElement, { delay: 2000 });
                toast.show();
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        // Function to display images and URLs
        function displayImages(links) {
            const imageList = document.getElementById('image-list');
            imageList.innerHTML = ''; // Clear previous content

            links.forEach(link => {
                const row = document.createElement('div');
                row.className = 'image-row';

                // Create image element
                const img = document.createElement('img');
                img.src = link;
                img.alt = 'Image';
                img.title = '点击复制链接';
                img.onerror = () => {
                    img.alt = '图片加载失败';
                    img.src = '';
                };
                img.addEventListener('click', () => copyLink(link));

                // Create URL span
                const urlSpan = document.createElement('span');
                urlSpan.className = 'url-text';
                urlSpan.textContent = link;

                // Create copy button
                const copyBtn = document.createElement('button');
                copyBtn.className = 'btn btn-success btn-sm';
                copyBtn.textContent = '复制';
                copyBtn.addEventListener('click', () => copyLink(link));

                // Append to row
                row.appendChild(img);
                row.appendChild(urlSpan);
                row.appendChild(copyBtn);
                imageList.appendChild(row);
            });

            document.getElementById('image-list-section').style.display = 'block';
        }

        // Handle file upload
        document.getElementById('file-input').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Get filename without extension
            const filename = file.name.replace(/\.(html|htm)$/i, '');

            // Reset UI
            const resultSection = document.getElementById('result-section');
            const imageListSection = document.getElementById('image-list-section');
            const linkList = document.getElementById('link-list');
            const imageList = document.getElementById('image-list');
            const downloadBtn = document.getElementById('download-btn');
            const errorMessage = document.getElementById('error-message');
            resultSection.style.display = 'none';
            imageListSection.style.display = 'none';
            linkList.textContent = '';
            imageList.innerHTML = '';
            downloadBtn.disabled = true;
            errorMessage.style.display = 'none';

            // Read and parse the file
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(e.target.result, 'text/html');

                    // Main logic
                    const imageLinks = collectImageLinks(doc);
                    const renamedLinks = renameLinks(imageLinks);
                    const uniqueLinks = [...new Set(renamedLinks)];
                    const sortedLinks = uniqueLinks.sort();

                    if (sortedLinks.length === 0) {
                        errorMessage.textContent = '未找到符合条件的图片链接。';
                        errorMessage.style.display = 'block';
                        return;
                    }

                    // Display links
                    linkList.textContent = sortedLinks.join('\n');
                    resultSection.style.display = 'block';
                    downloadBtn.disabled = false;

                    // Display images and URLs
                    displayImages(sortedLinks);

                    // Setup download button with dynamic filename
                    downloadBtn.onclick = () => downloadLinks(sortedLinks, filename);
                } catch (err) {
                    errorMessage.textContent = '解析文件时出错，请确保上传有效的HTML文件。';
                    errorMessage.style.display = 'block';
                    console.error(err);
                }
            };
            reader.onerror = function() {
                errorMessage.textContent = '读取文件时出错，请重试。';
                errorMessage.style.display = 'block';
            };
            reader.readAsText(file);
        });
    </script>
</body>
</html>
