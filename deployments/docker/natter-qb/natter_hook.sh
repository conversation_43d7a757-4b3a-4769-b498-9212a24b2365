#!/bin/sh

# Natter notification script arguments
protocol="$1"; private_ip="$2"; private_port="$3"; public_ip="$4"; public_port="$5"

# qBittorrent
qb_web_url="http://*************:8085"
qb_username="nas"
qb_password="acce-s6se/nas"

echo "Update qBittorrent listening port to ${public_port}..."

qb_cookie=$(
    curl "${qb_web_url}/api/v2/auth/login" \
        -X POST -sS --include \
        -H "Referer: ${qb_web_url}" \
        --data-raw "username=${qb_username}&password=${qb_password}" \
    | grep -m1 -i '^Set-Cookie: ' | cut -c13- | tr -d '\r'
)

curl "${qb_web_url}/api/v2/app/setPreferences" \
    -X POST -sS \
    -H "Referer: ${qb_web_url}" \
    --cookie "${qb_cookie}" \
    --data-raw 'json={"listen_port":"'"${public_port}"'"}'

curl "${qb_web_url}/api/v2/auth/logout" \
    -X POST -sS \
    -H "Referer: ${qb_web_url}" \
    --cookie "${qb_cookie}"

# URL编码函数
urlencode() {
    raw="$1"
    encoded=""
    i=0
    length=$(echo -n "$raw" | wc -c)

    while [ $i -lt $length ]; do
        char=$(echo -n "$raw" | cut -c $((i + 1)))
        case "$char" in
            [a-zA-Z0-9.~_-]) # 保留字母、数字和某些符号
                encoded="${encoded}${char}"
                ;;
            *) # 其他字符进行百分号编码
                hex=$(printf '%02X' "'$char")
                encoded="${encoded}%${hex}"
                ;;
        esac
        i=$((i + 1))
    done

    echo "$encoded"
}

api_url="https://api.day.app/eyANTXqT2poiMTYrEkjMPk_not_exist/Natter"

encoded_text=$(urlencode "qb outbound ip:port have been changed!!${public_ip}:${public_port}")

final_url="${api_url}/${encoded_text}"

echo "Send notification..."
# 使用 curl 发送通知
curl -s -o /dev/null -X GET "$final_url"

echo "Done."