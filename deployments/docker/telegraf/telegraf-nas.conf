[global_tags]

[agent]
  interval = "10s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "0s"
  flush_interval = "10s"
  flush_jitter = "0s"
  precision = "0s"
  hostname = "nas"
  omit_hostname = false

[[outputs.influxdb_v2]]
  urls = ["http://*************:8086"]
  token = "c82d94ac-fbe5-4e5f-8e51-1048000f9f18"
  organization = "hydra.cab"
  bucket = "hydra.cab"

[[inputs.cpu]]
  percpu = true
  totalcpu = true
  collect_cpu_time = false
  report_active = false
  core_tags = false

[[inputs.disk]]
  ignore_fs = ["tmpfs", "devtmpfs", "devfs", "iso9660", "overlay", "aufs", "squashfs"]

[[inputs.diskio]]

[[inputs.kernel]]

[[inputs.mem]]

[[inputs.processes]]

[[inputs.net]]
  interfaces = ["eth*"]

[[inputs.swap]]

[[inputs.system]]

# Read metrics about temperature
[[inputs.temp]]

[[inputs.transmission]]
  interval = '10s'

  [[inputs.transmission.clients]]
  name = 'TR'
  host = 'hydra.cab'
  port = 19091
  username = 'nas'
  password = 'acce-s6se/nas'

  [[inputs.transmission.clients]]
  name = 'TR-SMALL'
  host = 'hydra.cab'
  port = 19092
  username = 'nas'
  password = 'acce-s6se/nas'

[[inputs.qbittorrent]]
  [[inputs.qbittorrent.clients]]
  name = 'Synology-QB'
  host = '*************'
  port = 8085
  username = 'nas'
  password = 'acce-s6se/nas'

  [[inputs.qbittorrent.clients]]
  name = 'iKuai-QB'
  host = '*************'
  port = 8085
  username = 'nas'
  password = 'acce-s6se/nas'