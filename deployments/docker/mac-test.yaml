version: "3.7"
services:
  navidrome:
    image: deluan/navidrome:latest
    restart: unless-stopped
    environment:
      # Optional: put your config options customization here. Examples:
      ND_SCANSCHEDULE: 1h
      ND_LOGLEVEL: info
      ND_SESSIONTIMEOUT: 24h
      ND_BASEURL: ""
      ND_DATAFOLDER: /data
      ND_MUSICFOLDER: /music
    volumes:
      - "/Users/<USER>/Downloads/musicXXdata:/data"
      - "/Users/<USER>/Downloads/music:/music:ro"
    ports:
      - "4533:4533"

  musictag:
    image: xhongc/music_tag_web:latest
    restart: unless-stopped
    environment:
      ND_DATAFOLDER: /data
      ND_MUSICFOLDER: /music
    volumes:
      - /Users/<USER>/Downloads/music:/app/media
      - /Users/<USER>/Downloads/musicdata:/app/data
    ports:
      - "8001:8001"
