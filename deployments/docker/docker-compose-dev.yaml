version: "3.7"
services:
  redis1:
    image: quay.io/coppkaw/redis:latest
    container_name: redis1
    networks:
      zte_macvlan:
        ipv4_address: *************
    environment:
      - TZ=Asia/Shanghai
    restart: always

  redis2:
    image: quay.io/coppkaw/redis:latest
    container_name: redis2
    networks:
      zte_macvlan:
        ipv4_address: *************
    environment:
      - TZ=Asia/Shanghai
    restart: always

  alpine1:
    image: quay.io/coppkaw/alpine:latest
    container_name: alpine1
    restart: always
    privileged: true
    networks:
      zte_macvlan:
        ipv4_address: *************
    command: sleep 100000
    environment:
      - TZ=Asia/Shanghai

networks:
  zte_macvlan:
    external: true  # 使用外部网络，确保已创建此网络
