#!/bin/sh

# Natter notification script arguments
protocol="$1"; private_ip="$2"; private_port="$3"; public_ip="$4"; public_port="$5"

# Transmission
tr_web_url="http://*************:9091/transmission"
tr_username="nas"
tr_password="acce-s6se/nas"

echo "Update Transmission listening port to ${public_port}..."

tr_sessid=$(
    curl "${tr_web_url}/rpc" \
        -X POST -Ss --include \
        -u "${tr_username}:${tr_password}" \
        -H "Referer: ${tr_web_url}" \
    | grep -m1 -i '^X-Transmission-Session-Id: ' | cut -c28- | tr -d '\r'
)

curl "${tr_web_url}/rpc" \
    -X POST -Ss \
    -u "${tr_username}:${tr_password}" \
    -H "X-Transmission-Session-Id: ${tr_sessid}" \
    -H "Referer: ${tr_web_url}" \
    --data-raw '{"method":"session-set","arguments":{"peer-port":'"${public_port}"'}}'

# URL编码函数
urlencode() {
    raw="$1"
    encoded=""
    i=0
    length=$(echo -n "$raw" | wc -c)

    while [ $i -lt $length ]; do
        char=$(echo -n "$raw" | cut -c $((i + 1)))
        case "$char" in
            [a-zA-Z0-9.~_-]) # 保留字母、数字和某些符号
                encoded="${encoded}${char}"
                ;;
            *) # 其他字符进行百分号编码
                hex=$(printf '%02X' "'$char")
                encoded="${encoded}%${hex}"
                ;;
        esac
        i=$((i + 1))
    done

    echo "$encoded"
}

api_url="https://api.day.app/eyANTXqT2poiMTYrEkjMPk_not_exist/Natter"

encoded_text=$(urlencode "trsmall outbound ip:port have been changed!!${public_ip}:${public_port}")

final_url="${api_url}/${encoded_text}"

echo "Send notification..."
# 使用 curl 发送通知
curl -s -o /dev/null -X GET "$final_url"

echo "Done."