---
# For configuration options and examples, please see:
# https://gethomepage.dev/en/configs/services

- Downloader:
    - QB 8089:
        icon: qbittorrent.png
        href: http://hydra.cab:18089
        description: 多拨刷流
        ping: hydra.cab
        widget:
          type: qbittorrent
          url: http://hydra.cab:18089
          username: nas
          password: acce-s6se/nas

    - QB 8085:
        icon: qbittorrent.png
        href: https://hydra.cab:18085
        ping: hydra.cab
        description: 正常下载
        widget:
          type: qbittorrent
          url: https://hydra.cab:18085
          username: nas
          password: acce-s6se/nas
        container: qb

    - Tr:
        icon: transmission.png
        href: http://hydra.cab:19091
        ping: hydra.cab
        description: 保种 - 大于1GB
        widget:
          type: transmission
          url: http://hydra.cab:19091
          username: nas
          password: acce-s6se/nas
        container: tr

    - TrSmall:
        icon: transmission.png
        href: http://hydra.cab:19092
        ping: hydra.cab
        description: 保种 - 小文件
        widget:
          type: transmission
          url: http://hydra.cab:19092
          username: nas
          password: acce-s6se/nas
        container: trsmall


- Automation:
    - MoviePilot:
        icon: overseerr.png
        href: http://hydra.cab:16000
        ping: hydra.cab
        description: movie pilot at home
        container: mp

    - NAS220+:
        icon: dart.png
        href: http://hydra.cab:15000
        ping: hydra.cab
        description: Synology DS220+

    - HomeAssistant:
        icon: home-assistant.png
        href: http://hydra.cab:18123/lovelace/0
        ping: hydra.cab
        description: Home Assistant
        container: ha

    - NasTools:
        icon: kamatera.png
        href: http://hydra.cab:13001
        ping: hydra.cab
        description: nastools at home
        container: ns-dev

    - IYUU:
        icon: firefly.png
        href: http://hydra.cab:18787/index.html
        ping: hydra.cab
        description: IYUU auto seed
        container: iyuuplus

    - Talebook:
        icon: bookstack.png
        href: http://hydra.cab:18084
        ping: hydra.cab
        description: tale book
        container: talebook

    - CalibreWeb:
        icon: bookstack.png
        href: http://hydra.cab:18083
        ping: hydra.cab
        description: calibre web
        container: calibre-web

    - Portainer:
        icon: portainer.png
        href: http://hydra.cab:15004
        ping: hydra.cab
        description: container manager
        widget:
          type: portainer
          url: http://hydra.cab:15004
          env: 2
          key: ptr_IY5SS2xO1D8zc9BLa+ImG1VbN6O0mB/U40Fpdj/kB24=
        container: portainer


- Media:
    - Emby:
        icon: emby.png
        href: https://hydra.cab:16002
        ping: hydra.cab
        description: Emby Server
        widget:
          type: emby
          url: https://hydra.cab:16002
          key: bbca4ac4e9084b2a9a46ea2f7f7f6d7a
          enableBlocks: true # optional, defaults to false
          enableNowPlaying: true # optional, defaults to true
        container: emby

    - EmbyX:
        icon: emby.png
        href: https://hydra.cab:16003
        ping: hydra.cab
        description: EmbyX Server
        widget:
          type: emby
          url: https://hydra.cab:16003
          key: f4bf1ec1c77242c6af0fbe402e49b730
          enableBlocks: true # optional, defaults to false
          enableNowPlaying: false # optional, defaults to true
        container: embyx

    - JellyfinX:
        icon: jellyfin.png
        href: https://hydra.cab:16004
        ping: hydra.cab
        description: Jellyfin X Library
        widget:
          type: jellyfin
          url: https://hydra.cab:16004
          key: adc8ce3545d44562aeaf76f0b9ecc4c3
          enableBlocks: true # optional, defaults to false
          enableNowPlaying: false # optional, defaults to true
        container: jellyfinx

    - MetaTube:
        icon: lsio.png
        href: http://*************:7831
        ping: *************
        description: metatube server

    - ChineseSubFinder:
        icon: feedly.png
        href: http://*************:19035
        ping: *************
        description: chinese sub finder server

- LocalService:
    - iKuai:
        icon: coreos.png
        href: http://***********/#/system-overview
        ping: ***********
        description: iKuai Network Manager

    - PVE:
        icon: proxmox-light.png
        href: https://hydra.cab:14765
        ping: hydra.cab
        description: Proxmox VE

    - CookieCloud:
        icon: casaos.png
        href: http://***********:8088
        ping: ***********
        description: CookieCloud Server

    - Jackett:
        icon: jackett.png
        href: http://***********21:9117/UI/Dashboard
        ping: ***********21
        description: Jackett Torrent Indexer

    - ClashYACD:
        icon: clash.png
        href: http://*************/
        ping: *************
        description: Clash Dashboard