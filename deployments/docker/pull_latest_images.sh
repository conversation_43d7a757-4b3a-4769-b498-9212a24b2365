#!/bin/bash

# 定义一个关联数组用于特殊处理（例如，emby）
declare -A special_images=(
["lovechen/embyserver"]="lovechen-embyserver"
["amilys/embyserver"]="amilys-embyserver"
)

# 定义一个包含标签的镜像数组
images=(
"lobehub/lobe-chat:latest"
"ollama/ollama:latest"
"janten/iftop:latest"
"adguard/adguardhome:latest"
"allanpk716/chinesesubfinder:latest-lite"
"dreamacro/clash:latest"
"easychen/cookiecloud:latest"
"iyuucn/iyuuplus:latest"
"portainer/portainer-ce:latest"
"redis:latest"
"registry:latest"
"haishanh/yacd"
"homeassistant/home-assistant:latest"
"jxxghp/moviepilot-v2:latest"
"linuxserver/calibre-web:latest"
"linuxserver/transmission:latest"
"linuxserver/qbittorrent:latest"
"jellyfin/jellyfin:latest"
"ghcr.io/gethomepage/homepage:latest"
"metatube/metatube-server:latest"
"metatube/metatube-server:1.2.3"
"linuxserver/transmission:4.0.6"
"linuxserver/qbittorrent:4.6.1"
"linuxserver/qbittorrent:4.6.5"
"haroldli/xiaoya-tvbox:latest"
"nattertool/natter:latest"
)

# 阿里云 registry namespace (请替换 'your-namespace' 为您的实际命名空间)
ALIYUN_NAMESPACE="coppkaw"

# 接收第一个命令行参数作为过滤器
FILTER="$1"
filtered_images=()

# 如果过滤器不为空，则过滤镜像列表
if [ -n "$FILTER" ]; then
    echo "使用过滤器: '$FILTER'"
    for image in "${images[@]}"; do
        if [[ "$image" == *"$FILTER"* ]]; then
            filtered_images+=("$image")
        fi
    done
else
    echo "未提供过滤器，将处理所有镜像。"
    filtered_images=("${images[@]}")
fi

# 如果过滤后列表为空，则提示并退出
if [ ${#filtered_images[@]} -eq 0 ]; then
    echo "没有找到与过滤器 '$FILTER' 匹配的镜像。"
    exit 0
fi


# 函数：给镜像打标签
tag_image() {
    local source_image="$1"
    local target_image="$2"
    echo "正在打标签: $source_image -> $target_image"
    docker tag "$source_image" "$target_image"
}

# 函数：推送镜像
push_image() {
    local image="$1"
    echo "正在推送: $image"
    docker push "$image"
}

# 函数：为镜像打标签并推送到仓库
tag_and_push_image() {
    local image="$1"
    local target_name="$2"
    local tag="$3"

    # 为 Aliyun registry 打标签并推送
    local aliyun_target="registry.cn-hangzhou.aliyuncs.com/$ALIYUN_NAMESPACE/$target_name:$tag"
    tag_image "$image" "$aliyun_target"
    push_image "$aliyun_target"
}

# 处理每个过滤后的镜像
for image in "${filtered_images[@]}"; do
    echo "--------------------------------------------------"
    echo "正在处理镜像: $image"

    # 从原始源拉取
    echo "正在拉取: $image"
    docker pull "$image"

    # 提取不带标签的镜像名称
    image_name="${image%:*}"

    # 确定标签
    if [[ "$image" == *:* ]]; then
        tag="${image##*:}"
    else
        tag="latest"
    fi

    # 对特定镜像进行特殊处理
    if [[ -n "${special_images[$image_name]}" ]]; then
        target_name="${special_images[$image_name]}"
    else
        # 从镜像名称中提取最后一部分作为目标名称
        target_name="${image_name##*/}"
    fi

    # 调用函数进行打标签和推送
    tag_and_push_image "$image" "$target_name" "$tag"

    # 删除所有本地镜像（请谨慎使用此命令）
    echo "正在清理本地镜像..."
    docker rmi -f $(docker images -q)
done

echo "--------------------------------------------------"
echo "所有任务完成！"