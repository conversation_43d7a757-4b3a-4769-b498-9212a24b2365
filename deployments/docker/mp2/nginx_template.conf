server {
    # 监听域名和端口
    listen 80;
    server_name *********** *********** *********** OUTBOUND_IP;

    location / {
        # 判断来源IP是否为相同公网IP，是的话则认为是内网访问
        if ($http_x_forwarded_for = "OUTBOUND_IP") {
            return 307 http://*************:3000$request_uri;
        }
        # 默认转发地址
        return 307 http://OUTBOUND_IP:OUTBOUND_PORT$request_uri;
    }

    # 跨域支持（如有必要）
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization';
}