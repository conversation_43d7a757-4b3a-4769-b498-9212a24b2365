#!/bin/bash

# 检查是否提供了正确的参数
if [ "$#" -ne 5 ]; then
    echo "Usage: $0 <protocol> <local_ip> <local_port> <outbound_ip> <outbound_port>"
    exit 1
fi

# 获取参数
PROTOCOL=$1
LOCAL_IP=$2
LOCAL_PORT=$3
OUTBOUND_IP=$4
OUTBOUND_PORT=$5

# 定义模板文件和目标文件路径
TEMPLATE_FILE="/volume2/service/bin/mp2/nginx_template.conf"  # 模板文件路径
CONFIG_FILE="/volume2/service/bin/mp2/nginx.conf"            # 输出配置文件路径

# 检查模板文件是否存在
if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "Error: Template file $TEMPLATE_FILE does not exist."
    exit 1
fi

# 替换模板中的变量并写入到配置文件
sed \
    -e "s/OUTBOUND_IP/$OUTBOUND_IP/g" \
    -e "s/OUTBOUND_PORT/$OUTBOUND_PORT/g" \
    "$TEMPLATE_FILE" > "$CONFIG_FILE"

# 检查替换是否成功
if [ $? -eq 0 ]; then
    echo "Successfully generated $CONFIG_FILE from template."
else
    echo "Error: Failed to generate configuration file."
    exit 1
fi

# 重启 Docker 容器 mp2-302
DOCKER_RESTART_STATUS="success"
docker restart mp2-302
if [ $? -ne 0 ]; then
    echo "Error: Failed to restart Docker container mp2-302."
    DOCKER_RESTART_STATUS="failure"
else
    echo "Docker container mp2-302 restarted successfully."
fi


# URL编码函数
urlencode() {
    raw="$1"
    encoded=""
    i=0
    length=$(echo -n "$raw" | wc -c)

    while [ $i -lt $length ]; do
        char=$(echo -n "$raw" | cut -c $((i + 1)))
        case "$char" in
            [a-zA-Z0-9.~_-]) # 保留字母、数字和某些符号
                encoded="${encoded}${char}"
                ;;
            *) # 其他字符进行百分号编码
                hex=$(printf '%02X' "'$char")
                encoded="${encoded}%${hex}"
                ;;
        esac
        i=$((i + 1))
    done

    echo "$encoded"
}

api_url="https://api.day.app/eyANTXqT2poiMTYrEkjMPk_not_exist/Natter"

encoded_text=$(urlencode "MoviePilot outbound ip:port have been changed!!${OUTBOUND_IP}:${OUTBOUND_PORT}")

final_url="${api_url}/${encoded_text}"

echo "Send notification..."
# 使用 curl 发送通知
curl -s -o /dev/null -X GET "$final_url"