server {
    listen 80;
    server_name wd.hydra.cab OUTBOUND_IP;

    location / {
        # 判断来源IP是否为相同公网IP，是的话则认为是内网访问
        if ($http_x_forwarded_for = "OUTBOUND_IP") {
            return 307 http://***********:5005$request_uri;
        }
        return 307 http://OUTBOUND_IP:OUTBOUND_PORT$request_uri;
    }

    # 跨域支持（如有需要）
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PROPFIND, PROPPATCH, MKCOL, COPY, MOVE, LOCK, UNLOCK, DELETE, PUT, HEAD, PATCH, LINK, UNLINK';
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, Depth';
}