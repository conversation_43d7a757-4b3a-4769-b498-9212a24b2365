port: 7890
socks-port: 7891
allow-lan: true
mode: Rule
log-level: info
ipv6: true
external-controller: 0.0.0.0:9090

# dns and rule-providers are added to fully support the features from surge-penwyp-universal.conf
dns:
  enable: true
  ipv6: true
  nameserver:
    - ************
    - *********
    - ***************
    - system
  nameserver-policy:
    '*.taobao.com': '*********'
    '*.tmall.com': '*********'
    '*.alipay.com': '*********'
    '*.alicdn.com': '*********'
    '*.aliyun.com': '*********'
    '*.jd.com': '************'
    '*.qq.com': '************'
    '*.tencent.com': '************'
    '*.weixin.com': '************'
    '*.bilibili.com': '************'
    '*.netease.com': '************'
    '*.mi.com': '************'
    '*.xiaomi.com': '************'

proxies:
  - { name: Trojan-Go-DMIT, type: trojan, server: couldyourpleasetellmeyourai-9527.online, port: 443, password: "hxPHGUBSPAqiJAmAtKmPZRgotJTHnV5eRfqQMl52o9M=", udp: true, sni: couldyourpleasetellmeyourai-9527.online, network: ws, tfo: true, ws-opts: { path: /api/ws, headers: { Host: "couldyourpleasetellmeyourai-9527.online" } } }
  - { name: Relay-HK1, server: liam.monolink.net, port: 995, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-HK2, server: noah.monolink.net, port: 996, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-HK3, server: cn3.monolink.net, port: 998, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-HK4, server: cn4.monolink.net, port: 179, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-JP1, server: olivia.monolink.net, port: 995, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-JP2, server: emma.monolink.net, port: 996, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-US1, server: jessica.monolink.net, port: 587, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-US2, server: jessica.monolink.net, port: 996, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-TW1, server: ryan.monolink.net, port: 590, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-TW2, server: ryan.monolink.net, port: 995, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-SG1, server: cn11.monolink.net, port: 990, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: Relay-SG2, server: cn11.monolink.net, port: 991, type: ss, cipher: chacha20-ietf-poly1305, password: 5yPFpIEtt6yx, udp: true }
  - { name: BACKUP-TW1, server: hinetddns1.aikunapp.com, port: 6001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: YMogq69bGOfRfSYS.cn, udp: true }
  - { name: BACKUP-HK2, server: v4-aws-hk.aikunapp.com, port: 7001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: d1--cn-gotcha204-3.bilivideo.com, udp: true }
  - { name: BACKUP-HK3, server: v6-aws-hk.aikunapp.com, port: 7001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: d1--cn-gotcha204-3.bilivideo.com, udp: true }
  - { name: BACKUP-JP2, server: v4-aws-jp.aikunapp.com, port: 7001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: d1--cn-gotcha204-3.bilivideo.com, udp: true }
  - { name: BACKUP-JP3, server: v6-aws-jp.aikunapp.com, port: 7001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: d1--cn-gotcha204-3.bilivideo.com, udp: true }
  - { name: BACKUP-SG2, server: v4-aws-sg.aikunapp.com, port: 7001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: d1--cn-gotcha204-3.bilivideo.com, udp: true }
  - { name: BACKUP-SG3, server: v6-aws-sg.aikunapp.com, port: 7001, type: trojan, password: 57597fd7-19f1-4815-9b8a-89131411094e, tfo: true, "skip-cert-verify": true, sni: d1--cn-gotcha204-3.bilivideo.com, udp: true }

proxy-groups:
  - name: "🗳️ 节点指定"
    type: select
    proxies:
      - "💎 自动选择"
      - DIRECT
      - Relay-HK2
      - Relay-HK3
      - Relay-HK1
      - Relay-HK4
      - Trojan-Go-DMIT
      - Relay-SG1
      - Relay-SG2
      - Relay-JP1
      - Relay-JP2
      - Relay-TW1
      - Relay-TW2
      - Relay-US1
      - Relay-US2
      - BACKUP-HK2
      - BACKUP-HK3
      - BACKUP-JP2
      - BACKUP-JP3
      - BACKUP-SG2
      - BACKUP-TW1
  - name: "💎 自动选择"
    type: url-test
    url: http://www.gstatic.com/generate_204
    interval: 300
    timeout: 5
    proxies:
      - Relay-HK2
      - Relay-HK3
      - Relay-HK1
      - Relay-HK4
      - Relay-SG1
      - Relay-SG2
      - BACKUP-HK2
      - BACKUP-HK3
  - name: "✨ Claude"
    type: select
    proxies:
      - Trojan-Go-DMIT
      - BACKUP-TW1
  - name: "✨ Cursor"
    type: select
    proxies:
      - Relay-HK2
      - Relay-HK1
      - Relay-HK3
      - Relay-HK4
      - Relay-SG1
      - Relay-SG2
      - Relay-US1
      - Relay-US2
      - Relay-JP1
      - Relay-JP2   
      - "💎 自动选择"
  - name: "✨ ChatGemini"
    type: select
    proxies:
      - Relay-SG1
      - Relay-SG2
      - Trojan-Go-DMIT
      - Relay-US1
      - Relay-US2
      - Relay-JP1
      - Relay-JP2 
      - "💎 自动选择"
  - name: "✨ Grok"
    type: select
    proxies:
      - Relay-HK2
      - Relay-HK1
      - Relay-HK3
      - Relay-HK4
      - Relay-SG1
      - Relay-SG2
      - Relay-US1
      - Relay-US2
      - Relay-JP1
      - Relay-JP2
      - "💎 自动选择"
  - name: "✨ Trae"
    type: select
    proxies:
      - DIRECT
      - Relay-HK2
      - Relay-HK1
      - Relay-HK3
      - Relay-HK4
      - Relay-SG1
      - Relay-SG2
      - Relay-US1
      - Relay-US2
      - Relay-JP1
      - Relay-JP2
      - "💎 自动选择"
  - name: "🇯🇵 日本"
    type: select
    proxies:
      - BACKUP-JP2
      - BACKUP-JP3
      - Relay-JP1
      - Relay-JP2
  - name: "😶‍🌫️ SIS"
    type: select
    proxies:  
      - BACKUP-HK2
      - BACKUP-HK3
      - Relay-HK4
      - Relay-HK3
      - Relay-HK2
      - Relay-HK1
      - Relay-SG1
      - Relay-SG2
      - Relay-JP1
      - Relay-JP2
      - Relay-TW1
      - Relay-TW2
      - Relay-US1
      - Relay-US2
  - name: "🎮 Steam"
    type: select
    proxies:
      - DIRECT
      - "🗳️ 节点指定"
      - "💎 自动选择"
      - Relay-HK3
      - Relay-HK2
      - Relay-HK1
      - Relay-HK4
      - Relay-SG1
      - Relay-SG2
      - Relay-JP1
      - Relay-JP2
      - Relay-TW1
      - Relay-TW2
      - Relay-US1
      - Relay-US2
  - name: "🐟 漏网之鱼"
    type: select
    proxies:
      - "💎 自动选择"
  - name: "📹 GooglePhoto"
    type: select
    proxies:
      - BACKUP-HK2
      - BACKUP-HK3
      - Relay-US1
      - Relay-US2
      - Relay-SG1
      - Relay-SG2
      - Relay-JP1
      - Relay-JP2

rule-providers:
  private:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/private.txt"
    path: ./ruleset/private.txt
    interval: 86400
  icloud:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/icloud.txt"
    path: ./ruleset/icloud.txt
    interval: 86400
  google:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/google.txt"
    path: ./ruleset/google.txt
    interval: 86400
  proxy:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/proxy.txt"
    path: ./ruleset/proxy.txt
    interval: 86400
  direct:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/direct.txt"
    path: ./ruleset/direct.txt
    interval: 86400
  telegramcidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/telegramcidr.txt"
    path: ./ruleset/telegramcidr.txt
    interval: 86400
  cncidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/cncidr.txt"
    path: ./ruleset/cncidr.txt
    interval: 86400
  "tieba-ad":
    type: http
    behavior: domain
    url: "https://raw.githubusercontent.com/app2smile/rules/master/rule/tieba-ad.list"
    path: ./ruleset/tieba-ad.txt
    interval: 86400

rules:
  # —— 院校与内网直连 ——
  - 'DOMAIN-SUFFIX,edu.cn,DIRECT'
  - 'DOMAIN,code.gac.zoco.cc,DIRECT'
  - 'DOMAIN,share.aikeji.vip,DIRECT'
  # —— 工具类 API ——
  - 'DOMAIN,api.deepseek.com,DIRECT'
  - 'DOMAIN-SUFFIX,trae.ai,✨ Trae'
  # —— 大模型服务 ——
  # # Claude
  - 'DOMAIN,statsig.anthropic.com,✨ Claude'
  - 'DOMAIN,api.anthropic.com,✨ Claude'
  - 'DOMAIN-SUFFIX,anthropic.com,✨ Claude'
  - 'DOMAIN-SUFFIX,usefathom.com,✨ Claude'
  - 'DOMAIN-SUFFIX,claude.com,✨ Claude'
  - 'DOMAIN-SUFFIX,claudecode.com,✨ Claude'
  - 'DOMAIN-SUFFIX,claude.ai,✨ Claude'
  - 'DOMAIN-KEYWORD,intercom,✨ Claude'
  - 'PROCESS-NAME,Claude,✨ Claude'
  - 'PROCESS-NAME,Claude Desktop,✨ Claude'
  - 'DOMAIN-SUFFIX,demo.fuclaude.com,✨ Claude'
  # # Cursor
  - 'DOMAIN-KEYWORD,cursor,✨ Cursor'
  - 'PROCESS-NAME,Cursor,✨ Cursor'
  - 'PROCESS-NAME,Cursor Helper,✨ Cursor'
  # # Google
  - 'DOMAIN,www.google.com,💎 自动选择'
  - 'DOMAIN-KEYWORD,themoviedb,💎 自动选择'
  # # ChatGemini / OpenAI
  - 'DOMAIN-SUFFIX,openai.com,✨ ChatGemini'
  - 'DOMAIN-SUFFIX,chatgpt.com,✨ ChatGemini'
  - 'DOMAIN-SUFFIX,statsigapi.net,✨ ChatGemini'
  - 'DOMAIN-SUFFIX,google.com,✨ ChatGemini'
  - 'DOMAIN-SUFFIX,googleapis.com,✨ ChatGemini'
  - 'DOMAIN-SUFFIX,gstatic.com,✨ ChatGemini'
  - 'DOMAIN-SUFFIX,sora.com,✨ ChatGemini'
  - 'DOMAIN,v0.dev,✨ ChatGemini'
  # # Grok
  - 'DOMAIN-SUFFIX,grok.com,✨ Grok'
  # # SIS
  - 'DOMAIN-SUFFIX,sis.com,😶‍🌫️ SIS'
  - 'DOMAIN-SUFFIX,redd.it,😶‍🌫️ SIS'
  - 'DOMAIN-SUFFIX,reddxx.com,😶‍🌫️ SIS'
  - 'DOMAIN-SUFFIX,reddxxx.com,😶‍🌫️ SIS'
  # —— 深度直连 ——
  - 'DOMAIN-SUFFIX,maj-soul.com,DIRECT'
  - 'DOMAIN-SUFFIX,click.simba.taobao.com,DIRECT'
  - 'DOMAIN-SUFFIX,d-gm.mmstat.com,DIRECT'
  - 'DOMAIN-SUFFIX,hydra.cab,DIRECT'
  - 'DOMAIN-SUFFIX,et8.org,DIRECT'
  # —— 娱乐与媒体 ——
  - 'DOMAIN-SUFFIX,steam.com,🎮 Steam'
  - 'DOMAIN-SUFFIX,steamcontent.com,🎮 Steam'
  - 'DOMAIN-SUFFIX,steamstatic.com,🎮 Steam'
  # —— 日本区域 ——
  - 'DOMAIN-SUFFIX,abema.tv,🇯🇵 日本'
  - 'DOMAIN-SUFFIX,dmm.co.jp,🇯🇵 日本'
  - 'DOMAIN-KEYWORD,datearisa,🇯🇵 日本'
  - 'DOMAIN-KEYWORD,twitcasting,🇯🇵 日本'
  - 'DOMAIN-KEYWORD,hermes,🇯🇵 日本'
  - 'DOMAIN-KEYWORD,jav,🇯🇵 日本'
  # —— “漏网之鱼” ——
  - 'DOMAIN-KEYWORD,javdb,🗳️ 节点指定'
  - 'DOMAIN-KEYWORD,openrice,🗳️ 节点指定'
  - 'DOMAIN,www.dzmm.ai,🗳️ 节点指定'
  - 'DOMAIN,gofrp.org,🗳️ 节点指定'
  # —— Google Photo ——
  - 'DOMAIN-SUFFIX,photos.googleapis.com,📹 GooglePhoto'
  # —— 区域派发 ——
  - 'DOMAIN-SUFFIX,jp,🇯🇵 日本'
  - 'DOMAIN-SUFFIX,tw,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,hk,🗳️ 节点指定'
  # —— GFW 旁路 ——
  - 'DOMAIN-KEYWORD,facebook,🗳️ 节点指定'
  - 'DOMAIN-KEYWORD,google,🗳️ 节点指定'
  - 'DOMAIN-KEYWORD,instagram,🗳️ 节点指定'
  - 'DOMAIN-KEYWORD,twitter,🗳️ 节点指定'
  - 'DOMAIN-KEYWORD,youtube,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,appspot.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,t.co,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,twimg.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,whatsapp.net,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,googleadservices.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,doubleclick.net,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,lightnode.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,weatherapi.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,readpai.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,comicbox.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,fosshub.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,ankiweb.com,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,linux.do,🗳️ 节点指定'
  - 'DOMAIN-SUFFIX,v2ex.com,🗳️ 节点指定'
  # —— 应用直连 ——
  - 'DOMAIN-KEYWORD,gitee,DIRECT'
  - 'DOMAIN-KEYWORD,wechat,DIRECT'
  # —— 进程名直连 ——
  - 'PROCESS-NAME,v2ray,DIRECT'
  - 'PROCESS-NAME,xray,DIRECT'
  - 'PROCESS-NAME,clash,DIRECT'
  - 'PROCESS-NAME,naive,DIRECT'
  - 'PROCESS-NAME,trojan,DIRECT'
  - 'PROCESS-NAME,trojan-go,DIRECT'
  - 'PROCESS-NAME,ss-local,DIRECT'
  - 'PROCESS-NAME,privoxy,DIRECT'
  - 'PROCESS-NAME,leaf,DIRECT'
  - 'PROCESS-NAME,Thunder,DIRECT'
  - 'PROCESS-NAME,DownloadService,DIRECT'
  - 'PROCESS-NAME,qBittorrent,DIRECT'
  - 'PROCESS-NAME,Transmission,DIRECT'
  - 'PROCESS-NAME,fdm,DIRECT'
  - 'PROCESS-NAME,aria2c,DIRECT'
  - 'PROCESS-NAME,Folx,DIRECT'
  # —— 最终与本地 ——
  - 'GEOIP,CN,DIRECT'
  - 'MATCH,🐟 漏网之鱼'
