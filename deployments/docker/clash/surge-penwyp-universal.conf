[General]
http-api = nas_surge_automator@127.0.0.1:6171
loglevel = info
ipv6 = true
bypass-system = true
skip-proxy = 127.0.0.1,***********/16,10.0.0.0/8,**********/12,**********/10,localhost,*.local,e.crashlytics.com,captive.apple.com,::ffff:0:0:0:0/1,::ffff:128:0:0:0/1
# DNS设置或根据自己网络情况进行相应设置
bypass-tun = ***********/16,10.0.0.0/8,**********/12
test-timeout = 5
http-api-web-dashboard = true
use-local-host-item-for-proxy = true
dns-server = ************,*********,***************, system

[Proxy]
Trojan-Go-DMIT = trojan, couldyourpleasetellmeyourai-9527.online, 443, password="hxPHGUBSPAqiJAmAtKmPZRgotJTHnV5eRfqQMl52o9M=", ws=true, ws-path=/api/ws, ws-headers=Host:"couldyourpleasetellmeyourai-9527.online", tfo=true, sni=couldyourpleasetellmeyourai-9527.online
Relay-HK1 = ss, liam.monolink.net, 995, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-HK2 = ss, noah.monolink.net, 996, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-HK3 = ss, cn3.monolink.net, 998, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-HK4 = ss, cn4.monolink.net, 179, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-JP1 = ss, olivia.monolink.net, 995, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-JP2 = ss, emma.monolink.net, 996, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-US1 = ss, jessica.monolink.net, 587, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-US2 = ss, jessica.monolink.net, 996, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-TW1 = ss, ryan.monolink.net, 590, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-TW2 = ss, ryan.monolink.net, 995, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-SG1 = ss, cn11.monolink.net, 990, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
Relay-SG2 = ss, cn11.monolink.net, 991, encrypt-method=chacha20-ietf-poly1305, password=5yPFpIEtt6yx, udp-relay=true
BACKUP-TW1 = trojan, hinetddns1.aikunapp.com, 6001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=YMogq69bGOfRfSYS.cn
BACKUP-TW2 = hysteria2, hinetddns1.aikunapp.com, 5001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=xuexi.cn
BACKUP-HK1 = hysteria2, v4-aws-hk.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-HK2 = trojan, v4-aws-hk.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-HK3 = trojan, v6-aws-hk.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-HK4 = hysteria2, v6-aws-hk.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-JP1 = hysteria2, v4-aws-jp.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-JP2 = trojan, v4-aws-jp.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-JP3 = trojan, v6-aws-jp.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-JP4 = hysteria2, v6-aws-jp.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-SG1 = hysteria2, v4-aws-sg.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-SG2 = trojan, v4-aws-sg.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-SG3 = trojan, v6-aws-sg.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, tfo=true, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com
BACKUP-SG4 = hysteria2, v6-aws-sg.aikunapp.com, 7001, password=57597fd7-19f1-4815-9b8a-89131411094e, skip-cert-verify=true, sni=d1--cn-gotcha204-3.bilivideo.com

[Proxy Group]
🗳️ 节点指定 = select, "💎 自动选择", DIRECT, Relay-HK2, Relay-HK3, Relay-HK1, Relay-HK4, Trojan-Go-DMIT, Relay-SG1, Relay-SG2, Relay-JP1, Relay-JP2, Relay-TW1, Relay-TW2, Relay-US1, Relay-US2, BACKUP-HK1, BACKUP-HK2, BACKUP-HK3, BACKUP-HK4, BACKUP-JP1, BACKUP-JP2, BACKUP-JP3, BACKUP-JP4, BACKUP-SG1, BACKUP-SG2, BACKUP-SG3, BACKUP-SG4, BACKUP-TW1, BACKUP-TW2
💎 自动选择 = url-test, Relay-HK2, Relay-HK3, Relay-HK1, Relay-HK4, Relay-SG1, Relay-SG2, BACKUP-HK1, BACKUP-HK2, BACKUP-HK3, BACKUP-HK4, url=http://www.gstatic.com/generate_204, interval=300, timeout=5, include-all-proxies=0
✨ Claude = select, Trojan-Go-DMIT, BACKUP-TW1, BACKUP-TW2, BACKUP-JP1, BACKUP-SG1
✨ Cursor = select, Relay-HK2, Relay-HK1, Relay-HK3, Relay-HK4, Relay-SG1, Relay-SG2, Relay-US1, Relay-US2, Relay-JP1, Relay-JP2, BACKUP-TW2, "💎 自动选择"
✨ ChatGemini = select, Relay-SG1, Relay-SG2, Trojan-Go-DMIT, Relay-US1, Relay-US2, Relay-JP1, Relay-JP2, BACKUP-TW2, "💎 自动选择"
✨ Grok = select, Relay-HK2, Relay-HK1, Relay-HK3, Relay-HK4, Relay-SG1, Relay-SG2, Relay-US1, Relay-US2, Relay-JP1, Relay-JP2, BACKUP-TW2, "💎 自动选择"
✨ Trae = select, DIRECT, Relay-HK2, Relay-HK1, Relay-HK3, Relay-HK4, Relay-SG1, Relay-SG2, Relay-US1, Relay-US2, Relay-JP1, Relay-JP2, BACKUP-TW2, "💎 自动选择"
🇯🇵 日本 = select, BACKUP-JP1, BACKUP-JP2, BACKUP-JP3, BACKUP-JP4, Relay-JP1, Relay-JP2
😶‍🌫️ SIS = select, BACKUP-TW2, BACKUP-HK1, BACKUP-HK2, BACKUP-HK3, BACKUP-HK4, BACKUP-JP1, BACKUP-SG1, Relay-HK4, Relay-HK3, Relay-HK2, Relay-HK1, Relay-SG1, Relay-SG2, Relay-JP1, Relay-JP2, Relay-TW1, Relay-TW2, Relay-US1, Relay-US2
🎮 Steam = select, DIRECT, "🗳️ 节点指定", "💎 自动选择", Relay-HK3, Relay-HK2, Relay-HK1, Relay-HK4, Relay-SG1, Relay-SG2, Relay-JP1, Relay-JP2, Relay-TW1, Relay-TW2, Relay-US1, Relay-US2
🐟 漏网之鱼 = select, DIRECT, "💎 自动选择", BACKUP-TW2, BACKUP-HK1, BACKUP-JP1, BACKUP-SG1
📹 GooglePhoto = select, BACKUP-HK1, BACKUP-HK2, BACKUP-HK3, BACKUP-HK4, BACKUP-TW2, BACKUP-SG1, BACKUP-SG2, BACKUP-SG3, BACKUP-SG4, Relay-US1, Relay-US2, Relay-SG1, Relay-SG2, Relay-JP1, Relay-JP2

[Rule]

# —— 院校与内网直连 ——
DOMAIN-SUFFIX,edu.cn,DIRECT
DOMAIN,code.gac.zoco.cc,DIRECT
DOMAIN,share.aikeji.vip,DIRECT

# —— 工具类 API ——
DOMAIN,api.deepseek.com,DIRECT
DOMAIN-SUFFIX,trae.ai,"✨ Trae"

# —— 大模型服务 ——
# # Claude
DOMAIN,statsig.anthropic.com,"✨ Claude"
DOMAIN,api.anthropic.com,"✨ Claude"
DOMAIN-SUFFIX,anthropic.com,"✨ Claude"
DOMAIN-SUFFIX,usefathom.com,"✨ Claude"
DOMAIN-SUFFIX,claude.com,"✨ Claude"
DOMAIN-SUFFIX,claudecode.com,"✨ Claude"
DOMAIN-SUFFIX,claude.ai,"✨ Claude"
DOMAIN-KEYWORD,intercom,"✨ Claude"
PROCESS-NAME,Claude,"✨ Claude"
PROCESS-NAME,"Claude Desktop","✨ Claude"
DOMAIN-SUFFIX,demo.fuclaude.com,"✨ Claude"

# # Cursor
DOMAIN-KEYWORD,cursor,"✨ Cursor"
PROCESS-NAME,Cursor,"✨ Cursor"
PROCESS-NAME,"Cursor Helper","✨ Cursor"

# # Google
DOMAIN,www.google.com,"💎 自动选择"


# # ChatGemini / OpenAI
DOMAIN-SUFFIX,openai.com,"✨ ChatGemini"
DOMAIN-SUFFIX,chatgpt.com,"✨ ChatGemini"
DOMAIN-SUFFIX,statsigapi.net,"✨ ChatGemini"
DOMAIN-SUFFIX,google.com,"✨ ChatGemini"
DOMAIN-SUFFIX,googleapis.com,"✨ ChatGemini"
DOMAIN-SUFFIX,gstatic.com,"✨ ChatGemini"
DOMAIN-SUFFIX,sora.com,"✨ ChatGemini"
DOMAIN,v0.dev,"✨ ChatGemini"


# # Grok
DOMAIN-SUFFIX,grok.com,"✨ Grok"

# # SIS
DOMAIN-SUFFIX,sis.com,"😶‍🌫️ SIS"
DOMAIN-SUFFIX,redd.it,"😶‍🌫️ SIS"
DOMAIN-SUFFIX,reddxx.com,"😶‍🌫️ SIS"
DOMAIN-SUFFIX,reddxxx.com,"😶‍🌫️ SIS"


# —— 深度直连 ——
DOMAIN-SUFFIX,maj-soul.com,DIRECT
DOMAIN-SUFFIX,click.simba.taobao.com,DIRECT
DOMAIN-SUFFIX,d-gm.mmstat.com,DIRECT
DOMAIN-SUFFIX,hydra.cab,DIRECT
DOMAIN-SUFFIX,et8.org,DIRECT

# —— 娱乐与媒体 ——
DOMAIN-SUFFIX,steam.com,"🎮 Steam"
DOMAIN-SUFFIX,steamcontent.com,"🎮 Steam"
DOMAIN-SUFFIX,steamstatic.com,"🎮 Steam"

# —— 日本区域 ——
DOMAIN-SUFFIX,abema.tv,"🇯🇵 日本"
DOMAIN-SUFFIX,dmm.co.jp,"🇯🇵 日本"
DOMAIN-KEYWORD,datearisa,"🇯🇵 日本"
DOMAIN-KEYWORD,twitcasting,"🇯🇵 日本"
DOMAIN-KEYWORD,hermes,"🇯🇵 日本"
DOMAIN-KEYWORD,jav,"🇯🇵 日本"

# —— “漏网之鱼” ——
DOMAIN-KEYWORD,javdb,"🗳️ 节点指定"
DOMAIN-KEYWORD,openrice,"🗳️ 节点指定"
DOMAIN,www.dzmm.ai,"🗳️ 节点指定"
DOMAIN,gofrp.org,"🗳️ 节点指定"

# —— Google Photo ——
DOMAIN-SUFFIX,photos.googleapis.com,"📹 GooglePhoto"

# —— 区域派发 ——
DOMAIN-SUFFIX,jp,"🇯🇵 日本"
DOMAIN-SUFFIX,tw,"🗳️ 节点指定"
DOMAIN-SUFFIX,hk,"🗳️ 节点指定"

# —— GFW 旁路 ——
DOMAIN-KEYWORD,facebook,"🗳️ 节点指定"
DOMAIN-KEYWORD,google,"🗳️ 节点指定"
DOMAIN-KEYWORD,instagram,"🗳️ 节点指定"
DOMAIN-KEYWORD,twitter,"🗳️ 节点指定"
DOMAIN-KEYWORD,youtube,"🗳️ 节点指定"
DOMAIN-SUFFIX,appspot.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,t.co,"🗳️ 节点指定"
DOMAIN-SUFFIX,twimg.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,whatsapp.net,"🗳️ 节点指定"
DOMAIN-SUFFIX,googleadservices.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,doubleclick.net,"🗳️ 节点指定"
DOMAIN-SUFFIX,lightnode.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,weatherapi.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,readpai.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,comicbox.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,fosshub.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,ankiweb.com,"🗳️ 节点指定"
DOMAIN-SUFFIX,linux.do,"🗳️ 节点指定"
DOMAIN-SUFFIX,v2ex.com,"🗳️ 节点指定"

# —— 应用直连 ——
DOMAIN-KEYWORD,gitee,DIRECT
DOMAIN-KEYWORD,wechat,DIRECT

# —— 进程名直连 ——
PROCESS-NAME,v2ray,DIRECT
PROCESS-NAME,xray,DIRECT
PROCESS-NAME,clash,DIRECT
PROCESS-NAME,naive,DIRECT
PROCESS-NAME,trojan,DIRECT
PROCESS-NAME,trojan-go,DIRECT
PROCESS-NAME,ss-local,DIRECT
PROCESS-NAME,privoxy,DIRECT
PROCESS-NAME,leaf,DIRECT
PROCESS-NAME,Thunder,DIRECT
PROCESS-NAME,DownloadService,DIRECT
PROCESS-NAME,qBittorrent,DIRECT
PROCESS-NAME,Transmission,DIRECT
PROCESS-NAME,fdm,DIRECT
PROCESS-NAME,aria2c,DIRECT
PROCESS-NAME,Folx,DIRECT

# —— 订阅规则 ——
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/private.txt,DIRECT,"update-interval=86400"
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/icloud.txt,DIRECT,"update-interval=86400"
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/google.txt,"🗳️ 节点指定","update-interval=86400"
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/proxy.txt,"🗳️ 节点指定","update-interval=86400"
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/direct.txt,DIRECT,"update-interval=86400"
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/telegramcidr.txt,"🗳️ 节点指定","update-interval=86400"
RULE-SET,https://cdn.jsdelivr.net/gh/Loyalsoldier/surge-rules@release/ruleset/cncidr.txt,DIRECT,"update-interval=86400"
RULE-SET,https://raw.githubusercontent.com/app2smile/rules/master/rule/tieba-ad.list,REJECT-DROP

# —— 最终与本地 ——
RULE-SET,SYSTEM,DIRECT
RULE-SET,LAN,DIRECT
GEOIP,CN,DIRECT
FINAL,🐟 漏网之鱼

[Host]
# 主机名映射
*.taobao.com = server:*********
*.tmall.com = server:*********
*.alipay.com = server:*********
*.alicdn.com = server:*********
*.aliyun.com = server:*********
*.jd.com = server:************
*.qq.com = server:************
*.tencent.com = server:************
*.weixin.com = server:************
*.bilibili.com = server:************
*.netease.com = server:************
*.mi.com = server:************
*.xiaomi.com = server:************

[URL Rewrite]
^https?://(www.)?(g|google)\.cn https://www.google.com 302

[MITM]
hostname = *.google.cn, *.googlevideo.com, *.qinlinkeji.com
ca-passphrase = 481B4209
ca-p12 = 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
