
natter-emby302:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-emby302
  restart: always
  network_mode: host
  privileged: true
  environment:
    - TZ=Asia/Shanghai
  command: -t ************* -p 8096 -m gost -e /root/natter_hook.sh
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock
    - /usr/local/bin/docker:/usr/local/bin/docker
    - /volume2/service/bin/emby302/natter_hook.sh:/root/natter_hook.sh
    - /volume2/service/bin/emby302/nginx_template.conf:/volume2/service/bin/emby302/nginx_template.conf
    - /volume2/service/bin/emby302/nginx.conf:/volume2/service/bin/emby302/nginx.conf
  depends_on:
    - emby302
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-embyx302:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-embyx302
  restart: always
  network_mode: host
  privileged: true
  environment:
    - TZ=Asia/Shanghai
  command: -t ************* -p 8096 -m gost -e /root/natter_hook.sh
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock
    - /usr/local/bin/docker:/usr/local/bin/docker
    - /volume2/service/bin/embyx302/natter_hook.sh:/root/natter_hook.sh
    - /volume2/service/bin/embyx302/nginx_template.conf:/volume2/service/bin/embyx302/nginx_template.conf
    - /volume2/service/bin/embyx302/nginx.conf:/volume2/service/bin/embyx302/nginx.conf
  depends_on:
    - embyx
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-mp2:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-mp2
  restart: always
  network_mode: host
  privileged: true
  environment:
    - TZ=Asia/Shanghai
  command: -t ************* -p 3000 -m gost -e /root/natter_hook.sh
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock
    - /usr/local/bin/docker:/usr/local/bin/docker
    - /volume2/service/bin/mp2/natter_hook.sh:/root/natter_hook.sh
    - /volume2/service/bin/mp2/nginx_template.conf:/volume2/service/bin/mp2/nginx_template.conf
    - /volume2/service/bin/mp2/nginx.conf:/volume2/service/bin/mp2/nginx.conf
  depends_on:
    - mp2
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-qb:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-qb
  restart: always
  privileged: true
  cap_add:
    - NET_ADMIN
    - NET_RAW
  network_mode: host
  environment:
    - TZ=Asia/Shanghai
  command: -t ************* -m gost -e /root/natter_hook.sh -r
  volumes:
    - /volume2/service/bin/natter/qb/natter_hook.sh:/root/natter_hook.sh
  depends_on:
    - qb
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-synology302:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-synology302
  restart: always
  network_mode: host
  privileged: true
  environment:
    - TZ=Asia/Shanghai
  command: -t *********** -p 15000 -m gost -e /root/natter_hook.sh
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock
    - /usr/local/bin/docker:/usr/local/bin/docker
    - /volume2/service/bin/synology302/natter_hook.sh:/root/natter_hook.sh
    - /volume2/service/bin/synology302/nginx_template.conf:/volume2/service/bin/synology302/nginx_template.conf
    - /volume2/service/bin/synology302/nginx.conf:/volume2/service/bin/synology302/nginx.conf
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-webdav302:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-webdav302
  restart: always
  network_mode: host
  privileged: true
  environment:
    - TZ=Asia/Shanghai
  command: -t *********** -p 5005 -m gost -e /root/natter_hook.sh
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock
    - /usr/local/bin/docker:/usr/local/bin/docker
    - /volume2/service/bin/webdav302/natter_hook.sh:/root/natter_hook.sh
    - /volume2/service/bin/webdav302/nginx_template.conf:/volume2/service/bin/webdav302/nginx_template.conf
    - /volume2/service/bin/webdav302/nginx.conf:/volume2/service/bin/webdav302/nginx.conf
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-tr:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-tr
  restart: always
  privileged: true
  cap_add:
    - NET_ADMIN
    - NET_RAW
  network_mode: host
  environment:
    - TZ=Asia/Shanghai
  command: -t ************* -m gost -e /root/natter_hook.sh -r
  volumes:
    - /volume2/service/bin/natter/tr/natter_hook.sh:/root/natter_hook.sh
  depends_on:
    - tr
  logging:
    driver: "json-file"
    options:
      max-size: "20m"

natter-trsmall:
  image: registry.cn-hangzhou.aliyuncs.com/coppkaw/natter:latest
  container_name: natter-trsmall
  restart: always
  privileged: true
  cap_add:
    - NET_ADMIN
    - NET_RAW
  network_mode: host
  environment:
    - TZ=Asia/Shanghai
  command: -t ************* -m gost -e /root/natter_hook.sh -r
  volumes:
    - /volume2/service/bin/natter/trsmall/natter_hook.sh:/root/natter_hook.sh
  depends_on:
    - trsmall
  logging:
    driver: "json-file"
    options:
      max-size: "20m"
