server {
    # 监听域名和端口
    listen 80;
    server_name *********** *********** *********** OUTBOUND_IP;

    location / {
        # 判断来源IP是否为相同公网IP，是的话则认为是内网访问
        if ($http_x_forwarded_for = "OUTBOUND_IP") {
            return 307 http://*************:8096$request_uri;
        }
        # 使用 307 Temporary Redirect 保留请求方法和路径
        return 307 http://OUTBOUND_IP:OUTBOUND_PORT$request_uri;
    }

    # 跨域支持（如有必要）
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH';
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Api-Key, X-Api-Secret';
}