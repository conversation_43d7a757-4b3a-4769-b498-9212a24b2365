version: "3.8"
services:
  qb:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/qbittorrent:4.6.5
    container_name: qb
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::101
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - WEBUI_PORT=8085
    devices:
      - /dev/fuse:/dev/fuse
    volumes:
      - /volume2/service/bin/qbittorrent/config:/config
      - /volume2:/volume2
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  hydra:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/hydra:latest
    container_name: hydra
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::110
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - GIN_MODE=release
    devices:
      - /dev/fuse:/dev/fuse
    volumes:
      - /volume2/service/bin/hydra-docker:/config
      - /volume2:/volume2
      - /volume2/service/bin/hydra/hydra:/root/hydra
    command: /root/hydra hook emby_av
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  hydra-chown:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/hydra:latest
    container_name: hydra-chown
    privileged: true
    restart: always
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - GIN_MODE=release
    devices:
      - /dev/fuse:/dev/fuse
    volumes:
      - /volume2:/volume2
      - /volume2/service/bin/hydra/hydra:/root/hydra
      - /etc/passwd:/etc/passwd:ro
    command: /root/hydra chown
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  tiver-serve:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/hydra:latest
    container_name: tiver-serve
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - GIN_MODE=release
    ports:
      - target: 8030
        published: 8030
        protocol: tcp
    volumes:
      - /volume2:/volume2
      - /volume2/service/bin/tiver/tiver:/root/tiver
      - /volume2/service/bin/tiver/web:/root/web
      - /etc/passwd:/etc/passwd:ro
    command: /root/tiver serve -w /root/web
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  hydra-ddns:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/hydra:latest
    container_name: hydra-ddns
    privileged: true
    restart: always
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - GIN_MODE=release
    devices:
      - /dev/fuse:/dev/fuse
    volumes:
      - /volume2:/volume2
      - /volume2/service/bin/hydra/hydra:/root/hydra
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/local/bin/docker:/usr/local/bin/docker
      - /etc/passwd:/etc/passwd:ro
    command: /root/hydra ddns
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  cookiecloud:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/cookiecloud:latest
    container_name: cookiecloud
    privileged: true
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::105
    restart: always
    environment:
      - TZ=Asia/Shanghai
      - API_ROOT=/cookies
    volumes:
      - /volume2/service/bin/cookiecloud/data:/data/api/data
    ports:
      - 8088:8088
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  clash:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/clash:latest
    container_name: clash
    privileged: true
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::107
    restart: always
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/dat/hydra/docker/clash/clash-monocloud.yaml:/root/.config/clash/config.yaml
    ports:
      - 7890:7890
      - 7891:7891
      - 9090:9090
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  adguardhome:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/adguardhome:latest
    container_name: adguardhome
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::106
    restart: always
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/adguardhome/data:/opt/adguardhome/work
      - /volume2/service/bin/adguardhome/conf:/opt/adguardhome/conf
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  redis:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/redis:latest
    container_name: redis
    environment:
      - TZ=Asia/Shanghai
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::124
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  yacd:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/yacd:latest
    container_name: yacd
    privileged: true
    restart: always
    environment:
      - TZ=Asia/Shanghai
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::125
    ports:
      - 80:80
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  mp2:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/moviepilot-v2:latest
    container_name: mp2
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::100
    hostname: mp2
    stdin_open: true
    tty: true
    ports:
      - target: 3000
        published: 3000
        protocol: tcp
      - target: 3001
        published: 3001
        protocol: tcp
    environment:
      # 基础设置
      - NGINX_PORT=3000
      - PORT=3001
      - PUID=0
      - PGID=0
      - UMASK=000
      - TZ=Asia/Shanghai
      # 缓存
      - CACHE_BACKEND_TYPE=cachetools
      # 代理/更新相关
      - PROXY_HOST=http://*************:7890
      - MOVIEPILOT_AUTO_UPDATE=release
      - AUTO_UPDATE_RESOURCE=true
      - GITHUB_TOKEN=****************************************
      # 用户认证
      - AUTH_SITE=iyuu,hhclub,hddolby,hdfans,discfan
      - HHCLUB_USERNAME=BlackBox
      - HHCLUB_PASSKEY=********************************
      - HDFANS_UID=29676
      - HDFANS_PASSKEY=ba238f2d74c24fb287f433604508a9b6
      - IYUU_SIGN=IYUU11853Tbe0ad23656600ef96a2aec7e6fc97922d94caf90
      - DISCFAN_UID=46269
      - DISCFAN_PASSKEY=a77276a5778da45f4da01667ba42117b
      - HAIDAN_ID=45846
      - HAIDAN_PASSKEY=cb1c54c97fbbe46fa490aa0966675134
      # 鉴权 / API
      - SUPERUSER=nas
      - SUPERUSER_PASSWORD=acce-s6se/nas
      - API_TOKEN=KrB0ac5t5zFEkfUSYgXpZQ
      # COOKIE
      - COOKIECLOUD_HOST=http://*************:8088/cookies
      - COOKIECLOUD_KEY=acce-s6se-uuid-cookiecloud
      - COOKIECLOUD_PASSWORD=acce-s6se-password-cookiecloud
      - COOKIECLOUD_INTERVAL=10
      - COOKIECLOUD_ENABLE_LOCAL=true
      - USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
    volumes:
      - /volume2:/volume2
      - /volume2/service/bin/moviepilot2/plugins/filescanner:/app/app/plugins/filescanner
      - /volume2/service/bin/moviepilot2/config:/config
      - /volume2/service/bin/moviepilot2/core:/moviepilot/.cache/ms-playwright
      - /volume2/service/bin/cookiecloud/data:/config/cookies/
      - /var/run/docker.sock:/var/run/docker.sock:ro
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  tr:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/transmission:4.0.5
    container_name: tr
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::102
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - USER=nas
      - PASS=acce-s6se/nas
      - TRANSMISSION_WEB_HOME=/ui/transmission-web-control
    volumes:
      - /volume2/service/bin/transmission/config:/config
      - /volume2/service/bin/transmission/ui/:/ui
      - /volume2/pt:/volume2/pt
      - /volume2:/volume2
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  trsmall:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/transmission:4.0.5
    container_name: trsmall
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::103
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - USER=nas
      - PASS=acce-s6se/nas
      - TRANSMISSION_WEB_HOME=/ui/transmission-web-control
    volumes:
      - /volume2/service/bin/transmission-small/config:/config
      - /volume2/service/bin/transmission/ui/:/ui
      - /volume2:/volume2
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  iyuuplus:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/iyuuplus:latest
    container_name: iyuuplus
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::104
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/iyuuplus/config:/IYUU/db
      - /volume2/pt:/volume2/pt
      - /volume2/pt/.qbcache/torrent:/volume2/@appstore/qBittorrent/qBittorrent_conf/data/BT_backup
      - /volume2/service/bin/transmission/config/torrents:/volume2/service/bin/transmission/config/torrents
    ports:
      - "8787:8787"
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  embyx:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/emby
    container_name: embyx
    privileged: true
    hostname: embyx
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250:42:c0ff:fea8:121
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/embyx/config:/config
      - /volume2/onlyhub/avis_japan:/data/日本电影
      - /volume2/onlyhub/deepfakes:/data/deepfakes
    ports:
      - "8096:8096"
      - "8920:8920"
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  ha:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/home-assistant:latest
    container_name: ha
    hostname: home-assistant
    privileged: true
    restart: always
    extra_hosts:
      - "api.io.mi.com:***************"
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::109
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - HTTP_PROXY=http://*************:7890
      - HTTPS_PROXY=http://*************:7890
    volumes:
      - /volume2/service/bin/home-assistant/config:/config
    ports:
      - '8123:8123'
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  emby_port_forward:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/socat
    container_name: emby_port_forward
    ports:
      - "8096:8096"
    command: TCP-LISTEN:8096,fork TCP:*************:8096
    restart: always

  emby:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/emby
    container_name: emby
    privileged: true
    restart: always
    hostname: emby
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250:42:c0ff:fea8:120
    environment:
      - UID=1034
      - GID=100
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/emby/config:/config
      - /volume2/theatre/R18:/data/theatre/R18
      - /volume2/theatre/动漫:/data/theatre/动漫
      - /volume2/theatre/演唱会:/data/theatre/演唱会
      - /volume2/theatre/电影:/data/theatre/电影
      - /volume2/theatre/电视剧:/data/theatre/电视剧
      - /volume2/theatre/电视节目:/data/theatre/电视节目
      - /volume2/theatre/纪录片:/data/theatre/纪录片
      - /volume2/onlyhub/avis_japan:/data/日本电影
      - /volume2/onlyhub/avis_hk:/data/香港电影
      - /etc/passwd:/etc/passwd:ro
    ports:
      - "8096:8096"
      - "8920:8920"
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  chinesesubfinder:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/chinesesubfinder:latest-lite
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::123
    volumes:
      - /volume2/service/bin/chinesesubfinder/config:/config
      - /volume2/theatre:/media/theatre
      - /volume2/pt:/media/pt
    environment:
      - PUID=0
      - PGID=0
      - PERMS=true
      - TZ=Asia/Shanghai
      - UMASK=022
    container_name: chinesesubfinder
    ports:
      - "19035:19035"
      - "19037:19037"
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  metatube:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/metatube-server:1.2.3
    container_name: metatube
    ports:
      - 7831:7831
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::108
    environment:
      - TZ=Asia/Shanghai
      - HTTP_PROXY=http://*************:7890
      - HTTPS_PROXY=http://*************:7890
      - TOKEN=blackbox_metatube_key
      - DSN=/config/metatube.db
    volumes:
      - /volume2/service/bin/metatube/config:/config
    command: -port 7831
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  emby302:
    image: quay.io/coppkaw/penwyp-nginx:latest
    container_name: emby302
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::130
    restart: always
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/emby302/nginx.conf:/etc/nginx/conf.d/default.conf
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  embyx302:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/penwyp-nginx:latest
    container_name: embyx302
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::131
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/embyx302/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - embyx
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  synology302:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/penwyp-nginx:latest
    container_name: synology302
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::132
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/synology302/nginx.conf:/etc/nginx/conf.d/default.conf
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  webdav302:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/penwyp-nginx:latest
    container_name: webdav302
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::136
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/webdav302/nginx.conf:/etc/nginx/conf.d/default.conf
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

  mp2-302:
    image: registry.cn-hangzhou.aliyuncs.com/coppkaw/penwyp-nginx:latest
    container_name: mp2-302
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
        ipv6_address: 240e:3bc:267:e250::135
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/mp2/nginx.conf:/etc/nginx/conf.d/default.conf
    logging:
      driver: "json-file"
      options:
        max-size: "20m"

networks:
  zte_macvlan:
    external: true