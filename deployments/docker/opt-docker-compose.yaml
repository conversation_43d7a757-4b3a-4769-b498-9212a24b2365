version: "3.7"
services:
  tachi:
    image: ghcr.io/suwayomi/tachidesk
    container_name: tachi
    privileged: true
    network_mode: host
    volumes:
      - /volume2/service/bin/tachidesk/config:/code/seedcross/db
    #    ports:
    #      - 8019:8019
    restart: always

  influxdb:
    image: influxdb:latest
    container_name: influxdb
    privileged: true
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=influxdb_nas
      - DOCKER_INFLUXDB_INIT_PASSWORD=qq1120btlovE
      - DOCKER_INFLUXDB_INIT_ORG=hydra.cab
      - DOCKER_INFLUXDB_INIT_BUCKET=hydra.cab
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=************************************
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/influxdb/data:/var/lib/influxdb2
      - /volume2/service/bin/influxdb/config:/etc/influxdb2
      - /volume2:/volume2
    #    ports:
    #      - 8086:8086 HTTP API port
    #      - 2003:2003 Graphite support, if it is enabled
    restart: always


  telegraf:
    image: telegraf:latest
    container_name: telegraf
    privileged: true
    network_mode: host
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/hydra/config-list.json:/etc/telegraf/telegraf.conf:ro
      - /volume2/service/bin/telegraf/bin/telegraf:/usr/bin/telegraf
      - /volume2/service/dat/hydra/docker/telegraf/entrypoint.sh:/code/entrypoint.sh
    #    ports:
    #      - 8086:8086 HTTP API port
    #      - 2003:2003 Graphite support, if it is enabled
    entrypoint: /code/entrypoint.sh
    restart: always

  jellyseerr:
    image: fallenbagel/jellyseerr:latest
    container_name: jellyseerr
    privileged: true
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - LOG_LEVEL=debug
      - JELLYFIN_TYPE=emby
    volumes:
      - /volume2/service/bin/jellyseerr/config:/app/config
    extra_hosts:
      - "api.themoviedb.org:*************"
      - "image.themoviedb.org:*************"
      - "api.themoviedb.org:***********"
      - "www.themoviedb.org:*************"
      - "api.thetvdb.com:*************"
      - "api.thetvdb.com:**************"
      - "api.thetvdb.com:*************"
      - "api.themoviedb.org:*************"
      - "api.themoviedb.org:***********"
      - "api.themoviedb.org:*************"
      - "api.themoviedb.org:*************"
      - "api.themoviedb.org:**************"
      - "api.themoviedb.org:***********"
      - "api.themoviedb.org:************"
      - "api.themoviedb.org:*************"
    ports:
      - "5055:5055"
    restart: always

  plex:
    image: linuxserver/plex:latest
    container_name: plex
    privileged: true
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - PUID=1000
      - PGID=1000
      - VERSION=docker
      - PLEX_CLAIM= #optional
    volumes:
      - /volume2/service/bin/plex/config:/config
      - /volume2/media-library:/volume2/media-library
    ports:
      - "8324:8324" # Http webUI.
      - "32400:32400" #optional  Https webUI (you need to set up your own certificate).
      - "3005:3005" #optional  Https webUI (you need to set up your own certificate).
    restart: always

  mysqltest:
    image: mysql:latest
    container_name: mysqltest
    hostname: mysqltest
    command: --default-authentication-plugin=mysql_native_password --server-id=1 --log-bin='mysql-bin-1.log' --enforce-gtid-consistency='ON' --binlog_transaction_dependency_tracking='WRITESET' --log-slave-updates='ON' --gtid-mode='ON' --transaction-write-set-extraction='XXHASH64' --binlog-checksum='NONE' --master-info-repository='TABLE' --relay-log-info-repository='TABLE' --plugin-load='group_replication.so' --relay-log-recovery='ON'
    privileged: true
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - MYSQL_ROOT_PASSWORD=localdba
      - MYSQL_DATABASE=test
    volumes:
      - /volume2/service/dat/hydra/docker/mysql/my.cnf:/etc/my.cnf
    ports:
      - '3306:3306'
      - '33060:33060'

  nginx:
    image: nginx
    container_name: nginx
    privileged: true
    networks:
      macvlanipv46:
        ipv4_address: *******
    volumes:
      - /volume2/service/bin/nginx/config/nginx.conf:/etc/nginx/nginx.conf:ro
      - /volume2/service/bin/nginx/config/index.html:/usr/share/nginx/html/index.html
    restart: unless-stopped

  subconverter:
    image: tindy2013/subconverter
    container_name: subconverter
    restart: always
    network_mode: host
    environment:
      - TZ=Asia/Shanghai
      - UMASK=022 # 掩码权限，默认000，可以考虑设置为022
    volumes:
      - /volume2/service/bin/subconverter/config:/config
    ports:
      - "25500:25500"

  talebook:
    image: talebook/talebook
    container_name: talebook
    restart: always
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/talebook/data:/data
      - /volume2/service/bin/calibre-web/config:/data/books/imports/
    #    ports:
    #      - 80:80

  seedcross:
    image: ccf2012/seedcross
    container_name: seedcross
    privileged: true
    restart: always
    network_mode: host
    volumes:
      - /volume2/service/bin/seedcross/config:/code/seedcross/db
    #    ports:
    #      - 8019:8019

  jackett:
    image: linuxserver/jackett:latest
    container_name: jackett
    privileged: true
    restart: always
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - AUTO_UPDATE=true #optional
    volumes:
      - /volume2/service/bin/jackett/config:/config  # Configuration directory
    ports:
      - "9117:9117"

  moviebot:
    image: yipengfei/movie-robot:latest
    container_name: moviebot
    network_mode: host
    environment:
      - TZ=Asia/Shanghai
      - LICENSE_KEY=QIiNnWtZuUXxYMmmvwuGDRDQBgLkWZ3F17UNLVgUVmlsOqplhg0OJm9rb5eVeVysyBD3eWFR7kTzpku8iETf6IrAjd3eGf4vbKBV1DlMlcPZYtQiuHttphQ0YPkKNfeE
    restart: always
    volumes:
      - /volume2/service/bin/moviebot/data:/data
      - /volume2:/volume2
    ports:
      - 1329:1329

  musictag:
    image: xhongc/music_tag_web:latest
    container_name: musictag
    privileged: true
    restart: always
    network_mode: host
    volumes:
      - /volume2/service/bin/musictag/data:/app/data
      - /volume2/pilot_library/音乐:/app/media
    command: /start
    ports:
      - 80:80


  ns-fork:
    image: hsuyelin/nas-tools:latest
    container_name: ns-fork
    privileged: true
    restart: always
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - UMASK=022 # 掩码权限，默认000，可以考虑设置为022
      - NASTOOL_AUTO_UPDATE=false
    volumes:
      - /volume2/service/bin/nas-tools-fork/config:/config
      - /volume2:/volume2
    extra_hosts:
      - "api.themoviedb.org:*************"
      - "image.themoviedb.org:*************"
      - "api.themoviedb.org:***********"
      - "www.themoviedb.org:*************"
      - "api.thetvdb.com:*************"
      - "api.thetvdb.com:**************"
      - "api.thetvdb.com:*************"
      - "api.themoviedb.org:*************"
      - "api.themoviedb.org:***********"
      - "api.themoviedb.org:*************"
      - "api.themoviedb.org:*************"
      - "api.themoviedb.org:**************"
      - "api.themoviedb.org:***********"
      - "api.themoviedb.org:************"
      - "api.themoviedb.org:*************"
    ports:
      - "3000:3000"

  navidrome:
    image: deluan/navidrome:latest
    container_name: navidrome
    privileged: true
    restart: always
    networks:
      macvlanipv46:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - ND_SCANSCHEDULE=@daily
      - ND_LOGLEVEL=info
      - ND_SESSIONTIMEOUT=24h
      - ND_MUSICFOLDER=/music
      - ND_DATAFOLDER=/data
      - ND_PORT=4533
    volumes:
      - /volume2/service/bin/navidrome/data:/data
      - /volume2/pilot_library/音乐:/music:ro
    ports:
      - 4533:4533

  homepage:
    image: quay.io/coppkaw/homepage:latest
    container_name: homepage
    privileged: true
    restart: always
    networks:
      macvlanzte:
        ipv4_address: *************
    volumes:
      - /volume2/service/bin/homepage/config:/app/config
      - /volume2/service/bin/homepage/icons:/app/public/icons
      - /volume2/service/bin/homepage/images:/app/images
      - /var/run/docker.sock:/var/run/docker.sock:ro # (optional) For docker integrations
    #    ports:
    #      - "3000:3000"


  calibre-web:
    image: quay.io/coppkaw/calibre-web:latest
    container_name: calibre-web
    privileged: true
    restart: always
    network_mode: host
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - DOCKER_MODS=linuxserver/mods:universal-calibre #optional
      - OAUTHLIB_RELAX_TOKEN_SCOPE=1 #optional
    volumes:
      - /volume2/service/bin/calibre-web/config:/config
      - /volume2/pt/入党申请书:/x-books
    #    ports:
    #      - 8083:8083


  jellyfinx:
    image: quay.io/coppkaw/jellyfin:latest
    container_name: jellyfinx
    privileged: true
    hostname: jellyfinx
    restart: always
    networks:
      macvlanzte:
        ipv4_address: *************
    environment:
      - UID=0 # The UID to run emby as (default: 2)
      - GID=0 # The GID to run emby as (default 2)
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/jellyfinx/cache:/cache
      - /volume2/service/bin/jellyfinx/config:/config
      - /volume2/backup/SSL证书/ssl_20250211/hydra.cab_iis:/ssl/certs
      - /volume2/onlyhub/avis_japan:/data/日本电影
      - /volume2/onlyhub/deepfakes:/data/deepfakes
    ports:
      - "8096:8096" # HTTP port
      - "8920:8920" # HTTPS port


  ollama:
    image: quay.io/coppkaw/ollama:latest
    container_name: ollama
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/ollama/data:/root/.ollama
    #    ports:
    #      - 11434:11434

  lobe-chat:
    image: quay.io/coppkaw/lobe-chat:latest
    container_name: lobe-chat
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - OPENAI_API_KEY=********************************************************************************************************************************************************************
      - ACCESS_CODE=blackbox668
    #    ports:
    #      - 3210:3210


  openwebui:
    image: quay.io/coppkaw/open-webui:main
    container_name: openwebui
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/openwebui/data:/app/backend/data
      - /volume2/service/bin/openwebui/data/webui_secret_key:/app/backend/.webui_secret_key
    ports:
      - 8080:8080



  natter-deepseek:
    image: quay.io/coppkaw/natter:latest
    container_name: natter-deepseek
    restart: always
    network_mode: host
    privileged: true
    environment:
      - TZ=Asia/Shanghai
    command: -t *********** -p 28080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/local/bin/docker:/usr/local/bin/docker

  natter-lobe-chat:
    image: quay.io/coppkaw/natter:latest
    container_name: natter-lobe-chat
    restart: always
    network_mode: host
    privileged: true
    environment:
      - TZ=Asia/Shanghai
    command: -t *********** -p 23210
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/local/bin/docker:/usr/local/bin/docker

  mp19:
    image: quay.io/coppkaw/moviepilot:1.9.17
    container_name: mp19
    privileged: true
    restart: always
    networks:
      zte_macvlan:
        ipv4_address: *************
    hostname: moviepilot
    stdin_open: true
    tty: true
    ports:
      - target: 3000
        published: 3000
        protocol: tcp
    environment:
      # 基础设置
      - NGINX_PORT=3000
      - PORT=3001
      - PUID=0
      - PGID=0
      - UMASK=000
      - TZ=Asia/Shanghai
      - PROXY_HOST=http://*************:7890
      - MOVIEPILOT_AUTO_UPDATE=false # true/release/dev/false
      - AUTO_UPDATE_RESOURCE=true
      - GITHUB_TOKEN=****************************************
      # 用户认证
      - AUTH_SITE=iyuu,hhclub,hddolby,hdfans,haidan,disfan
      - HHCLUB_USERNAME=BlackBox
      - HHCLUB_PASSKEY=********************************
      - HDFANS_UID=29676
      - HDFANS_PASSKEY=ba238f2d74c24fb287f433604508a9b6
      - IYUU_SIGN=IYUU11853Tbe0ad23656600ef96a2aec7e6fc97922d94caf90
      - DISCFAN_UID=46269
      - DISCFAN_PASSKEY=a77276a5778da45f4da01667ba42117b
      - HAIDAN_ID=45846
      - HAIDAN_PASSKEY=cb1c54c97fbbe46fa490aa0966675134
      # 鉴权 / API
      - SUPERUSER=nas
      - SUPERUSER_PASSWORD=acce-s6se/nas # 需要通过config/logs查看初始密码，再进行修改
      - API_TOKEN=nas_moviepilot
      # 刮削相关
      - TMDB_API_DOMAIN=api.themoviedb.org
      - TMDB_IMAGE_DOMAIN=image.tmdb.org
      - WALLPAPER=tmdb
      - RECOGNIZE_SOURCE=themoviedb
      - SCRAP_METADATA=true
      - SCRAP_SOURCE=themoviedb
      - SCRAP_FOLLOW_TMDB=true
      # COOKIE
      - COOKIECLOUD_HOST=http://*************:8088/cookies
      - COOKIECLOUD_KEY=9YyqjRo3y9M9TPQQTtWA2K
      - COOKIECLOUD_PASSWORD=cAA7puRKy3w8591JNpc8pZ
      - COOKIECLOUD_INTERVAL=10
      - COOKIECLOUD_ENABLE_LOCAL=false
      - USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      # 订阅相关
      - SUBSCRIBE_MODE=spider
      - SUBSCRIBE_RSS_INTERVAL=30
      - SUBSCRIBE_SEARCH=true
      - AUTO_DOWNLOAD_USER=0
      # OCR
      - OCR_HOST=https://movie-pilot.org
      # 下载目录
      - DOWNLOAD_PATH=/volume2/pt/黑洞
      - DOWNLOAD_MOVIE_PATH=/volume2/pt/黑洞/电影
      - DOWNLOAD_TV_PATH=/volume2/pt/黑洞/电视剧
      - DOWNLOAD_ANIME_PATH=/volume2/pt/黑洞/动画
      - DOWNLOAD_CATEGORY=false
      - DOWNLOAD_SUBTITLE=true
      # 下载工具
      - DOWNLOADER=qbittorrent
      - QB_HOST=http://***********:18085
      - QB_USER=nas
      - QB_PASSWORD=acce-s6se/nas
      - QB_CATEGORY=false
      - QB_SEQUENTIAL=true
      - QB_FORCE_RESUME=false
      - DOWNLOADER_MONITOR=true
      - TORRENT_TAG=moviepilot
      # 通知/互动 SLACK
      - MESSAGER=slack
      - SLACK_OAUTH_TOKEN=*********************************************************
      - SLACK_APP_TOKEN=xapp-1-A067HFF5CUC-6238547889015-27f72416cba71b33f1e10fa0c70a99cf467e2c0bc61dfad2694a64b5bf06b735
      - SLACK_CHANNEL=general
      # MEDIASERVER
      - MEDIASERVER=emby
      - EMBY_HOST=http://em.hydra.cab
      - EMBY_API_KEY=f8d9e96cdc374e4a864430f9606b081c
      - MEDIASERVER_SYNC_INTERVAL=1
      - MEDIASERVER_SYNC_BLACKLIST=''
      # 进阶设置
      - MOVIE_RENAME_FORMAT={{title}}{% if year %} ({{year}}){% endif %}/{{title}}{% if year %} ({{year}}){% endif %}{% if part %}-{{part}}{% endif %}{% if videoFormat %} - {{videoFormat}}{% endif %}{{fileExt}}
      - TV_RENAME_FORMAT={{title}}{% if year %} ({{year}}){% endif %}/Season {{season}}/{{title}} - {{season_episode}}{% if part %}-{{part}}{% endif %}{% if episode %} - 第 {{episode}} 集{% endif %}{{fileExt}}
      - PLUGIN_MARKET=https://github.com/jxxghp/MoviePilot-Plugins
    volumes:
      - /volume2:/volume2
      - /volume2/service/bin/moviepilot19/data:/moviepilot
      - /volume2/service/bin/moviepilot19/config:/config
      - /volume2/service/bin/moviepilot19/core:/moviepilot/.cache/ms-playwright
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /volume2/service/bin/moviepilot19/src/app/utils/system.py:/app/app/utils/system.py
      - /volume2/service/bin/moviepilot19/src/app/modules/filetransfer/__init__.py:/app/app/modules/filetransfer/__init__.py

  portainer:
    image: quay.io/coppkaw/portainer-ce:latest
    container_name: portainer
    #    command: --admin-password="acce-s6se/nas"
    restart: always
    privileged: true
    networks:
      zte_macvlan:
        ipv4_address: *************
    volumes:
      - /volume2/service/bin/portainer/data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    #    ports:
    #      - "8000:8000"
    #      - "9000:9000" # HTTP
    #      - "9443:9443" # HTTPS port

  registry:
    image: quay.io/coppkaw/registry:latest
    container_name: registry
    restart: always
    environment:
      - UID=0 # The UID to run emby as (default: 2)
      - GID=0 # The GID to run emby as (default 2)
      - TZ=Asia/Shanghai
    volumes:
      - /volume2/service/bin/registry/data:/var/lib/registry  # data directory
    ports:
      - "4999:5000"

  mp2compare:
    image: quay.io/coppkaw/moviepilot-v2:latest
    container_name: mp2compare
    privileged: true
    restart: always
    network_mode: "host"
    hostname: mp2compare
    stdin_open: true
    tty: true
    ports:
      - target: 3000
        published: 3000
        protocol: tcp
      - target: 3001
        published: 3001
        protocol: tcp
    environment:
      # 基础设置
      - NGINX_PORT=3000
      - PORT=3001
      - PUID=0
      - PGID=0
      - UMASK=000
      - TZ=Asia/Shanghai
      # 缓存
      - CACHE_BACKEND_TYPE=cachetools
      # 代理/更新相关
      - PROXY_HOST=http://*************:7890
      - MOVIEPILOT_AUTO_UPDATE=false # true/release/dev/false
      - AUTO_UPDATE_RESOURCE=true
      - GITHUB_TOKEN=****************************************
      # 用户认证
      - AUTH_SITE=iyuu,hhclub,hddolby,hdfans,discfan
      - HHCLUB_USERNAME=BlackBox
      - HHCLUB_PASSKEY=********************************
      - HDFANS_UID=29676
      - HDFANS_PASSKEY=ba238f2d74c24fb287f433604508a9b6
      - IYUU_SIGN=IYUU11853Tbe0ad23656600ef96a2aec7e6fc97922d94caf90
      - DISCFAN_UID=46269
      - DISCFAN_PASSKEY=a77276a5778da45f4da01667ba42117b
      - HAIDAN_ID=45846
      - HAIDAN_PASSKEY=cb1c54c97fbbe46fa490aa0966675134
      # 鉴权 / API
      - SUPERUSER=nas
      - SUPERUSER_PASSWORD=acce-s6se/nas # 需要通过config/logs查看初始密码，再进行修改
      - API_TOKEN=KrB0ac5t5zFEkfUSYgXpZQ
      # COOKIE
      - COOKIECLOUD_HOST=http://***********:18088/cookies
      - COOKIECLOUD_KEY=acce-s6se-uuid-cookiecloud
      - COOKIECLOUD_PASSWORD=acce-s6se-password-cookiecloud
      - COOKIECLOUD_INTERVAL=10
      - COOKIECLOUD_ENABLE_LOCAL=true
      - USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
    volumes:
      - /volume2:/volume2
      - /volume2/service/bin/moviepilot2compare/config:/config
      - /volume2/service/bin/moviepilot2compare/core:/moviepilot/.cache/ms-playwright
      - /volume2/service/bin/cookiecloud/data:/config/cookies/
      - /var/run/docker.sock:/var/run/docker.sock:ro

networks:
  macvlanipv46:
    external: true
