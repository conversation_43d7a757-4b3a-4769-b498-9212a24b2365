#!/bin/bash

# 定义所有定时器的名称
TIMERS=(
    restart-mp2.timer
    restart-emby302.timer
    restart-embyx302.timer
    restart-hydra-chown.timer
    hydra-build-index.timer
    hydra-file-hardlink.timer
    hydra-start-webhook.timer
    hydra-torrent-transfer.timer
    start-lh-frpc.timer
    hydra-prune.timer
)

# 重新加载 systemd 守护进程
/bin/systemctl daemon-reload

# 禁用所有定时器
/bin/systemctl disable "${TIMERS[@]}"
/bin/systemctl stop "${TIMERS[@]}"

# 启用所有定时器
/bin/systemctl enable "${TIMERS[@]}"

# 启动所有定时器
/bin/systemctl start "${TIMERS[@]}"
