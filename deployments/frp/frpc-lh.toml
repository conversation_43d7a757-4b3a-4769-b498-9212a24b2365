# A literal address or host name for IPv6 must be enclosed
# in square brackets, as in "[::1]:80", "[ipv6-host]:http" or "[ipv6-host%zone]:80"
# For single serverAddr field, no need square brackets, like serverAddr = "::".serverAddr = "**************"
serverAddr = "**************"
serverPort = 7000

# Decide if exit program when first login failed, otherwise continuous relogin to frps
# default is true
loginFailExit = true

# console or real logFile path like ./frpc.log
log.to = "./frpc-lh.log"
# trace, debug, info, warn, error
log.level = "info"
log.maxDays = 3
# disable log colors when log.to is console, default is false
log.disablePrintColor = false

auth.method = "token"
# auth.additionalScopes specifies additional scopes to include authentication information.
# Optional values are HeartBeats, NewWorkConns.
# auth.additionalScopes = ["HeartBeats", "NewWorkConns"]

# auth token
auth.token = "acce-s6se/fr"

# The maximum amount of time a dial to server will wait for a connect to complete. Default value is 10 seconds.
# transport.dialServerTimeout = 10

# dialServerKeepalive specifies the interval between keep-alive probes for an active network connection between frpc and frps.
# If negative, keep-alive probes are disabled.
# transport.dialServerKeepalive = 7200

# connections will be established in advance, default value is zero
transport.poolCount = 5

# If tcp stream multiplexing is used, default is true, it must be same with frps
# transport.tcpMux = true

# Specify keep alive interval for tcp mux.
# only valid if tcpMux is enabled.
# transport.tcpMuxKeepaliveInterval = 30

# Communication protocol used to connect to server
# supports tcp, kcp, quic, websocket and wss now, default is tcp
transport.protocol = "tcp"

# set client binding ip when connect server, default is empty.
# only when protocol = tcp or websocket, the value will be used.
transport.connectServerLocalIP = "0.0.0.0"

[[proxies]]
name = "hydra.cab-ssh"
type = "xtcp"
# 只有与此处设置的 secretKey 一致的用户才能访问此服务
secretKey = "acce-s6se/nas"
localIP = "127.0.0.1"
localPort = 36001

# Limit bandwidth for this proxy, unit is KB and MB
transport.bandwidthLimit = "1MB"
# Where to limit bandwidth, can be 'client' or 'server', default is 'client'
transport.bandwidthLimitMode = "client"
# If true, traffic of this proxy will be encrypted, default is false
transport.useEncryption = false
# If true, traffic will be compressed
transport.useCompression = false

[[proxies]]
name = "hydra.cab"
type = "https"
customDomains = ["hydra.cab"]
[proxies.plugin]
type = "https2http"
localAddr = "***********:15000"
crtPath = "/volume2/backup/ArchiveOfStars/ssl/hydra.cab_apache/hydra.cab.crt"
keyPath = "/volume2/backup/ArchiveOfStars/ssl/hydra.cab_apache/hydra.cab.key"
hostHeaderRewrite = "127.0.0.1"
requestHeaders.set.x-from-where = "frp"

[[proxies]]
name = "backup.hydra.cab"
type = "http"
localIP = "***********32"
localPort = 80
customDomains = ["backup.hydra.cab"]

[[proxies]]
name = "em.hydra.cab"
type = "http"
localIP = "***********20"
localPort = 8096
customDomains = ["em.hydra.cab"]

[[proxies]]
name = "embackup.hydra.cab"
type = "http"
localIP = "***********20"
localPort = 8096
customDomains = ["embackup.hydra.cab"]

[[proxies]]
name = "emx.hydra.cab"
type = "http"
localIP = "***********31"
localPort = 80
customDomains = ["emx.hydra.cab"]

[[proxies]]
name = "qb.hydra.cab"
type = "https"
localIP = "***********01"
localPort = 8085
customDomains = ["qb.hydra.cab"]
[proxies.plugin]
type = "https2http"
localAddr = "***********01:8085"
crtPath = "/volume2/backup/ArchiveOfStars/ssl/qb.hydra.cab_apache/qb.hydra.cab.crt"
keyPath = "/volume2/backup/ArchiveOfStars/ssl/qb.hydra.cab_apache/qb.hydra.cab.key"
hostHeaderRewrite = "127.0.0.1"
requestHeaders.set.x-from-where = "frp"

[[proxies]]
name = "tr.hydra.cab"
type = "https"
localIP = "***********02"
localPort = 9091
customDomains = ["tr.hydra.cab"]
[proxies.plugin]
type = "https2http"
localAddr = "***********02:9091"
crtPath = "/volume2/backup/ArchiveOfStars/ssl/tr.hydra.cab_apache/tr.hydra.cab.crt"
keyPath = "/volume2/backup/ArchiveOfStars/ssl/tr.hydra.cab_apache/tr.hydra.cab.key"
hostHeaderRewrite = "127.0.0.1"
requestHeaders.set.x-from-where = "frp"

[[proxies]]
name = "trsmall.hydra.cab"
type = "https"
localIP = "***********03"
localPort = 9091
customDomains = ["trsmall.hydra.cab"]
[proxies.plugin]
type = "https2http"
localAddr = "***********03:9091"
crtPath = "/volume2/backup/ArchiveOfStars/ssl/trsmall.hydra.cab_apache/trsmall.hydra.cab.crt"
keyPath = "/volume2/backup/ArchiveOfStars/ssl/trsmall.hydra.cab_apache/trsmall.hydra.cab.key"
hostHeaderRewrite = "127.0.0.1"
requestHeaders.set.x-from-where = "frp"

[[proxies]]
name = "ha.hydra.cab"
type = "https"
localIP = "***********09"
localPort = 8123
customDomains = ["ha.hydra.cab"]
[proxies.plugin]
type = "https2http"
localAddr = "***********09:8123"
crtPath = "/volume2/backup/ArchiveOfStars/ssl/ha.hydra.cab_apache/ha.hydra.cab.crt"
keyPath = "/volume2/backup/ArchiveOfStars/ssl/ha.hydra.cab_apache/ha.hydra.cab.key"
hostHeaderRewrite = "127.0.0.1"
requestHeaders.set.x-from-where = "frp"

[[proxies]]
name = "wd.hydra.cab"
type = "http"
localIP = "***********36"
localPort = 80
customDomains = ["wd.hydra.cab"]

#[[proxies]]
#name = "mp.hydra.cab"
#type = "http"
#localIP = "***********35"
#localPort = 80
#customDomains = ["mp.hydra.cab"]

[[proxies]]
name = "mp.hydra.cab"
type = "https"
customDomains = ["mp.hydra.cab"]
[proxies.plugin]
type = "https2http"
localAddr = "***********00:3000"
crtPath = "/volume2/backup/ArchiveOfStars/ssl/mp.hydra.cab_apache/mp.hydra.cab.crt"
keyPath = "/volume2/backup/ArchiveOfStars/ssl/mp.hydra.cab_apache/mp.hydra.cab.key"
hostHeaderRewrite = "127.0.0.1"
requestHeaders.set.x-from-where = "frp"

#[[proxies]]
#name = "zte.hydra.cab"
#type = "http"
#localIP = "***********"
#localPort = 80
#customDomains = ["zte.hydra.cab"]

#[[proxies]]
#name = "ikuai.hydra.cab"
#type = "http"
#localIP = "***********"
#localPort = 80
#customDomains = ["ikuai.hydra.cab"]
