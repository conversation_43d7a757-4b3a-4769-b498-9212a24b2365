#!/bin/bash

# Define the FRPS command, config file, and paths
FRPS_COMMAND="/volume2/service/bin/frp/frps"
FRPS_CONFIG="/volume2/service/bin/frp/frps-gy.toml"
FRPS_LOG="/volume2/service/bin/frp/frps-gy.log"
FRPS_PID_FILE="/volume2/service/bin/frp/frps-gy.pid"

# Function to check if the process is running
is_running() {
  if [ -f "$FRPS_PID_FILE" ]; then
    PID=$(cat "$FRPS_PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
      return 0
    else
      return 1
    fi
  else
    return 1
  fi
}

# Start the service
start() {
  if is_running; then
    echo "FRPS is already running (PID: $(cat $FRPS_PID_FILE))."
  else
    echo "Starting FRPS..."
    $FRPS_COMMAND -c $FRPS_CONFIG >> "$FRPS_LOG" 2>&1 &
    echo $! > "$FRPS_PID_FILE"
    echo "FRPS started (PID: $(cat $FRPS_PID_FILE))."
  fi
}

# Stop the service
stop() {
  if is_running; then
    echo "Stopping FRPS..."
    kill $(cat "$FRPS_PID_FILE")
    rm -f "$FRPS_PID_FILE"
    echo "FRPS stopped."
  else
    echo "FRPS is not running."
  fi
}

# Check the status of the service
status() {
  if is_running; then
    echo "FRPS is running (PID: $(cat $FRPS_PID_FILE))."
  else
    echo "FRPS is not running."
  fi
}

# Restart the service
restart() {
  echo "Restarting FRPS..."
  stop
  start
}

# View the FRPS log file
view_log() {
  if [ -f "$FRPS_LOG" ]; then
    echo "Tailing FRPS log (Press Ctrl+C to exit)..."
    tail -f "$FRPS_LOG"
  else
    echo "Log file not found: $FRPS_LOG"
  fi
}

# Display the help message
help() {
  echo "Usage: $0 {start|stop|status|restart|tail|help}"
}

# Parse the command-line arguments
case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  status)
    status
    ;;
  restart)
    restart
    ;;
  tail)
    view_log
    ;;
  help)
    help
    ;;
  *)
    echo "Invalid command!"
    help
    ;;
esac

exit 0
