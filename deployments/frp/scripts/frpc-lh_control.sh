#!/bin/bash

# Define the FRPC command, config file, and paths
FRPC_COMMAND="/volume2/service/bin/frp/frpc"
FRPC_CONFIG="/volume2/service/bin/frp/frpc-lh.toml"
FRPC_LOG="/volume2/service/bin/frp/frpc-lh.log"
FRPC_PID_FILE="/volume2/service/bin/frp/frpc-lh.pid"

# Function to check if the process is running
is_running() {
  if [ -f "$FRPC_PID_FILE" ]; then
    PID=$(cat "$FRPC_PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
      return 0
    else
      return 1
    fi
  else
    return 1
  fi
}

# Start the service
start() {
  if is_running; then
    echo "FRPC is already running (PID: $(cat $FRPC_PID_FILE))."
  else
    echo "Starting FRPC..."
    $FRPC_COMMAND -c $FRPC_CONFIG >> "$FRPC_LOG" 2>&1 &
    echo $! > "$FRPC_PID_FILE"
    echo "FRPC started (PID: $(cat $FRPC_PID_FILE))."
  fi
}

# Stop the service
stop() {
  if is_running; then
    echo "Stopping FRPC..."
    kill $(cat "$FRPC_PID_FILE")
    rm -f "$FRPC_PID_FILE"
    echo "FRPC stopped."
  else
    echo "FRPC is not running."
  fi
}

# Check the status of the service
status() {
  if is_running; then
    echo "FRPC is running (PID: $(cat $FRPC_PID_FILE))."
  else
    echo "FRPC is not running."
  fi
}

# Restart the service
restart() {
  echo "Restarting FRPC..."
  stop
  start
}

# View the FRPC log file
view_log() {
  if [ -f "$FRPC_LOG" ]; then
    echo "Tailing FRPC log (Press Ctrl+C to exit)..."
    tail -f "$FRPC_LOG"
  else
    echo "Log file not found: $FRPC_LOG"
  fi
}

# Display the help message
help() {
  echo "Usage: $0 {start|stop|status|restart|tail|help}"
}

# Parse the command-line arguments
case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  status)
    status
    ;;
  restart)
    restart
    ;;
  tail)
    view_log
    ;;
  help)
    help
    ;;
  *)
    echo "Invalid command!"
    help
    ;;
esac

exit 0
