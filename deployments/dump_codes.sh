#!/bin/bash

# 输出文件路径
OUTPUT_DIR="data"
OUTPUT_FILE="$OUTPUT_DIR/structures.txt"

# 创建data目录（如果不存在）
mkdir -p "$OUTPUT_DIR"

# 清空或创建输出文件
> "$OUTPUT_FILE"

# 函数：生成目录树结构
print_tree() {
    local dir="$1"
    local prefix="$2"
    
    # 获取目录内容并排序，排除.git和.idea
    find "$dir" -maxdepth 1 -not -path "$dir" -not -name ".git" -not -name ".idea" | sort | while read -r entry; do
        # 提取文件名
        name=$(basename "$entry")
        
        # 过滤条件
        if [[ "$name" == "data" || "$name" == "bin" || "$name" =~ \.tar$ || "$name" =~ \.dll$ ]]; then
            continue
        fi
        
        # 确定是否是最后一个条目
        if [[ "$entry" == "$(find "$dir" -maxdepth 1 -not -path "$dir" -not -name ".git" -not -name ".idea" | sort | tail -n 1)" ]]; then
            pointer="└── "
            new_prefix="$prefix    "
        else
            pointer="├── "
            new_prefix="$prefix│   "
        fi
        
        echo "$prefix$pointer$name" >> "$OUTPUT_FILE"
        
        # 如果是目录，递归处理
        if [[ -d "$entry" ]]; then
            print_tree "$entry" "$new_prefix"
        fi
    done
}

# 写入目录结构
echo "Directory Structure:" >> "$OUTPUT_FILE"
echo "====================" >> "$OUTPUT_FILE"
print_tree "." ""

# 写入文件内容标题
echo -e "\nFile Contents:" >> "$OUTPUT_FILE"
echo "==============" >> "$OUTPUT_FILE"

# 定义要包含的文件类型：Golang文件、配置文件以及其他类型
FILE_TYPES="*.go *.yaml *.yml *.json *.toml *.ini *.txt go.mod Makefile Dockerfile"

# 遍历所有符合条件的文件并输出内容
find . -type f -not -path "./data/*" -not -path "./bin/*" -not -path "./.idea/*" \
    -not -name "*.tar" -not -name "*.dll" \
    \( -name "*.go" -o -name "*.yaml" -o -name "*.yml" -o -name "*.json" -o -name "*.toml" -o -name "*.ini" -o -name "*.txt" -o -name "go.mod" -o -name "Makefile" -o -name "Dockerfile" \) | while read -r file; do
    echo -e "\n==================================================" >> "$OUTPUT_FILE"
    echo "File: $file" >> "$OUTPUT_FILE"
    echo "==================================================" >> "$OUTPUT_FILE"
    
    # 尝试读取文件内容
    if cat "$file" >> "$OUTPUT_FILE" 2>/dev/null; then
        echo "" >> "$OUTPUT_FILE"
    else
        echo "Error: Could not read file (possibly permission denied)" >> "$OUTPUT_FILE"
    fi
done

echo "Golang project structure and contents have been written to $OUTPUT_FILE"
