# go-claude 配置示例
proxy_port: 8700           # 本地代理端口

# 重试配置
retry:
  max_retries: 3           # 最大重试次数
  retry_delay: 1s          # 重试延迟
  recovery_interval: 5m    # provider恢复检查间隔

# 模型提供商配置列表
model_providers:
  - name: "deepseek"
    api_key: "***********************************"
    endpoint: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
  - name: "qwen"
    api_key: "***********************************"
    endpoint: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model: "qwen-max"
  - name: "huoshan-deepseek"
    api_key: "3ccbf174-128e-4dcd-adec-72868f0daa0d"
    endpoint: "https://ark.cn-beijing.volces.com/api/v3"
    model: "deepseek-v3-250324"
  - name: "huoshan-doubao"
    api_key: "3ccbf174-128e-4dcd-adec-72868f0daa0d"
    endpoint: "https://ark.cn-beijing.volces.com/api/v3"
    model: "doubao-seed-1-6-250615"

# 全局负载均衡策略
load_balance: "random"     # 可选 random/round_robin/least-busy

# 各智能体配置
tool_agent_ai:
  names: ["deepseek","qwen","huoshan-deepseek","huoshan-doubao"]
  load_balance: "random"

coder_agent_ai:
  names: ["deepseek","qwen","huoshan-deepseek","huoshan-doubao"]

think_agent_ai:
  names: ["deepseek","qwen","huoshan-deepseek","huoshan-doubao"]

router_agent_ai:
  names: ["deepseek","qwen","huoshan-deepseek","huoshan-doubao"]

# 日志配置
log_level: "debug"          # 可选 debug/info/warn/error