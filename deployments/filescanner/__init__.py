import json
import time
from datetime import datetime
from pathlib import Path
from typing import Any, List, Dict, Tuple, Optional

from apscheduler.triggers.cron import CronTrigger

from app import schemas
from app.chain.transfer import TransferChain
from app.core.event import eventmanager
from app.log import logger
from app.plugins import _PluginBase
from app.schemas import NotificationType


class FileScanner(_PluginBase):
    """
    文件扫描整理插件
    """
    # 插件名称
    plugin_name = "文件扫描整理"
    # 插件描述
    plugin_desc = "定时扫描指定目录并自动整理文件到目标存储"
    # 插件图标
    plugin_icon = "https://raw.githubusercontent.com/jxxghp/MoviePilot-Plugins/main/icons/filescanner.png"
    # 插件版本
    plugin_version = "1.1"
    # 插件作者
    plugin_author = "thirdparty"
    # 作者主页
    author_url = "https://github.com/jxxghp/MoviePilot-Plugins"
    # 插件配置项ID前缀
    plugin_config_prefix = "filescanner_"
    # 加载顺序
    plugin_order = 20
    # 可使用的用户级别
    auth_level = 1

    # 私有属性
    _enabled = False
    _cron = None
    _tasks = []
    _notify = False
    _transfer_chain = None
    _process_delay = 2  # 文件处理间隔延时（秒）
    _batch_size = 10   # 批量处理大小限制
    _max_retries = 3   # 最大重试次数

    def init_plugin(self, config: dict = None):
        """
        初始化插件
        """
        if not config:
            return
        
        self._enabled = config.get("enabled", False)
        self._cron = config.get("cron", "0 3 * * *")
        self._notify = config.get("notify", False)
        self._process_delay = config.get("process_delay", 2)
        self._batch_size = config.get("batch_size", 10)
        self._max_retries = config.get("max_retries", 3)
        
        # 解析任务列表
        tasks_json = config.get("tasks", "[]")
        try:
            self._tasks = json.loads(tasks_json) if isinstance(tasks_json, str) else tasks_json
        except Exception as e:
            logger.error(f"解析任务配置失败: {str(e)}")
            self._tasks = []
        
        # 初始化传输链
        if self._enabled:
            self._transfer_chain = TransferChain()

    def get_state(self) -> bool:
        """
        获取插件运行状态
        """
        return self._enabled

    @staticmethod
    def get_command() -> List[Dict[str, Any]]:
        """
        定义远程控制命令
        """
        return []

    def get_api(self) -> List[Dict[str, Any]]:
        """
        获取插件API
        """
        return []

    def get_form(self) -> Tuple[Optional[List[dict]], Dict[str, Any]]:
        """
        拼装插件配置页面，使用Vuetify组件
        """
        return [
            {
                'component': 'VForm',
                'props': {
                    'model': 'plugin_form',
                },
                'content': [
                    {
                        'component': 'VRow',
                        'content': [
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12,
                                    'md': 4
                                },
                                'content': [
                                    {
                                        'component': 'VSwitch',
                                        'props': {
                                            'model': 'enabled',
                                            'label': '启用插件',
                                            'color': 'primary'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12,
                                    'md': 4
                                },
                                'content': [
                                    {
                                        'component': 'VTextField',
                                        'props': {
                                            'model': 'cron',
                                            'label': 'CRON表达式',
                                            'placeholder': '0 3 * * *',
                                            'hint': '定时执行时间，默认每天凌晨3点'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12,
                                    'md': 4
                                },
                                'content': [
                                    {
                                        'component': 'VSwitch',
                                        'props': {
                                            'model': 'notify',
                                            'label': '发送通知',
                                            'color': 'primary'
                                        }
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        'component': 'VRow',
                        'content': [
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12,
                                    'md': 4
                                },
                                'content': [
                                    {
                                        'component': 'VTextField',
                                        'props': {
                                            'model': 'process_delay',
                                            'label': '处理延时（秒）',
                                            'type': 'number',
                                            'min': 0,
                                            'max': 60,
                                            'hint': '文件处理间隔时间，避免API频率限制'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12,
                                    'md': 4
                                },
                                'content': [
                                    {
                                        'component': 'VTextField',
                                        'props': {
                                            'model': 'batch_size',
                                            'label': '批量大小限制',
                                            'type': 'number',
                                            'min': 1,
                                            'max': 100,
                                            'hint': '单次处理的最大文件数量'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12,
                                    'md': 4
                                },
                                'content': [
                                    {
                                        'component': 'VTextField',
                                        'props': {
                                            'model': 'max_retries',
                                            'label': '最大重试次数',
                                            'type': 'number',
                                            'min': 0,
                                            'max': 10,
                                            'hint': '遇到错误时的重试次数'
                                        }
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        'component': 'VRow',
                        'content': [
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12
                                },
                                'content': [
                                    {
                                        'component': 'VTextarea',
                                        'props': {
                                            'model': 'tasks',
                                            'label': '扫描任务配置',
                                            'rows': 15,
                                            'placeholder': '''[
  {
    "name": "整理电视剧",
    "enabled": true,
    "source_path": "/volume2/pt/电视剧/",
    "target_storage": "local",
    "target_path": "/媒体资源库/电视剧/",
    "transfer_type": "copy",
    "min_filesize": 0,
    "scrape": true,
    "library_category_folder": true,
    "library_type_folder": true
  }
]''',
                                            'hint': 'JSON格式的任务配置，支持多个任务'
                                        }
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        'component': 'VRow',
                        'content': [
                            {
                                'component': 'VCol',
                                'props': {
                                    'cols': 12
                                },
                                'content': [
                                    {
                                        'component': 'VAlert',
                                        'props': {
                                            'type': 'info',
                                            'text': '任务配置说明：',
                                            'variant': 'tonal'
                                        },
                                        'content': [
                                            {
                                                'component': 'div',
                                                'html': '''
<b>必填参数：</b><br>
• name: 任务名称<br>
• source_path: 源路径<br>
• target_storage: 目标存储<br>
• target_path: 目标路径<br>
<br>
<b>可选参数：</b><br>
• enabled: 是否启用 (默认 true)<br>
• transfer_type: 整理方式 (copy/move/link/softlink，默认 copy)<br>
• min_filesize: 最小文件大小，单位MB (默认 0)<br>
• scrape: 是否刮削 (默认 true)<br>
• library_category_folder: 媒体库类别子目录 (默认 true)<br>
• library_type_folder: 媒体库类型子目录 (默认 true)
'''
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ], {
            "enabled": False,
            "cron": "0 3 * * *",
            "notify": False,
            "process_delay": 2,
            "batch_size": 10,
            "max_retries": 3,
            "tasks": json.dumps([{
                "name": "示例任务",
                "enabled": True,
                "source_path": "/path/to/source/",
                "target_storage": "local",
                "target_path": "/path/to/target/",
                "transfer_type": "copy",
                "min_filesize": 0,
                "scrape": True,
                "library_category_folder": True,
                "library_type_folder": True
            }], indent=2, ensure_ascii=False)
        }

    def get_page(self) -> Optional[List[dict]]:
        """
        拼装插件详情页面
        """
        if not self._tasks:
            return [
                {
                    'component': 'VRow',
                    'content': [
                        {
                            'component': 'VCol',
                            'props': {
                                'cols': 12
                            },
                            'content': [
                                {
                                    'component': 'VAlert',
                                    'props': {
                                        'type': 'info',
                                        'text': '暂未配置任何扫描任务'
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        
        # 显示任务列表
        task_cards = []
        for idx, task in enumerate(self._tasks):
            if not isinstance(task, dict):
                continue
                
            task_cards.append({
                'component': 'VCol',
                'props': {
                    'cols': 12,
                    'md': 6,
                    'lg': 4
                },
                'content': [
                    {
                        'component': 'VCard',
                        'props': {
                            'variant': 'tonal'
                        },
                        'content': [
                            {
                                'component': 'VCardTitle',
                                'props': {
                                    'class': 'd-flex justify-space-between align-center'
                                },
                                'content': [
                                    {
                                        'component': 'span',
                                        'text': task.get('name', f'任务{idx+1}')
                                    },
                                    {
                                        'component': 'VChip',
                                        'props': {
                                            'color': 'success' if task.get('enabled', True) else 'error',
                                            'size': 'small'
                                        },
                                        'text': '启用' if task.get('enabled', True) else '禁用'
                                    }
                                ]
                            },
                            {
                                'component': 'VCardText',
                                'content': [
                                    {
                                        'component': 'VList',
                                        'props': {
                                            'density': 'compact'
                                        },
                                        'content': [
                                            {
                                                'component': 'VListItem',
                                                'props': {
                                                    'prepend-icon': 'mdi-folder-open',
                                                    'title': '源路径',
                                                    'subtitle': task.get('source_path', '未设置')
                                                }
                                            },
                                            {
                                                'component': 'VListItem',
                                                'props': {
                                                    'prepend-icon': 'mdi-folder-move',
                                                    'title': '目标路径',
                                                    'subtitle': f"{task.get('target_storage', 'local')}:{task.get('target_path', '未设置')}"
                                                }
                                            },
                                            {
                                                'component': 'VListItem',
                                                'props': {
                                                    'prepend-icon': 'mdi-transfer',
                                                    'title': '整理方式',
                                                    'subtitle': task.get('transfer_type', 'copy')
                                                }
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            })
        
        return [
            {
                'component': 'VRow',
                'content': task_cards
            },
            {
                'component': 'VRow',
                'content': [
                    {
                        'component': 'VCol',
                        'props': {
                            'cols': 12
                        },
                        'content': [
                            {
                                'component': 'VAlert',
                                'props': {
                                    'type': 'info',
                                    'variant': 'tonal'
                                },
                                'content': [
                                    {
                                        'component': 'div',
                                        'text': f'定时执行时间：{self._cron}'
                                    },
                                    {
                                        'component': 'div',
                                        'text': f'通知设置：{"开启" if self._notify else "关闭"}'
                                    },
                                    {
                                        'component': 'div',
                                        'text': f'处理延时：{self._process_delay}秒'
                                    },
                                    {
                                        'component': 'div',
                                        'text': f'批量大小：{self._batch_size}个文件'
                                    },
                                    {
                                        'component': 'div',
                                        'text': f'重试次数：{self._max_retries}次'
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]

    def get_service(self) -> List[Dict[str, Any]]:
        """
        注册插件公共服务
        [{
            "id": "服务ID",
            "name": "服务名称", 
            "trigger": "触发器：cron/interval/date/CronTrigger.from_crontab()",
            "func": self.xxx,
            "kwargs": {} # 定时器参数
        }]
        """
        if not self._enabled or not self._cron:
            return []
        
        return [{
            "id": "FileScanner",
            "name": "文件扫描整理",
            "trigger": CronTrigger.from_crontab(self._cron),
            "func": self.scan_and_transfer_task,
            "kwargs": {}
        }]

    def scan_and_transfer_task(self):
        """
        扫描文件并执行整理任务
        """
        if not self._enabled:
            return
        
        if not self._tasks:
            logger.warning("文件扫描整理：未配置任何任务")
            return
        
        logger.info("开始执行文件扫描整理任务...")
        
        # 统计信息
        total_tasks = 0
        success_tasks = 0
        failed_tasks = 0
        messages = []
        
        for task in self._tasks:
            if not isinstance(task, dict):
                continue
            
            # 检查任务是否启用
            if not task.get('enabled', True):
                continue
            
            task_name = task.get('name', '未命名任务')
            total_tasks += 1
            
            # 执行单个任务
            success = self._execute_single_task(task, task_name, messages)
            if success:
                success_tasks += 1
            else:
                failed_tasks += 1
        
        # 汇总结果
        logger.info(f"文件扫描整理任务完成: 总计 {total_tasks} 个, 成功 {success_tasks} 个, 失败 {failed_tasks} 个")
        
        # 发送通知
        if self._notify and messages:
            title = "文件扫描整理完成"
            text = f"总计: {total_tasks} | 成功: {success_tasks} | 失败: {failed_tasks}\n\n"
            text += "\n".join(messages)
            
            self.post_message(
                mtype=NotificationType.Plugin,
                title=title,
                text=text
            )

    def _execute_single_task(self, task: dict, task_name: str, messages: list) -> bool:
        """
        执行单个任务，带重试机制
        """
        try:
            # 验证必需参数
            source_path = task.get('source_path')
            target_storage = task.get('target_storage')
            target_path = task.get('target_path')
            
            if not all([source_path, target_storage, target_path]):
                logger.error(f"任务 [{task_name}] 缺少必需参数")
                messages.append(f"❌ {task_name}: 配置不完整")
                return False
            
            # 检查存储连接状态
            if not self._check_storage_connection(target_storage):
                logger.error(f"任务 [{task_name}] 目标存储 {target_storage} 连接失败")
                messages.append(f"❌ {task_name}: 存储连接失败")
                return False
            
            # 构建 FileItem
            fileitem = schemas.FileItem(
                storage="local",
                type="dir",
                path=source_path,
                name=Path(source_path).name
            )
            
            # 获取配置参数
            transfer_type = task.get('transfer_type', 'copy')
            min_filesize = task.get('min_filesize', 0)
            scrape = task.get('scrape', True)
            library_category_folder = task.get('library_category_folder', True)
            library_type_folder = task.get('library_type_folder', True)
            
            logger.info(f"执行任务 [{task_name}]: {source_path} -> {target_storage}:{target_path}")
            
            # 重试机制
            for retry_count in range(self._max_retries + 1):
                try:
                    # 添加延时避免API频率限制
                    if retry_count > 0:
                        wait_time = self._process_delay * (retry_count + 1)
                        logger.info(f"任务 [{task_name}] 第{retry_count}次重试，等待{wait_time}秒...")
                        time.sleep(wait_time)
                    
                    # 调用整理方法
                    success, message = self._transfer_chain.manual_transfer(
                        fileitem=fileitem,
                        target_storage=target_storage,
                        target_path=Path(target_path),
                        transfer_type=transfer_type,
                        min_filesize=min_filesize * 1024 * 1024 if min_filesize else 0,  # 转换为字节
                        scrape=scrape,
                        library_category_folder=library_category_folder,
                        library_type_folder=library_type_folder
                    )
                    
                    if success:
                        messages.append(f"✅ {task_name}: 整理成功")
                        logger.info(f"任务 [{task_name}] 执行成功")
                        return True
                    else:
                        # 检查是否是认证相关错误
                        if self._is_auth_error(message):
                            if retry_count < self._max_retries:
                                logger.warning(f"任务 [{task_name}] 认证失败，将进行重试: {message}")
                                continue
                        
                        messages.append(f"❌ {task_name}: {message}")
                        logger.error(f"任务 [{task_name}] 执行失败: {message}")
                        return False
                        
                except Exception as e:
                    error_msg = str(e)
                    
                    # 检查是否是认证相关异常
                    if self._is_auth_error(error_msg) and retry_count < self._max_retries:
                        logger.warning(f"任务 [{task_name}] 遇到认证异常，将进行重试: {error_msg}")
                        continue
                    
                    if retry_count >= self._max_retries:
                        messages.append(f"❌ {task_name}: {error_msg}")
                        logger.error(f"执行任务 [{task_name}] 重试{self._max_retries}次后仍失败: {error_msg}")
                        return False
                    
            return False
                    
        except Exception as e:
            messages.append(f"❌ {task_name}: {str(e)}")
            logger.error(f"执行任务 [{task_name}] 时发生异常: {str(e)}", exc_info=True)
            return False

    def _check_storage_connection(self, storage_name: str) -> bool:
        """
        检查存储连接状态
        """
        try:
            # 这里可以添加存储连接检查逻辑
            # 目前简单返回True，实际项目中可以调用存储模块的连接检查方法
            return True
        except Exception as e:
            logger.error(f"检查存储 {storage_name} 连接状态失败: {str(e)}")
            return False

    def _is_auth_error(self, error_message: str) -> bool:
        """
        判断是否是认证相关错误
        """
        auth_keywords = [
            "请先扫码登录",
            "refresh frequently",
            "access_token",
            "登录失败",
            "认证失败",
            "token",
            "unauthorized"
        ]
        
        if not error_message:
            return False
            
        error_lower = error_message.lower()
        return any(keyword.lower() in error_lower for keyword in auth_keywords)

    def post_message(self, mtype: NotificationType, title: str, text: str, **kwargs):
        """
        发送消息通知
        """
        eventmanager.send_event(
            etype="NoticeMessage",
            event_data={
                "type": mtype.value,
                "title": title,
                "text": text,
                **kwargs
            }
        )

    def stop_service(self):
        """
        停止插件
        """
        pass