#!/bin/bash

# 电子书分类器测试脚本

set -e

echo "=== 电子书分类器测试 ==="

# 创建测试目录
TEST_DIR="/tmp/ebook_test"
SOURCE_DIR="$TEST_DIR/source"
TARGET_DIR="$TEST_DIR/target"

echo "创建测试目录..."
rm -rf "$TEST_DIR"
mkdir -p "$SOURCE_DIR"
mkdir -p "$TARGET_DIR"

# 创建测试文件
echo "创建测试电子书文件..."
touch "$SOURCE_DIR/深入理解计算机系统.pdf"
touch "$SOURCE_DIR/三体.epub"
touch "$SOURCE_DIR/史记.txt"
touch "$SOURCE_DIR/经济学原理.pdf"
touch "$SOURCE_DIR/乔布斯传.mobi"
touch "$SOURCE_DIR/瑜伽入门.pdf"
touch "$SOURCE_DIR/巴黎旅行指南.epub"
touch "$SOURCE_DIR/未知书籍.pdf"

echo "测试文件创建完成，共 $(ls -1 "$SOURCE_DIR" | wc -l) 个文件"

# 测试帮助信息
echo ""
echo "=== 测试帮助信息 ==="
if go run ./apps/hydra/cmd/main.go ebook-classify --help; then
    echo "✓ 帮助信息显示正常"
else
    echo "✗ 帮助信息显示失败"
    exit 1
fi

# 测试试运行模式
echo ""
echo "=== 测试试运行模式 ==="
if go run ./apps/hydra/cmd/main.go ebook-classify \
    -s "$SOURCE_DIR" \
    -t "$TARGET_DIR" \
    --dry-run \
    --default-move \
    -l debug; then
    echo "✓ 试运行模式测试通过"
else
    echo "✗ 试运行模式测试失败"
    exit 1
fi

# 测试索引统计（应该显示空索引）
echo ""
echo "=== 测试索引统计 ==="
if go run ./apps/hydra/cmd/main.go ebook-classify \
    -t "$TARGET_DIR" \
    --show-stats; then
    echo "✓ 索引统计显示正常"
else
    echo "✗ 索引统计显示失败"
    exit 1
fi

# 清理测试目录
echo ""
echo "清理测试目录..."
rm -rf "$TEST_DIR"

echo ""
echo "=== 所有测试通过 ==="
echo ""
echo "使用说明："
echo "1. 设置 LLM API 密钥："
echo "   export OPENAI_API_KEY='your-api-key'"
echo ""
echo "2. 基本使用："
echo "   hydra ebook-classify -s /path/to/source -t /path/to/target --llm-api-key \$OPENAI_API_KEY"
echo ""
echo "3. 试运行模式："
echo "   hydra ebook-classify -s /path/to/source -t /path/to/target --dry-run"
echo ""
echo "4. 查看索引统计："
echo "   hydra ebook-classify -t /path/to/target --show-stats"
echo ""
echo "详细使用说明请查看: docs/ebook-classifier-usage.md"
