# Hydra

A comprehensive Go monorepo containing multiple CLI applications for system automation, file management, and data processing.

## 🏗️ Architecture

This is a monorepo containing multiple Go CLI applications:

### Main Applications
- **Hydra** (`apps/hydra/`) - Multi-purpose CLI for system automation, file management, and torrent operations
- **Tiver** (`apps/tiver/`) - Specialized tool for thread and image downloading/management with PDF generation
- **Licenser** (`apps/licenser/`) - License management utility
- **Offer** (`apps/offer/`) - Offer management utility  
- **Drior** (`apps/drior/`) - Full-stack server application with database and frontend

### Shared Libraries
- `shared/` - Common utilities shared across applications (JSON handling, logging, licensing)
- `internal/` - Internal packages for document handling, models, and services

## 🚀 Quick Start

### Prerequisites
- Go 1.19 or later
- Node.js 16+ (for frontend development)
- Make

### Installation

```bash
# Install Go dependencies
make deps

# Build all applications
make all

# Or build individually
make build-hydra
make build-tiver
make build-licenser
make build-offer
make build-drior
```

## 📦 Applications

### Hydra
Multi-purpose automation CLI with torrent client integration.

```bash
# Build and run
make build-hydra
./bin/hydra --help
```

### Tiver
Thread and image management tool with PDF generation capabilities.

```bash
# Build
make build-tiver

# Configure via environment variables (recommended)
export TIVER_DB_DSN="user:password@tcp(host:port)/database?tls=tidb&parseTime=true&loc=Asia%2FShanghai"
export TIVER_HTTP_USER_AGENT="Custom User Agent"
export TIVER_HTTP_PROXY="http://proxy:8080"
export TIVER_HTTP_COOKIE="session=abc123"
export TIVER_LOG_LEVEL="info"
export TIVER_DATA_DIR="./data"

# Run commands
./bin/tiver download --help
./bin/tiver refresh --help
```

### Drior
Full-stack application with backend API and frontend.

```bash
# Complete setup
make setup

# Development mode
make drior-server-dev    # Backend on :8080
make frontend-dev        # Frontend on :3000

# Or start both
make dev
```

## ⚙️ Configuration

### Tiver Configuration

Tiver supports configuration via environment variables for better security and flexibility:

#### Database Configuration
- `TIVER_DB_DSN` - Complete database connection string (recommended)
- `TIVER_DB_HOST` - Database host (default: gateway01.ap-southeast-1.prod.aws.tidbcloud.com)
- `TIVER_DB_PORT` - Database port (default: 4000)
- `TIVER_DB_USERNAME` - Database username
- `TIVER_DB_PASSWORD` - Database password
- `TIVER_DB_NAME` - Database name (default: tiver)
- `TIVER_DB_TLS_NAME` - TLS configuration name (default: tidb)

#### HTTP Configuration
- `TIVER_HTTP_USER_AGENT` - Custom User-Agent string
- `TIVER_HTTP_TIMEOUT` - HTTP timeout in seconds (default: 30)
- `TIVER_HTTP_PROXY` - Proxy URL for HTTP requests
- `TIVER_HTTP_COOKIE` - Authentication cookie

#### Application Configuration
- `TIVER_LOG_LEVEL` - Log level: debug, info, warn, error (default: info)
- `TIVER_DATA_DIR` - Data directory path (default: ./data)

### Legacy Configuration
If environment variables are not set, Tiver will use default values for compatibility.

## 🧪 Testing

```bash
# Run all tests
make test

# Test specific applications
make test-hydra
make test-tiver
make drior-test

# Code quality
make fmt      # Format code
make lint     # Run linter
make check    # Run fmt, lint, and test
```

## 🔧 Development

### Code Quality
```bash
make fmt      # Format Go code
make lint     # Run golangci-lint
make check    # Run fmt, lint, and test in sequence
```

### Dependency Management
```bash
make deps        # Install and tidy dependencies
make mod-update  # Update all dependencies
```

### Docker Support
```bash
make docker-hydra  # Build Hydra Docker image
make docker-tiver  # Build Tiver Docker image
```

## 🏢 Production Deployment

### Linux Builds
```bash
make build-hydra-linux
make build-tiver-linux
make build-licenser-linux
make build-offer-linux
```

### SystemD Services
SystemD service files are available in `deployments/systemd/` for production deployment.

### Docker Deployment
Docker configurations are available in `deployments/docker/` with docker-compose files for different environments.

## 📁 Project Structure

```
.
├── apps/                    # Main applications
│   ├── hydra/              # Hydra CLI application
│   ├── tiver/              # Tiver image/thread tool
│   │   ├── internal/
│   │   │   ├── app/        # Application initialization
│   │   │   ├── config/     # Configuration management
│   │   │   ├── errors/     # Error handling
│   │   │   ├── command/    # CLI commands
│   │   │   └── ...
│   ├── licenser/           # License utility
│   ├── offer/              # Offer utility
│   └── drior/              # Full-stack server
├── shared/                 # Shared libraries
│   ├── log/               # Logging utilities
│   ├── json/              # JSON handling
│   └── license/           # License management
├── configs/               # Configuration files
├── deployments/           # Deployment configs
│   ├── docker/           # Docker configurations
│   └── systemd/          # SystemD services
├── docs/                 # Documentation
└── internal/             # Internal shared packages
```

## 🔧 Key Integrations

- **Torrent Clients**: qBittorrent and Transmission API support
- **Slack Integration**: Webhook and bot functionality via slack-go/slack
- **Database**: SQLite, MySQL support via GORM and sqlx
- **Config Management**: JSON-based configuration with validation
- **Deployment**: Docker containers with systemd services

## 🤝 Contributing

1. Follow Go coding standards
2. Run `make check` before committing
3. Ensure all tests pass
4. Update documentation as needed

## 📋 Recent Improvements (2024)

### Tiver Architecture Optimization
- ✅ Fixed package naming issues (`pkg` → `internal`)
- ✅ Externalized configuration management with environment variable support
- ✅ Fixed concurrent safety issues with proper mutex protection
- ✅ Eliminated code duplication with unified initialization
- ✅ Standardized error handling with structured error types
- ✅ Enhanced security by removing hardcoded credentials

### Benefits
- **Security**: Sensitive data now configurable via environment variables
- **Maintainability**: Reduced code duplication and improved structure
- **Reliability**: Fixed concurrency issues and standardized error handling
- **Flexibility**: Easy configuration for different environments

## 📄 License

[Add your license information here]