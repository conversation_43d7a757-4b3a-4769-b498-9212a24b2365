# ==============================================================================
# 变量定义 (Variables)
# ==============================================================================

# SSH 和 SCP 配置
SSH_USER_HOST := root@***********
SSH_PORT      := 36001
SCP_CMD       := scp -O -P $(SSH_PORT)
SSH_CMD       := ssh -p $(SSH_PORT) $(SSH_USER_HOST)

# LH Server SSH 配置
LH_SSH_HOST := root@**************
LH_SSH_CMD  := ssh $(LH_SSH_HOST)
LH_SCP_CMD  := scp

# 本地路径 (Local Paths)
LOCAL_DEPLOY_DIR   := deployments
LOCAL_CLASH_CONFIG := $(LOCAL_DEPLOY_DIR)/docker/clash/clash-monocloud.yaml
LOCAL_SSL_DIR      := $(LOCAL_DEPLOY_DIR)/ssl
LOCAL_FRPC_CONFIG  := $(LOCAL_DEPLOY_DIR)/frp/frpc-lh.toml
LOCAL_FRPC_SCRIPT  := $(LOCAL_DEPLOY_DIR)/frp/scripts/frpc-lh_control.sh

# NAS 目标路径 (NAS Target Paths)
NAS_BASE_SERVICE_DIR    := /volume2/service
NAS_CLASH_CONFIG_TARGET := $(NAS_BASE_SERVICE_DIR)/dat/hydra/docker/clash/clash-monocloud.yaml
NAS_FRPC_DIR            := $(NAS_BASE_SERVICE_DIR)/bin/frp
NAS_FRPC_CONFIG_TARGET  := $(NAS_FRPC_DIR)/frpc-lh.toml
NAS_FRPC_SCRIPT_TARGET  := $(NAS_FRPC_DIR)/frpc-lh_control.sh
NAS_SSL_BACKUP_DIR      := /volume2/backup/ArchiveOfStars/ssl

# SSL 模块 (SSL Modules)
SSL_MODULES := \
	em.hydra.cab_apache \
	ha.hydra.cab_apache \
	hydra.cab_apache \
	mp.hydra.cab_apache \
	qb.hydra.cab_apache

# systemd 文件 (systemd Files)
SYSTEMD_FILES := \
	restart-mp2.timer \
	restart-emby302.timer \
	restart-embyx302.timer \
	restart-hydra-chown.timer \
	hydra-build-index.timer \
	hydra-file-hardlink.timer \
	hydra-start-webhook.timer \
	hydra-torrent-transfer.timer \
	hydra-prune.timer \
	start-lh-frpc.timer \
	restart-mp2.service \
	restart-emby302.service \
	restart-embyx302.service \
	restart-hydra-chown.service \
	hydra-build-index.service \
	hydra-file-hardlink.service \
	hydra-start-webhook.service \
	hydra-torrent-transfer.service \
	hydra-prune.service \
	start-lh-frpc.service \
	enable_custom.sh

NAS_SYSTEMD_DIR := /etc/systemd/system

# ==============================================================================
# 任务目标 (Targets)
# ==============================================================================
.PHONY: clash ssl frpc systemd compose status

clash:
	# 将clash-monocloud.yaml文件拷贝到NAS的指定目录
	@echo "Copying clash-monocloud.yaml to NAS via port $(SSH_PORT) ..."
	$(SCP_CMD) $(LOCAL_CLASH_CONFIG) $(SSH_USER_HOST):$(NAS_CLASH_CONFIG_TARGET)

ssl:
	# 将SSL证书文件部署到NAS的Apache配置目录
	@echo "Deploying SSL certificates to NAS at $(NAS_SSL_BACKUP_DIR) ..."
	@$(foreach module,$(SSL_MODULES), \
		echo "--> Deploying module: $(module)"; \
		$(SCP_CMD) $(LOCAL_SSL_DIR)/$(module)/* $(SSH_USER_HOST):$(NAS_SSL_BACKUP_DIR)/$(module)/; \
	)
	@echo "SSL certificate deployment completed successfully"

frpc:
	# 将frpc.toml文件部署到NAS的frp配置目录
	@echo "Deploying frpc configuration to NAS at $(NAS_FRPC_DIR) via port $(SSH_PORT) ..."
	$(SCP_CMD) $(LOCAL_FRPC_CONFIG) $(SSH_USER_HOST):$(NAS_FRPC_CONFIG_TARGET)
	$(SCP_CMD) $(LOCAL_FRPC_SCRIPT) $(SSH_USER_HOST):$(NAS_FRPC_SCRIPT_TARGET)

	# 重启frpc服务
	@echo "Restarting frpc service on NAS ..."
	$(SSH_CMD) "bash $(NAS_FRPC_SCRIPT_TARGET) restart"
	
	@echo "frpc service restart completed successfully"

systemd:
	# 将 systemd 文件部署到 NAS
	@echo "Deploying systemd files to NAS at $(NAS_SYSTEMD_DIR) ..."
	@$(foreach file,$(SYSTEMD_FILES), \
		echo "--> Deploying: $(file)"; \
		$(SCP_CMD) $(LOCAL_DEPLOY_DIR)/systemd/$(file) $(SSH_USER_HOST):$(NAS_SYSTEMD_DIR)/; \
	)

	# 授予 enable_custom.sh 执行权限并运行它
	@echo "Initializing systemd services on NAS ..."
	$(SSH_CMD) "chmod +x $(NAS_SYSTEMD_DIR)/enable_custom.sh && $(NAS_SYSTEMD_DIR)/enable_custom.sh"

	@echo "Systemd services initialized successfully."

compose:
	# 将所有 docker-compose YAML 文件拷贝到 NAS 的 /volume2/service/compose 目录
	@echo "Copying docker-compose YAML files to NAS:/volume2/service/compose ..."
	@for file in $(LOCAL_DEPLOY_DIR)/docker/*.yaml; do \
		$(SCP_CMD) $$file $(SSH_USER_HOST):/volume2/service/compose/; \
		echo "--> Copied: $$file"; \
	done
	@echo "All docker-compose files copied successfully."

# 检查 systemd 服务状态并汇总结果
.PHONY: check-systemd-status
status check-systemd-status:
	# 远程检查所有已部署 systemd 服务的状态，并统计成功/失败数量，输出失败详情
	@echo "Checking systemd services status on NAS ..."
	@SUCCESS=0; \
	FAILED=0; \
	FAILED_SERVICES=""; \
	FAILED_DETAILS=""; \
	for file in $(SYSTEMD_FILES); do \
		if echo $$file | grep -q ".service"; then \
			service_name=$${file}; \
			log_msg="Checking status for: $${service_name}"; \
			echo "--> $$log_msg"; \
			STATUS_OUTPUT=$$( $(SSH_CMD) "systemctl status $${service_name} --no-pager 2>&1" ); \
			if echo "$$STATUS_OUTPUT" | grep -q "Active: active (running)"; then \
				SUCCESS=$$((SUCCESS+1)); \
				echo "[OK] $${service_name} is running"; \
			else \
				FAILED=$$((FAILED+1)); \
				FAILED_SERVICES="$$FAILED_SERVICES $${service_name}"; \
				FAILED_DETAILS="$$FAILED_DETAILS\n[ERROR] $${service_name}:\n$$STATUS_OUTPUT\n"; \
				echo "[ERROR] $${service_name} is not running"; \
			fi; \
		fi; \
	done; \
	TOTAL=$$((SUCCESS+FAILED)); \
	echo "\nSummary: $$SUCCESS success, $$FAILED failed, $$TOTAL total"; \
	if [ $$FAILED -gt 0 ]; then \
		echo "\nFailed service details:"; \
		echo "$$FAILED_DETAILS"; \
	fi
	@echo "Systemd status check completed."

# ==============================================================================
# 构建和安装目标 (Build and Install Targets)
# ==============================================================================

# Go binary names
HYDRA_BINARY    := hydra
TIVER_BINARY    := tiver
LICENSER_BINARY := licenser
OFFER_BINARY    := offer

# GOBIN path (defaults to $GOPATH/bin if not set)
GOBIN ?= $(shell go env GOPATH)/bin

.PHONY: deps build-hydra build-tiver build-licenser build-offer build all

# Install dependencies
deps:
	@echo "Installing Go dependencies..."
	go mod download
	go mod tidy

# Build targets
build-hydra:
	@echo "Building Hydra..."
	go build -o bin/$(HYDRA_BINARY) ./apps/hydra/cmd/main.go

build-tiver:
	@echo "Building Tiver..."
	go build -o bin/$(TIVER_BINARY) ./apps/tiver/cmd/main.go

build-licenser:
	@echo "Building Licenser..."
	go build -o bin/$(LICENSER_BINARY) ./apps/licenser/cmd/main.go

build-offer:
	@echo "Building Offer..."
	go build -o bin/$(OFFER_BINARY) ./apps/offer/cmd/main.go


# Build all binaries
build: build-hydra build-tiver build-licenser build-offer 

all: deps build

# Install targets
.PHONY: install-hydra install-tiver install-licenser install-offer  install

install-hydra: build-hydra
	@echo "Installing Hydra to $(GOBIN)..."
	@mkdir -p $(GOBIN)
	cp bin/$(HYDRA_BINARY) $(GOBIN)/
	@echo "Hydra installed to $(GOBIN)/$(HYDRA_BINARY)"

install-tiver: build-tiver
	@echo "Installing Tiver to $(GOBIN)..."
	@mkdir -p $(GOBIN)
	cp bin/$(TIVER_BINARY) $(GOBIN)/
	@echo "Tiver installed to $(GOBIN)/$(TIVER_BINARY)"

install-licenser: build-licenser
	@echo "Installing Licenser to $(GOBIN)..."
	@mkdir -p $(GOBIN)
	cp bin/$(LICENSER_BINARY) $(GOBIN)/
	@echo "Licenser installed to $(GOBIN)/$(LICENSER_BINARY)"

install-offer: build-offer
	@echo "Installing Offer to $(GOBIN)..."
	@mkdir -p $(GOBIN)
	cp bin/$(OFFER_BINARY) $(GOBIN)/
	@echo "Offer installed to $(GOBIN)/$(OFFER_BINARY)"


# Install all binaries
install: install-hydra install-tiver install-licenser install-offer 
	@echo "All binaries installed to $(GOBIN)"

# Deploy tiver to remote host (LH server - AMD64 Linux)
.PHONY: lh-tiver
lh-tiver:
	@echo "Building Tiver for AMD64 Linux..."
	GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o bin/$(TIVER_BINARY)-amd64 ./apps/tiver/cmd/main.go
	@echo "Compressing with UPX..."
	upx --best --lzma bin/$(TIVER_BINARY)-amd64
	@echo "Deploying Tiver binary to $(LH_SSH_HOST):/root/gobin ..."
	$(LH_SSH_CMD) "mkdir -p /root/gobin"
	$(LH_SCP_CMD) bin/$(TIVER_BINARY)-amd64 $(LH_SSH_HOST):/root/gobin/$(TIVER_BINARY)
	$(LH_SSH_CMD) "chmod +x /root/gobin/$(TIVER_BINARY)"
	@echo "Tiver deployed successfully to $(LH_SSH_HOST):/root/gobin/$(TIVER_BINARY)"
	@rm -f bin/$(TIVER_BINARY)-amd64

# Deploy hydra to NAS (***********)
.PHONY: hydra
hydra:
	@echo "Building Hydra for Linux/AMD64..."
	GOOS=linux GOARCH=amd64 go build -o bin/$(HYDRA_BINARY)-linux ./apps/hydra/cmd/main.go
	@echo "Deploying Hydra binary to $(SSH_USER_HOST):/volume2/service/bin/hydra ..."
	$(SSH_CMD) "mkdir -p /volume2/service/bin/hydra"
	$(SSH_CMD) "rm -f /volume2/service/bin/hydra/hydra"
	$(SCP_CMD) bin/$(HYDRA_BINARY)-linux $(SSH_USER_HOST):/volume2/service/bin/hydra/hydra
	$(SSH_CMD) "chmod +x /volume2/service/bin/hydra/hydra"
	@echo "Hydra deployed successfully to $(SSH_USER_HOST):/volume2/service/bin/hydra/hydra"
	@rm -f bin/$(HYDRA_BINARY)-linux

# Deploy tiver to NAS (***********)
.PHONY: tiver
tiver:
	@echo "Building Tiver for Linux/AMD64..."
	GOOS=linux GOARCH=amd64 go build -o bin/$(TIVER_BINARY)-linux ./apps/tiver/cmd/main.go
	@echo "Deploying Tiver binary and web resources to $(SSH_USER_HOST):/volume2/service/bin/tiver ..."
	$(SSH_CMD) "mkdir -p /volume2/service/bin/tiver/web"
	$(SSH_CMD) "rm -f /volume2/service/bin/tiver/tiver"
	$(SCP_CMD) bin/$(TIVER_BINARY)-linux $(SSH_USER_HOST):/volume2/service/bin/tiver/tiver
	$(SSH_CMD) "chmod +x /volume2/service/bin/tiver/tiver"
	@echo "Copying web resources..."
	$(SCP_CMD) -r apps/tiver-web/* $(SSH_USER_HOST):/volume2/service/bin/tiver/web/
	@echo "Tiver deployed successfully to $(SSH_USER_HOST):/volume2/service/bin/tiver/"
	@rm -f bin/$(TIVER_BINARY)-linux

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	@echo "Clean completed."

# ==============================================================================
# Tiver Web Server
# ==============================================================================
.PHONY: tiver-serve tiver-serve-dev

tiver-serve: build-tiver
	@echo "Starting Tiver web server on port 8030..."
	@./bin/$(TIVER_BINARY) serve --port 8030

tiver-serve-dev:
	@echo "Starting Tiver web server in development mode..."
	@go run apps/tiver/cmd/main.go serve --port 8030