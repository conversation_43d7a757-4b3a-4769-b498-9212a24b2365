# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

- `make deps` - Install Go dependencies and run go mod tidy
- `make build-hydra` - Build the main Hydra CLI tool
- `make build-tiver` - Build the Tiver image/thread management tool  
- `make build-licenser` - Build the Licenser utility
- `make build-offer` - Build the Offer utility
- `make build-drior` - Build the Drior server application with database support
- `make all` - Build all applications

## Testing

- `make test` - Run all tests across the project
- `make test-hydra` - Run tests for Hydra app only
- `make test-tiver` - Run tests for Tiver app only
- `make drior-test` - Run tests for Drior app only

## Code Quality

- `make fmt` - Format Go code using go fmt
- `make lint` - Run golangci-lint on the entire codebase  
- `make check` - Run fmt, lint, and test in sequence

## Development (Drior Full-Stack)

- `make setup` - Complete project setup including backend build, database migration, and frontend dependencies
- `make drior-server-dev` - Start Drior server in development mode on port 8080
- `make frontend-dev` - Start frontend development server
- `make dev` - Start both backend and frontend for full-stack development

## Architecture

This is a monorepo containing multiple Go CLI applications in the `apps/` directory:

### Main Applications
- **Hydra** (`apps/hydra/`) - Multi-purpose CLI for system automation, file management, and torrent operations
- **Tiver** (`apps/tiver/`) - Specialized tool for thread and image downloading/management
- **Licenser** (`apps/licenser/`) - License management utility
- **Offer** (`apps/offer/`) - Offer management utility  
- **Drior** (`apps/drior/`) - Full-stack server application with database and frontend

### Shared Libraries
- `shared/` - Common utilities shared across applications (JSON handling, logging, licensing)
- `internal/` - Internal packages for document handling, models, and services

### Key Integrations
- **Torrent Clients**: Supports both qBittorrent and Transmission via their APIs
- **Slack Integration**: Uses slack-go/slack for webhook and bot functionality
- **Database**: Supports SQLite, MySQL via GORM and sqlx
- **Config Management**: JSON-based configuration with validation
- **Deployment**: Docker containers with systemd services for production

### Command Structure
Both Hydra and Tiver use cobra for CLI commands with a common pattern:
- Each app has a main command root in `cmd/main.go`
- Individual commands are in `internal/command/` 
- Configuration is handled in `internal/config/`
- Business logic is in `pkg/` (for Hydra) or `internal/` (for Tiver)

### Docker & Deployment
- Docker configurations in `deployments/docker/`
- SystemD service files in `deployments/systemd/`
- Remote deployment automation via Makefile targets
- Production configurations in `configs/`

## Frontend (for Drior)

The project includes a frontend component for the Drior application:

- `make frontend-install` - Install frontend dependencies
- `make frontend-build` - Build frontend for production
- `make frontend-lint` - Lint frontend code
- `make frontend-test` - Run frontend tests