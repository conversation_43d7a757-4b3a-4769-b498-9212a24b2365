# ================================
# 1. 环境变量（PATH & ENVIRONMENT VARIABLES）
# ================================
# -- Go 环境 --
export GOPATH="$HOME/go"
export PATH="$GOPATH/bin:$PATH"
export PATH="/opt/homebrew/opt/go@1.23/bin:$PATH"

# -- JAVA 环境 --
# 只保留一个生效的 JAVA_HOME，其他用注释说明
# export JAVA_HOME="/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home"
# export JAVA_HOME="/Library/Java/JavaVirtualMachines/jdk-21.0.7.jdk/Contents/Home"
# export JAVA_HOME="/Library/Java/JavaVirtualMachines/jdk-24.0.1.jdk/Contents/Home"
export JAVA_HOME="/Library/Java/JavaVirtualMachines/graalvm-jdk-24.0.1+9.1/Contents/Home"
export CLASS_PATH="$JAVA_HOME/lib"
export PATH="$PATH:$JAVA_HOME/bin"

# -- MySQL --
export PATH="/opt/homebrew/opt/mysql/bin:$PATH"
export MYSQL_PS1="(\u@\h) [\d]> "

# -- 其他环境变量 --
export LD_LIBRARY_PATH="$HOME/Downloads/instantclient_19_8"
export DYLD_LIBRARY_PATH="$HOME/Downloads/instantclient_19_8"
export DISABLE_AUTO_UPDATE="true"
export GOTOOLCHAIN="local"
export LC_ALL="en_US.UTF-8"
export HOMEBREW_BOTTLE_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles"
export HOMEBREW_BREW_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/brew.git"
export HOMEBREW_CORE_GIT_REMOTE="https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/homebrew-core.git"
export HOMEBREW_NO_AUTO_UPDATE=true

# -- 代理设置 --
export https_proxy=http://127.0.0.1:6152
export http_proxy=http://127.0.0.1:6152
export all_proxy=socks5://127.0.0.1:6153

# -- Dev 环境 --
export PATH="$PATH:$GOROOT/bin"

# ====================
# 2. Oh-My-Zsh 配置
# ====================
export ZSH="$HOME/.oh-my-zsh"
ZSH_THEME="robbyrussell"
plugins=(
  git
  aliases
  catimg
  command-not-found
  common-aliases
  docker-compose
  git-extras
  extract
  zsh-autosuggestions
  zsh-completions
  zsh-syntax-highlighting
  z
)
source $ZSH/oh-my-zsh.sh
# source ~/Dat/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh
# source ~/Dat/zsh-autosuggestions/zsh-autosuggestions.zsh
# source ~/Dat/zsh-completions/zsh-completions.plugin.zsh

# ====================
# 3. 自定义函数（Functions）
# ====================
function f() { find . -iname "*$1*" "${@:2}"; } # 查找文件
function r() { grep "$1" "${@:2}" -R .; } # 递归查找内容
function mkcd() { mkdir -p "$@" && cd "$_"; } # 创建并进入目录
function cursor { open -a "/Applications/Cursor.app" "$@"; } # 用 Cursor 打开文件

function venvv() {
    if [ -z "$1" ]; then
        echo "使用方法: venvv <环境名称>"
        return 1
    fi

    local env_name="$1"
    local env_path="./$env_name" # 默认在当前目录创建
    # local env_path="$HOME/envs/$env_name" # 示例: 也可以指定一个统一的虚拟环境存放目录

    # 检查虚拟环境是否已存在
    if [ -d "$env_path" ]; then
        echo "虚拟环境 '$env_name' 已存在，正在激活..."
        source "$env_path/bin/activate"
        echo "已激活环境: $(basename "$VIRTUAL_ENV")"
    else
        echo "虚拟环境 '$env_name' 不存在，正在创建并激活..."
        # 查找最新安装的Python 3版本
        # 优先使用 'python3'，如果不存在则尝试 'python' (不推荐，但作为备用)
        # 也可以指定一个你偏好的版本，例如 /usr/local/bin/python3.10
        local python_cmd=$(command -v python3 || command -v python)

        if [ -z "$python_cmd" ]; then
            echo "错误: 未找到 Python 3 或 Python 解释器。请确保已安装 Python。"
            return 1
        fi

        echo "使用 '$python_cmd' 创建虚拟环境..."
        "$python_cmd" -m venv "$env_path"
        if [ $? -ne 0 ]; then
            echo "错误: 虚拟环境创建失败。请检查 '$python_cmd' 是否能正常运行。"
            return 1
        fi

        source "$env_path/bin/activate"
        echo "虚拟环境 '$env_name' 已创建并激活。"
        echo "提示: 你可能需要运行 'pip install --upgrade pip' 来更新 pip。"
    fi
}

# ====================
# 4. 用户自定义（User Customization）
# ====================
# export MANPATH="/usr/local/man:$MANPATH"
# export ARCHFLAGS="-arch x86_64"
# export SSH_KEY_PATH="~/.ssh/rsa_id"
# export EDITOR='vim'
# eval "$(pyenv init -)"
# eval "$(pyenv virtualenv-init -)"
# alias zshconfig="mate ~/.zshrc"
# alias ohmyzsh="mate ~/.oh-my-zsh"

# ========== END ==========
. "$HOME/.local/bin/env"

# ====================
# 5. 别名（Aliases）
# ====================
# -- 通用 --
alias vb='vi ~/.zshrc'
alias sb='source ~/.zshrc'
alias dat='cd ~/Dat'
alias csd="cd /Users/<USER>/Documents/PingCAP/SandDraft"
alias sd="cursor '/Users/<USER>/Documents/PingCAP/SandDraft'"
alias dow='cd ~/Downloads'
alias gl='git log'
alias gosrc='cd ~/go/src'
alias github='cd ~/go/src/github.com'
alias gobin='cd /Users/<USER>/go/bin/'
alias gbin='cd /Users/<USER>/go/bin/'
alias count='gocloc'
alias localhost="ifconfig -a|grep inet|grep -v 127.0.0.1|grep -v inet6|awk '{print $2}'|tr -d 'addr:'"
alias his='history'
alias copy='pbcopy'
alias json='python -m json.tool '
alias tailf='tail -f'
alias sj='date "+%Y-%m-%d %H:%M:%S" | pbcopy'
alias sjd='date "+%Y-%m-%d %H:%M:%S"'
alias rq='date "+%Y-%m-%d" | pbcopy'
alias rqd='date "+%Y-%m-%d"'
alias ds='du -h | sort -h'
alias k='killall -9 SCIM_Extension'

# -- 项目跳转 --
alias rds='cd /Users/<USER>/Dat/rds_public_tools'
alias hy='cd /Users/<USER>/go/src/github.com/penwyp/hydra'
alias ka='cd /Users/<USER>/go/src/github.com/penwyp/kaf-cli'
alias ts='cd /Users/<USER>/Dat/tms-suite'
alias gt='cd /Users/<USER>/go/src/github.com/penwyp/go-tutorial'
alias tms='cd /Users/<USER>/go/src/github.com/penwyp/tms'
alias tmsc='cd /Users/<USER>/go/src/github.com/penwyp/tms-compare'
alias tmc='cd /Users/<USER>/go/src/github.com/penwyp/tms-compare'
alias surge='cd ~/Library/Mobile\ Documents/com~apple~CloudDocs/Surge'
alias dbt='cd /Users/<USER>/Dat/dbt5'
alias p='/Users/<USER>/go/src/github.com/pingcap/tidb/pkg/parser'
alias d='/Users/<USER>/Dat/druid'
alias op='/Users/<USER>/Dat/ObjectParser'
alias pv='cd "/Users/<USER>/Documents/PingCAP Document/VS草稿"'
alias e='cd /Users/<USER>/go/src/github.com/DoraZa/mini-ecvault'
alias m='cd /Users/<USER>/go/src/github.com/DoraZa/mini-gateway'
alias me='cd /Users/<USER>/go/src/github.com/DoraZa/mini-edulive'
alias tidb='cd /Users/<USER>/go/src/github.com/pingcap/tidb'
alias trs='/Users/<USER>/go/src/github.com/wentaojin/transferdb'
alias transfer='/Users/<USER>/go/src/github.com/wentaojin/transferdb'
alias logs='/Users/<USER>/go/src/github.com/penwyp/tms/data/logs'
alias bar='go build -o /Users/<USER>/go/src/github.com/penwyp/tms/bin/tms-web-bar cmd/web/main.go && date && ./bin/tms-web-bar  -config data/config-cluster.toml'
alias j="java -jar tmsctl/target/tmsctl-1.0-SNAPSHOT.jar"
alias jj='./tmsctl/target/tmsctl'

# -- SSH 快捷方式 --
alias nas='ssh root@192.168.6.1 -p 36001'
alias nas2='ssh root@192.168.7.1 -p 36001'
alias tas='ssh root@100.113.54.32 -p 36001'
alias ras='ssh root@127.0.0.1 -p 36001'
alias fas='ssh root@127.0.0.1 -p 36001'
alias lh='ssh root@129.226.210.60'
alias lh2='ssh root@38.54.125.67'
alias gy='ssh root@149.104.22.38'
alias pve='ssh <EMAIL> -p 14766'
# -- PingCAP SSH --
alias de="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/tms ; exec zsh'"
alias dd="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  doraza@10.2.103.35 'cd /home/<USER>/projects ; exec zsh'"
alias des="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/tms-suite ; exec zsh'"
alias dop="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/tms_parser ; exec zsh'"
alias deq="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/tms_sql ; exec zsh'"
alias dea="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/tms_all ; exec zsh'"
alias tmq="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/tms_sql ; exec zsh'"
alias mm="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/mini-gateway ; exec zsh'"
alias mme="ssh -t -o ServerAliveInterval=60 -o ConnectTimeout=240  penwyp@10.2.103.35 'cd /home/<USER>/projects/mini-edulive ; exec zsh'"
alias de15="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  penwyp@10.2.103.15"
alias de2="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  penwyp@10.2.103.200"
alias de200="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.200 -i ~/Downloads/lijie_ras"
alias de15x="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.15 -i ~/Downloads/lijie_ras"
alias de15r="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.15 -i ~/Downloads/lijie_ras"
alias de105="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.105 -i ~/Downloads/lijie_ras"
alias de24="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.24 -i ~/Downloads/lijie_ras"
alias de33="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.33 -i ~/Downloads/lijie_ras"
alias deor="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.33 -i ~/Downloads/lijie_ras"
alias de35="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.35 -i ~/Downloads/lijie_ras"
alias de35dsg="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  dsg@10.2.103.35"
alias de55r="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60  root@10.2.103.55 -i ~/Downloads/lijie_ras"
alias de55="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60 dsg@10.2.103.55"
alias de59="ssh -o ConnectTimeout=240 -o ServerAliveInterval=60 dsg@10.2.103.59"
alias deor21="ssh oracle@10.2.103.21"
alias de21="ssh root@10.2.103.21"

# -- MySQL 快捷方式 --
alias db="mysql -h 10.2.103.24 -utims -ptims -Dtimsdev -A"
alias dbc="mysql -h 10.2.103.24 -utims -ptims -Dtms_cluster -A"
alias dbcm="mysql -h 10.2.103.24 -utims -ptims -Dtms_cluster_multi -A"
alias dbm="mysql -h 10.2.103.24 -utims -ptims -Dtms_cluster_multi -A"
alias tdb="mysql -P4000 -h10.2.103.15 -Dinformation_schema -utimsdev -ptimsdev -A"
alias tdbroot="mysql -P4000 -h10.2.103.15 -Dinformation_schema -uroot -pts123456 -A"
alias ti="mysql --comments --connect-timeout 15 -u '3XpA6zRuPTSNjpf.root' -h gateway01.ap-southeast-1.prod.aws.tidbcloud.com -P 4000 -D tiver --ssl-mode=VERIFY_IDENTITY --ssl-ca=/etc/ssl/cert.pem -pTlRWzh9eKnW8BvcN"

# -- Git 快捷方式 --
alias gs="git status"
alias gcm="git checkout main"
alias gcd="git checkout develop"
alias gcs="git checkout staging"
alias gsm="git summary"
alias ga='git add'
alias gd='git diff'
alias gf='git fetch'
alias gla='git log --oneline'
alias grv='git remote -v'
alias grb='git rebase'
alias gb='git branch'
alias gpf="git push -f"
alias gpl="git pull"
alias gps="git push"
alias gc="git commit -m"
alias gm="gitmoji"
alias gca="git commit --amend"
alias gbda="git branch | egrep -v 'master|develop' | xargs git branch -D"
alias gco='git checkout '

# -- Go Modules --
alias deps="go mod tidy && go mod download"

# -- 其他 --
alias kaf='kaf-cli'
alias s="rsync -av --ignore-existing --exclude '.*' /Users/<USER>/Movies/ /Volumes/backup/Mac/Movies/"
alias dora='git config user.name DoraZa && git config user.email <EMAIL>'

# -- Docker --
alias dit='docker exec -it '

# for ccr
alias goc="cd /Users/<USER>/go/src/github.com/penwyp/go-claude"
alias bs="brew search"

# Amazon Q pre block
alias qq='[[ -f "${HOME}/Library/Application Support/amazon-q/shell/zshrc.pre.zsh" ]] && builtin source "${HOME}/Library/Application Support/amazon-q/shell/zshrc.pre.zsh" && [[ -f "${HOME}/Library/Application Support/amazon-q/shell/zshrc.post.zsh" ]] && builtin source "${HOME}/Library/Application Support/amazon-q/shell/zshrc.post.zsh"'
alias to="ssh root@216.234.143.68 -p 36001"
alias hg="cd /Users/<USER>/Dat/HistoryGenius"
alias cc="ccusage"
