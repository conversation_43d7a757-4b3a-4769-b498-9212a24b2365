export PATH=$HOME/bin:/volume2/service/bin/hydra:/usr/local/bin:$PATH
export ZSH="$HOME/.oh-my-zsh"
export https_proxy=http://10.10.10.66:17890;export http_proxy=http://10.10.10.66:17890;export all_proxy=socks5://10.10.10.66:17891

ZSH_THEME="jnrowe"

plugins=(
    git
    aliases
    catimg
    command-not-found
    common-aliases
    dirhistory
    docker-compose
    git-extras
    extract
    z
    sublime
    zsh-autosuggestions
    zsh-completions
    zsh-syntax-highlighting
)

source $ZSH/oh-my-zsh.sh

# alias for pen.wu
alias vb='vi ~/.zshrc'
alias sb='source ~/.zshrc'
alias s='hydra search -q '
alias b='hydra build-index'
alias r='hydra rm -e -p '
alias dr='hydra rm -p '
alias tailf='tail -f '
alias ll='ls -alh '
alias cdd='cd /volume2/service/compose '
alias oh='cd /volume2/onlyhub '
alias ds='du -h --max-depth=1 | sort -h'
alias ds2='du -h --max-depth=2 | sort -h'
alias iftop='sudo docker run -it --rm --net host quay.io/coppkaw/iftop -P -i eth0'

cd /volume2/theatre