{"link_type": "hard", "paths_mapping": [{"ori_path": "/volume2/pt/黑洞", "dest_path": "/volume2/media_library/黑洞"}, {"ori_path": "/volume2/pt/短剧", "dest_path": "/volume2/media_library/短剧"}, {"ori_path": "/volume2/pt/电视剧", "dest_path": "/volume2/media_library/电视剧"}, {"ori_path": "/volume2/pt/电影", "dest_path": "/volume2/media_library/电影"}, {"ori_path": "/volume2/pt/纪录片", "dest_path": "/volume2/media_library/纪录片"}, {"ori_path": "/volume2/pt/电视节目", "dest_path": "/volume2/media_library/电视节目"}, {"ori_path": "/volume2/pt/演唱会", "dest_path": "/volume2/media_library/演唱会"}, {"ori_path": "/volume2/theatre/R18/香港", "dest_path": "/volume2/onlyhub/avis_hk", "linux_acl": 777}, {"ori_path": "/volume2/pt/.acache/avis_japan", "dest_path": "/volume2/onlyhub/avis_japan", "include_all_file": true, "text_replace": [{"from": "-uncensored.", "to": ".", "case_sensitive": true}, {"from": "_Uncen.", "to": ".", "case_sensitive": true}]}, {"ori_path": "/volume2/pt/.acache/avis_china", "dest_path": "/volume2/onlyhub/avis_china", "include_all_file": false, "size_limit_mb": 30}, {"ori_path": "/volume2/pt/.acache/deepfakes", "dest_path": "/volume2/onlyhub/deepfakes", "include_all_file": true}, {"ori_path": "/volume2/pt/.acache/books", "dest_path": "/volume2/onlyhub/books", "include_all_file": true, "linux_acl": 777}], "includes": ["mp4", "flv", "f4v", "webm", "m4v", "mov", "cpk", "dirac", "3gp", "3g2", "rm", "rmvb", "wmv", "avi", "asf", "mpg", "bdjo", "mpls", "mpeg", "clpi", "bdmv", "mpe", "vob", "mkv", "ts", "ram", "qt", "fli", "flc", "mod", "m2ts", "iso"], "excludes": [], "exclude_name_regex": ["pt/\\.qbcache", "sample\\..*", "Sample\\..*", "Back\\.To\\.Field\\.2020\\.Upgraded.*", "Back\\.To\\.Field\\.Plus\\.2021.*", "Back\\.To\\.Field\\.Slow\\.2021.*", "Back\\.To\\.<PERSON>\\.<PERSON>\\.2021.*", "Back\\.To\\.Field\\.Extra\\.2021.*", "\\.S\\d+E00\\.", "\\.S\\d+SP\\d+\\."], "exclude_folder_names": ["@eaDir", "<PERSON><PERSON>", ".AppleDB"], "delete_dir": false, "keep_dir_struct": true, "open_cache": false, "mkdir_if_single": true}