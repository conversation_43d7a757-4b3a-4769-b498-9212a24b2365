package services

import (
	"database/sql"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/penwyp/hydra/internal/models"
)

type DocumentService struct {
	db          *gorm.DB
	uploadDir   string
	maxFileSize int64
}

func NewDocumentService(db *gorm.DB, uploadDir string, maxFileSize int64) *DocumentService {
	return &DocumentService{
		db:          db,
		uploadDir:   uploadDir,
		maxFileSize: maxFileSize,
	}
}

// CreateDocument uploads and creates a new document
func (s *DocumentService) CreateDocument(file *multipart.FileHeader, title string, isPublic bool, userID uint) (*models.Document, error) {
	// Validate file
	if err := s.validateFile(file); err != nil {
		return nil, err
	}

	// Generate unique filename
	filename := s.generateFilename(file.Filename)
	filePath := filepath.Join(s.uploadDir, filename)

	// Ensure upload directory exists
	if err := os.MkdirAll(s.uploadDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %w", err)
	}

	// Save file to disk
	if err := s.saveFile(file, filePath); err != nil {
		return nil, fmt.Errorf("failed to save file: %w", err)
	}

	// Create document record
	document := &models.Document{
		Title:        title,
		Filename:     filename,
		OriginalName: file.Filename,
		FilePath:     filePath,
		FileSize:     file.Size,
		MimeType:     "application/pdf",
		UploadedBy:   userID,
		IsPublic:     isPublic,
	}

	if title == "" {
		document.Title = file.Filename
	}

	// Save to database
	if err := s.db.Create(document).Error; err != nil {
		// Clean up file if database save fails
		os.Remove(filePath)
		return nil, fmt.Errorf("failed to save document to database: %w", err)
	}

	return document, nil
}

// GetDocuments retrieves documents with filters and pagination
func (s *DocumentService) GetDocuments(filter *models.DocumentFilter, userID uint, isAdmin bool) ([]*models.Document, int64, error) {
	query := s.db.Model(&models.Document{}).Preload("Uploader")

	// Apply access control
	if !isAdmin {
		query = query.Where("uploaded_by = ? OR is_public = ?", userID, true)
	}

	// Apply filters
	if filter.Title != "" {
		query = query.Where("title LIKE ?", "%"+filter.Title+"%")
	}
	if filter.Filename != "" {
		query = query.Where("filename LIKE ?", "%"+filter.Filename+"%")
	}
	if filter.UploadedBy != nil {
		query = query.Where("uploaded_by = ?", *filter.UploadedBy)
	}
	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}
	if filter.FileSizeMin != nil {
		query = query.Where("file_size >= ?", *filter.FileSizeMin)
	}
	if filter.FileSizeMax != nil {
		query = query.Where("file_size <= ?", *filter.FileSizeMax)
	}
	if filter.CreatedAfter != nil {
		query = query.Where("created_at >= ?", *filter.CreatedAfter)
	}
	if filter.CreatedBefore != nil {
		query = query.Where("created_at <= ?", *filter.CreatedBefore)
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply ordering
	orderBy := "created_at"
	if filter.OrderBy != "" {
		orderBy = filter.OrderBy
	}
	order := "DESC"
	if filter.Order != "" {
		order = strings.ToUpper(filter.Order)
	}
	query = query.Order(fmt.Sprintf("%s %s", orderBy, order))

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	var documents []*models.Document
	if err := query.Find(&documents).Error; err != nil {
		return nil, 0, err
	}

	return documents, total, nil
}

// GetDocumentByID retrieves a document by ID
func (s *DocumentService) GetDocumentByID(id uint, userID uint, isAdmin bool) (*models.Document, error) {
	var document models.Document
	if err := s.db.Preload("Uploader").First(&document, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("document not found")
		}
		return nil, err
	}

	// Check access permissions
	if !document.CanAccess(userID, isAdmin) {
		return nil, errors.New("access denied")
	}

	return &document, nil
}

// UpdateDocument updates a document's metadata
func (s *DocumentService) UpdateDocument(id uint, title string, isPublic *bool, userID uint, isAdmin bool) (*models.Document, error) {
	document, err := s.GetDocumentByID(id, userID, isAdmin)
	if err != nil {
		return nil, err
	}

	// Check modify permissions
	if !document.CanModify(userID, isAdmin) {
		return nil, errors.New("permission denied")
	}

	// Update fields
	updates := make(map[string]interface{})
	if title != "" {
		updates["title"] = title
	}
	if isPublic != nil {
		updates["is_public"] = *isPublic
	}

	if len(updates) > 0 {
		if err := s.db.Model(document).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	return document, nil
}

// DeleteDocument deletes a document
func (s *DocumentService) DeleteDocument(id uint, userID uint, isAdmin bool) error {
	document, err := s.GetDocumentByID(id, userID, isAdmin)
	if err != nil {
		return err
	}

	// Check modify permissions
	if !document.CanModify(userID, isAdmin) {
		return errors.New("permission denied")
	}

	// Delete from database (soft delete)
	if err := s.db.Delete(document).Error; err != nil {
		return err
	}

	// Delete physical file
	if err := os.Remove(document.FilePath); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Warning: failed to delete file %s: %v\n", document.FilePath, err)
	}

	return nil
}

// GetDocumentFile returns the file path for a document
func (s *DocumentService) GetDocumentFile(id uint, userID uint, isAdmin bool) (string, error) {
	document, err := s.GetDocumentByID(id, userID, isAdmin)
	if err != nil {
		return "", err
	}

	// Check if file exists
	if _, err := os.Stat(document.FilePath); os.IsNotExist(err) {
		return "", errors.New("file not found on disk")
	}

	return document.FilePath, nil
}

// GetRecentDocuments returns recently uploaded documents
func (s *DocumentService) GetRecentDocuments(limit int, userID uint, isAdmin bool) ([]*models.Document, error) {
	query := s.db.Model(&models.Document{}).Preload("Uploader")

	// Apply access control
	if !isAdmin {
		query = query.Where("uploaded_by = ? OR is_public = ?", userID, true)
	}

	var documents []*models.Document
	if err := query.Order("created_at DESC").Limit(limit).Find(&documents).Error; err != nil {
		return nil, err
	}

	return documents, nil
}

// GetDocumentStats returns document statistics
func (s *DocumentService) GetDocumentStats(userID uint, isAdmin bool) (*models.DocumentStats, error) {
	stats := &models.DocumentStats{}

	query := s.db.Model(&models.Document{})

	// Apply access control for non-admin users
	if !isAdmin {
		query = query.Where("uploaded_by = ? OR is_public = ?", userID, true)
	}

	// Total documents
	if err := query.Count(&stats.TotalDocuments).Error; err != nil {
		return nil, err
	}

	// Public documents
	publicQuery := s.db.Model(&models.Document{}).Where("is_public = ?", true)
	if err := publicQuery.Count(&stats.PublicDocuments).Error; err != nil {
		return nil, err
	}

	// Private documents
	stats.PrivateDocuments = stats.TotalDocuments - stats.PublicDocuments

	// Total size
	var totalSize sql.NullInt64
	if err := query.Select("COALESCE(SUM(file_size), 0)").Scan(&totalSize).Error; err != nil {
		return nil, err
	}
	stats.TotalSize = totalSize.Int64

	// Recent uploads (last 7 days)
	recentQuery := query.Where("created_at >= ?", time.Now().AddDate(0, 0, -7))
	if err := recentQuery.Count(&stats.RecentUploads).Error; err != nil {
		return nil, err
	}

	// For now, set recent views to 0 (would need view tracking)
	stats.RecentViews = 0

	return stats, nil
}

// UpdateDocumentMetadata updates document metadata fields (for PDF extraction)
func (s *DocumentService) UpdateDocumentMetadata(id uint, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return nil
	}

	// Update document metadata without permission checks (internal use)
	if err := s.db.Model(&models.Document{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update document metadata: %w", err)
	}

	return nil
}

// SearchDocuments searches documents by query
func (s *DocumentService) SearchDocuments(req *models.DocumentSearchRequest, userID uint, isAdmin bool) ([]*models.DocumentSearchResult, error) {
	query := s.db.Model(&models.Document{}).Preload("Uploader")

	// Apply access control
	if !isAdmin {
		query = query.Where("uploaded_by = ? OR is_public = ?", userID, true)
	}

	// Simple text search in title, filename, author, subject, keywords
	searchTerm := "%" + req.Query + "%"
	query = query.Where(
		"title LIKE ? OR filename LIKE ? OR original_name LIKE ? OR author LIKE ? OR subject LIKE ? OR keywords LIKE ?",
		searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm,
	)

	// Apply pagination
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	// Order by relevance (created_at for now)
	query = query.Order("created_at DESC")

	var documents []*models.Document
	if err := query.Find(&documents).Error; err != nil {
		return nil, err
	}

	// Convert to search results
	results := make([]*models.DocumentSearchResult, len(documents))
	for i, doc := range documents {
		results[i] = &models.DocumentSearchResult{
			Document: doc,
			Score:    1.0,                    // Simple scoring for now
			Matches:  []models.SearchMatch{}, // TODO: Implement text matching
		}
	}

	return results, nil
}

// Helper methods

func (s *DocumentService) validateFile(file *multipart.FileHeader) error {
	// Check file size
	if file.Size > s.maxFileSize {
		return fmt.Errorf("file size exceeds maximum allowed size of %d bytes", s.maxFileSize)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if ext != ".pdf" {
		return errors.New("only PDF files are allowed")
	}

	return nil
}

func (s *DocumentService) generateFilename(originalName string) string {
	ext := filepath.Ext(originalName)
	return fmt.Sprintf("%s%s", uuid.New().String(), ext)
}

func (s *DocumentService) saveFile(file *multipart.FileHeader, filePath string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	dst, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer dst.Close()

	_, err = io.Copy(dst, src)
	return err
}
