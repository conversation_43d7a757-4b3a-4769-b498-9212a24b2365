package handlers

import (
	"net/http"
	"path/filepath"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/penwyp/hydra/internal/models"
	"github.com/penwyp/hydra/internal/services"
	"github.com/penwyp/hydra/internal/utils"
)

type DocumentHandler struct {
	documentService *services.DocumentService
}

func NewDocumentHandler(documentService *services.DocumentService) *DocumentHandler {
	return &DocumentHandler{
		documentService: documentService,
	}
}

// UploadDocument handles PDF file upload
func (h *DocumentHandler) UploadDocument(c *gin.Context) {
	// Get current user
	userID, isAdmin := utils.GetUserFromContext(c)

	// Get form data
	title := c.PostForm("title")
	isPublicStr := c.PostForm("is_public")
	isPublic := isPublicStr == "true"

	// Get uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "No file uploaded", err)
		return
	}

	// Create document
	document, err := h.documentService.CreateDocument(file, title, isPublic, userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Failed to upload document", err)
		return
	}

	utils.SuccessResponse(c, "Document uploaded successfully", document)
}

// GetDocuments handles getting document list with filters
func (h *DocumentHandler) GetDocuments(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse query parameters
	var filter models.DocumentFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid query parameters", err)
		return
	}

	// Set default pagination
	if filter.Limit <= 0 {
		filter.Limit = 20
	}
	if filter.Limit > 100 {
		filter.Limit = 100
	}

	// Get documents
	documents, total, err := h.documentService.GetDocuments(&filter, userID, isAdmin)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get documents", err)
		return
	}

	// Prepare paginated response
	response := map[string]interface{}{
		"data": documents,
		"meta": map[string]interface{}{
			"total":  total,
			"limit":  filter.Limit,
			"offset": filter.Offset,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetDocument handles getting a single document
func (h *DocumentHandler) GetDocument(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse document ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid document ID", err)
		return
	}

	// Get document
	document, err := h.documentService.GetDocumentByID(uint(id), userID, isAdmin)
	if err != nil {
		if err.Error() == "document not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Document not found", err)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get document", err)
		return
	}

	utils.SuccessResponse(c, "Document retrieved successfully", document)
}

// UpdateDocument handles updating document metadata
func (h *DocumentHandler) UpdateDocument(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse document ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid document ID", err)
		return
	}

	// Parse request body
	var req struct {
		Title    string `json:"title"`
		IsPublic *bool  `json:"is_public"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Update document
	document, err := h.documentService.UpdateDocument(uint(id), req.Title, req.IsPublic, userID, isAdmin)
	if err != nil {
		if err.Error() == "document not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Document not found", err)
			return
		}
		if err.Error() == "permission denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Permission denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update document", err)
		return
	}

	utils.SuccessResponse(c, "Document updated successfully", document)
}

// DeleteDocument handles document deletion
func (h *DocumentHandler) DeleteDocument(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse document ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid document ID", err)
		return
	}

	// Delete document
	err = h.documentService.DeleteDocument(uint(id), userID, isAdmin)
	if err != nil {
		if err.Error() == "document not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Document not found", err)
			return
		}
		if err.Error() == "permission denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Permission denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete document", err)
		return
	}

	utils.SuccessResponse(c, "Document deleted successfully", nil)
}

// GetDocumentFile handles serving PDF file content
func (h *DocumentHandler) GetDocumentFile(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse document ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid document ID", err)
		return
	}

	// Get file path
	filePath, err := h.documentService.GetDocumentFile(uint(id), userID, isAdmin)
	if err != nil {
		if err.Error() == "document not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Document not found", err)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		if err.Error() == "file not found on disk" {
			utils.ErrorResponse(c, http.StatusNotFound, "File not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get document file", err)
		return
	}

	// Set appropriate headers
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "inline")

	// Serve file
	c.File(filePath)
}

// DownloadDocument handles PDF file download
func (h *DocumentHandler) DownloadDocument(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse document ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid document ID", err)
		return
	}

	// Get document info
	document, err := h.documentService.GetDocumentByID(uint(id), userID, isAdmin)
	if err != nil {
		if err.Error() == "document not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Document not found", err)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get document", err)
		return
	}

	// Set download headers
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=\""+document.OriginalName+"\"")

	// Serve file
	c.File(document.FilePath)
}

// GetRecentDocuments handles getting recently uploaded documents
func (h *DocumentHandler) GetRecentDocuments(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	// Get recent documents
	documents, err := h.documentService.GetRecentDocuments(limit, userID, isAdmin)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get recent documents", err)
		return
	}

	utils.SuccessResponse(c, "Recent documents retrieved successfully", documents)
}

// GetDocumentStats handles getting document statistics
func (h *DocumentHandler) GetDocumentStats(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Get document statistics
	stats, err := h.documentService.GetDocumentStats(userID, isAdmin)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get document statistics", err)
		return
	}

	utils.SuccessResponse(c, "Document statistics retrieved successfully", stats)
}

// SearchDocuments handles document search (placeholder for now)
func (h *DocumentHandler) SearchDocuments(c *gin.Context) {
	// Parse request body
	var req models.DocumentSearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// For now, return empty results
	// TODO: Implement actual search functionality
	results := []models.DocumentSearchResult{}

	utils.SuccessResponse(c, "Search completed", results)
}

// GetDocumentMetadata handles getting PDF metadata (placeholder for now)
func (h *DocumentHandler) GetDocumentMetadata(c *gin.Context) {
	userID, isAdmin := utils.GetUserFromContext(c)

	// Parse document ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid document ID", err)
		return
	}

	// Get document to check access
	document, err := h.documentService.GetDocumentByID(uint(id), userID, isAdmin)
	if err != nil {
		if err.Error() == "document not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Document not found", err)
			return
		}
		if err.Error() == "access denied" {
			utils.ErrorResponse(c, http.StatusForbidden, "Access denied", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get document", err)
		return
	}

	// For now, return basic metadata from document record
	metadata := map[string]interface{}{
		"title":             document.Title,
		"author":            document.Author,
		"subject":           document.Subject,
		"keywords":          document.Keywords,
		"creator":           document.Creator,
		"producer":          document.Producer,
		"creation_date":     document.CreationDate,
		"modification_date": document.ModificationDate,
		"page_count":        document.PageCount,
	}

	utils.SuccessResponse(c, "Document metadata retrieved successfully", metadata)
}
