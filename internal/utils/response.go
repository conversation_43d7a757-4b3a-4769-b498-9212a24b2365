package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SuccessResponse sends a successful JSON response
func SuccessResponse(c *gin.Context, message string, data interface{}) {
	response := gin.H{
		"message": message,
	}

	if data != nil {
		response["data"] = data
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, response)
}

// ErrorResponse sends an error JSON response
func ErrorResponse(c *gin.Context, statusCode int, message string, err error) {
	response := gin.H{
		"error": message,
	}

	if err != nil {
		response["details"] = err.Error()
	}

	c.<PERSON>(statusCode, response)
}

// GetUserFromContext extracts user information from the Gin context
// Returns userID and isAdmin status
func GetUserFromContext(c *gin.Context) (uint, bool) {
	// For now, return mock values since JWT is not implemented
	// TODO: Extract actual user info from JW<PERSON> token in context

	// Check if there's a user in context (set by auth middleware)
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(uint); ok {
			isAdmin, _ := c.Get("is_admin")
			admin, _ := isAdmin.(bool)
			return uid, admin
		}
	}

	// Default to admin user for development
	return 1, true
}
