package models

import (
	"time"

	"gorm.io/gorm"
)

// Document represents a PDF document in the system
type Document struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	Title            string         `json:"title" gorm:"size:255"`
	Filename         string         `json:"filename" gorm:"size:255;not null"`
	OriginalName     string         `json:"original_name" gorm:"size:255;not null"`
	FilePath         string         `json:"file_path" gorm:"size:500;not null"`
	FileSize         int64          `json:"file_size" gorm:"not null"`
	MimeType         string         `json:"mime_type" gorm:"size:100;default:'application/pdf'"`
	PageCount        *int           `json:"page_count"`
	Author           string         `json:"author" gorm:"size:255"`
	Subject          string         `json:"subject" gorm:"size:500"`
	Keywords         string         `json:"keywords" gorm:"size:500"`
	Creator          string         `json:"creator" gorm:"size:255"`
	Producer         string         `json:"producer" gorm:"size:255"`
	CreationDate     *time.Time     `json:"creation_date"`
	ModificationDate *time.Time     `json:"modification_date"`
	UploadedBy       uint           `json:"uploaded_by" gorm:"not null"`
	IsPublic         bool           `json:"is_public" gorm:"default:false"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Uploader *User `json:"uploader,omitempty" gorm:"foreignKey:UploadedBy"`
}

// DocumentFilter represents filters for document queries
type DocumentFilter struct {
	Title         string     `json:"title" form:"title"`
	Filename      string     `json:"filename" form:"filename"`
	UploadedBy    *uint      `json:"uploaded_by" form:"uploaded_by"`
	IsPublic      *bool      `json:"is_public" form:"is_public"`
	FileSizeMin   *int64     `json:"file_size_min" form:"file_size_min"`
	FileSizeMax   *int64     `json:"file_size_max" form:"file_size_max"`
	CreatedAfter  *time.Time `json:"created_after" form:"created_after"`
	CreatedBefore *time.Time `json:"created_before" form:"created_before"`
	Limit         int        `json:"limit" form:"limit"`
	Offset        int        `json:"offset" form:"offset"`
	OrderBy       string     `json:"order_by" form:"order_by"`
	Order         string     `json:"order" form:"order"`
}

// DocumentStats represents document statistics
type DocumentStats struct {
	TotalDocuments   int64 `json:"total_documents"`
	PublicDocuments  int64 `json:"public_documents"`
	PrivateDocuments int64 `json:"private_documents"`
	TotalSize        int64 `json:"total_size"`
	RecentUploads    int64 `json:"recent_uploads"`
	RecentViews      int64 `json:"recent_views"`
}

// DocumentSearchRequest represents a search request
type DocumentSearchRequest struct {
	Query  string `json:"query" binding:"required"`
	Limit  int    `json:"limit"`
	Offset int    `json:"offset"`
}

// DocumentSearchResult represents a search result
type DocumentSearchResult struct {
	Document *Document     `json:"document"`
	Score    float64       `json:"score"`
	Matches  []SearchMatch `json:"matches"`
}

// SearchMatch represents a text match in search results
type SearchMatch struct {
	Page      int    `json:"page"`
	Text      string `json:"text"`
	Highlight string `json:"highlight"`
}

// TableName returns the table name for Document model
func (Document) TableName() string {
	return "documents"
}

// BeforeCreate sets default values before creating a document
func (d *Document) BeforeCreate(tx *gorm.DB) error {
	if d.Title == "" {
		d.Title = d.OriginalName
	}
	return nil
}

// CanAccess checks if a user can access this document
func (d *Document) CanAccess(userID uint, isAdmin bool) bool {
	// Admin can access all documents
	if isAdmin {
		return true
	}
	// Owner can access their own documents
	if d.UploadedBy == userID {
		return true
	}
	// Anyone can access public documents
	return d.IsPublic
}

// CanModify checks if a user can modify this document
func (d *Document) CanModify(userID uint, isAdmin bool) bool {
	// Admin can modify all documents
	if isAdmin {
		return true
	}
	// Only owner can modify their documents
	return d.UploadedBy == userID
}
